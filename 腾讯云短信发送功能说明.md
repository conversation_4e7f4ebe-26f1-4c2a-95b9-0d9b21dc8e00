# 腾讯云短信发送功能说明

## 功能概述

在 `ExternalServiceImpl.java` 中新增了腾讯云短信发送功能，使用 `tencentcloud-sdk-java-sms` SDK 实现短信发送服务。

## 新增内容

### 1. 依赖导入
```java
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.SendSmsRequest;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
```

### 2. 配置属性
```java
// 短信相关配置
@Value("${TENCENT.SMS.SDK_APP_ID:1400009099}")
private String SMS_SDK_APP_ID;
@Value("${TENCENT.SMS.SIGN_NAME:腾讯云}")
private String SMS_SIGN_NAME;
@Value("${TENCENT.SMS.TEMPLATE_ID:449739}")
private String SMS_TEMPLATE_ID;
```

### 3. 新增方法

#### 3.1 基础发送方法
```java
public SendSmsResponse sendSms(String[] phoneNumbers, String[] templateParams)
```
- **功能**: 使用默认模板发送短信
- **参数**: 
  - `phoneNumbers`: 手机号数组，格式如 `+8613711112222`
  - `templateParams`: 模板参数数组
- **返回**: `SendSmsResponse` 发送结果

#### 3.2 指定模板发送方法
```java
public SendSmsResponse sendSms(String[] phoneNumbers, String[] templateParams, String templateId)
```
- **功能**: 使用指定模板发送短信
- **参数**: 
  - `phoneNumbers`: 手机号数组
  - `templateParams`: 模板参数数组
  - `templateId`: 模板ID
- **返回**: `SendSmsResponse` 发送结果

#### 3.3 验证码发送方法
```java
public SendSmsResponse sendVerificationCodeSms(String phoneNumber, String verificationCode)
```
- **功能**: 发送验证码短信（单个手机号）
- **参数**: 
  - `phoneNumber`: 手机号，格式如 `+8613711112222`
  - `verificationCode`: 验证码
- **返回**: `SendSmsResponse` 发送结果

## 配置说明

### application.yml 配置
```yaml
TENCENT:
  SECRET_ID: your_secret_id
  SECRET_KEY: your_secret_key
  REGION: your_region
  SMS:
    SDK_APP_ID: 1400009099        # 短信应用ID
    SIGN_NAME: 腾讯云              # 短信签名
    TEMPLATE_ID: 449739           # 默认模板ID
```

### 环境变量配置（推荐）
```bash
export TENCENTCLOUD_SECRET_ID=your_secret_id
export TENCENTCLOUD_SECRET_KEY=your_secret_key
```

## 使用示例

### 1. 发送验证码短信
```java
@Autowired
private ExternalService externalService;

public void sendVerificationCode(String phoneNumber) {
    String verificationCode = "123456";
    try {
        SendSmsResponse response = externalService.sendVerificationCodeSms("+8613711112222", verificationCode);
        log.info("短信发送成功: {}", response.getRequestId());
    } catch (BusinessException e) {
        log.error("短信发送失败: {}", e.getMessage());
    }
}
```

### 2. 批量发送短信
```java
public void sendBatchSms() {
    String[] phoneNumbers = {"+8613711112222", "+8613711112223", "+8613711112224"};
    String[] templateParams = {"1234"};
    
    try {
        SendSmsResponse response = externalService.sendSms(phoneNumbers, templateParams);
        log.info("批量短信发送成功: {}", response.getRequestId());
    } catch (BusinessException e) {
        log.error("批量短信发送失败: {}", e.getMessage());
    }
}
```

### 3. 使用自定义模板发送短信
```java
public void sendCustomTemplateSms() {
    String[] phoneNumbers = {"+8613711112222"};
    String[] templateParams = {"张三", "2024-01-01", "订单123456"};
    String customTemplateId = "123456";
    
    try {
        SendSmsResponse response = externalService.sendSms(phoneNumbers, templateParams, customTemplateId);
        log.info("自定义模板短信发送成功: {}", response.getRequestId());
    } catch (BusinessException e) {
        log.error("自定义模板短信发送失败: {}", e.getMessage());
    }
}
```

## 手机号格式说明

### 国内手机号
- 格式: `+86` + 手机号
- 示例: `+8613711112222`

### 国际手机号
- 格式: `+[国家码]` + 手机号
- 示例: 
  - 美国: `+1234567890`
  - 英国: `+441234567890`

## 模板参数说明

### 模板示例
```
您的验证码是{1}，请在5分钟内完成验证。
```

### 对应参数
```java
String[] templateParams = {"123456"};
```

### 多参数模板示例
```
尊敬的{1}，您的订单{2}已于{3}发货，请注意查收。
```

### 对应参数
```java
String[] templateParams = {"张三", "ORD123456", "2024-01-01"};
```

## 错误处理

### 常见错误码
- `FailedOperation.SignatureIncorrectOrUnapproved`: 签名错误或未审核通过
- `FailedOperation.TemplateIncorrectOrUnapproved`: 模板错误或未审核通过
- `UnauthorizedOperation.SmsSdkAppIdVerifyFail`: SDK应用ID验证失败
- `UnsupportedOperation.ContainDomesticAndInternationalPhoneNumber`: 包含国内外混合手机号

### 异常处理
所有短信发送异常都会被捕获并转换为 `BusinessException`，包含具体的错误信息。

## 注意事项

1. **密钥安全**: 建议使用环境变量或配置文件管理密钥，避免硬编码
2. **签名审核**: 短信签名必须在腾讯云控制台审核通过后才能使用
3. **模板审核**: 短信模板必须在腾讯云控制台审核通过后才能使用
4. **手机号格式**: 必须使用E.164标准格式，包含国家码
5. **发送频率**: 注意控制发送频率，避免被限流
6. **费用控制**: 短信发送会产生费用，建议设置合理的发送策略

## 监控和日志

- 所有短信发送结果都会记录到日志中
- 可以通过 `response.getRequestId()` 获取请求ID用于问题追踪
- 建议在生产环境中添加短信发送监控和告警
