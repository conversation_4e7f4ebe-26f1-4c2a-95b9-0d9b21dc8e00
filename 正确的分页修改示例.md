# 正确的分页修改示例

## 修改概述

我已经按照正确的顺序完成了 AdminController 中两个方法的分页修改，展示了正确的修改流程：

**修改顺序：Service 接口 → Service 实现 → Controller**

## 已完成的修改

### 1. queryByStatusList 方法

#### Service 接口修改 (IServiceContentService.java)
```java
// 添加了新的分页方法
Page<ServiceContent> queryByStatusListPage(int status, PageRequest pageRequest);
```

#### Service 实现修改 (ServiceContentServiceImpl.java)
```java
@Override
public Page<ServiceContent> queryByStatusListPage(int status, PageRequest pageRequest) {
    if (pageRequest == null) {
        pageRequest = new PageRequest();
    }
    pageRequest.validate();
    
    Page<ServiceContent> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
    Page<ServiceContent> result = this.lambdaQuery()
            .eq(ServiceContent::getServiceContentStatus, status)
            .orderByDesc(ServiceContent::getCreateTime)
            .page(page);
    
    // 处理图片路径
    List<ServiceContent> processedRecords = new ArrayList<>();
    for (ServiceContent serviceContent : result.getRecords()) {
        serviceContent.setPetUrls(getImagePath(serviceContent.getPetUrls()));
        serviceContent.setDoorUrls(getImagePath(serviceContent.getDoorUrls()));
        serviceContent.setRoomUrls(getImagePath(serviceContent.getRoomUrls()));
        serviceContent.setEatUrls(getImagePath(serviceContent.getEatUrls()));
        processedRecords.add(serviceContent);
    }
    result.setRecords(processedRecords);
    
    return result;
}
```

#### Controller 修改 (AdminController.java)
```java
@PostMapping("/list/queryByStatusList/{status}")
@ApiOperation(value = "获取待审核服务内容", notes = "分页获取待审核服务内容,接口传1")
public ApiResponse<Page<ServiceContent>> queryByStatusList(@PathVariable int status, @RequestBody @ApiParam(value = "分页参数") PageRequest pageRequest) {
    return ApiResponse.ok(service.queryByStatusListPage(status, pageRequest));
}
```

### 2. getServiceContent 方法

#### Service 接口修改 (IServiceContentService.java)
```java
// 添加了新的分页方法
Page<Map.Entry<String, List<ServiceContent>>> queryByStatusPage(int status, PageRequest pageRequest);
```

#### Service 实现修改 (ServiceContentServiceImpl.java)
```java
@Override
public Page<Map.Entry<String, List<ServiceContent>>> queryByStatusPage(int status, PageRequest pageRequest) {
    if (pageRequest == null) {
        pageRequest = new PageRequest();
    }
    pageRequest.validate();
    
    // 先获取所有数据并分组
    Map<String, List<ServiceContent>> groupedData = this.lambdaQuery()
            .eq(ServiceContent::getServiceContentStatus, status)
            .orderByDesc(ServiceContent::getCreateTime)
            .list()
            .stream()
            .collect(Collectors.groupingBy(ServiceContent::getOrderNo));
    
    // 将 Map 转换为 List 以便分页
    List<Map.Entry<String, List<ServiceContent>>> entryList = new ArrayList<>(groupedData.entrySet());
    
    // 手动分页
    Page<Map.Entry<String, List<ServiceContent>>> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
    page.setTotal(entryList.size());
    
    int start = (pageRequest.getCurrent() - 1) * pageRequest.getSize();
    int end = Math.min(start + pageRequest.getSize(), entryList.size());
    
    if (start < entryList.size()) {
        page.setRecords(entryList.subList(start, end));
    }
    
    return page;
}
```

#### Controller 修改 (AdminController.java)
```java
@PostMapping("/list/serviceContent/{status}")
@ApiOperation(value = "获取待审核服务内容", notes = "分页获取待审核服务内容,接口传1")
public ApiResponse<Page<Map.Entry<String, List<ServiceContent>>>> getServiceContent(@PathVariable int status, @RequestBody @ApiParam(value = "分页参数") PageRequest pageRequest) {
    return ApiResponse.ok(service.queryByStatusPage(status, pageRequest));
}
```

## 接口变更说明

### 1. queryByStatusList
- **原接口：** `GET /api/admin/list/queryByStatusList/{status}`
- **新接口：** `POST /api/admin/list/queryByStatusList/{status}`
- **返回类型：** `ApiResponse<Page<ServiceContent>>`

### 2. getServiceContent
- **原接口：** `GET /api/admin/list/serviceContent/{status}`
- **新接口：** `POST /api/admin/list/serviceContent/{status}`
- **返回类型：** `ApiResponse<Page<Map.Entry<String, List<ServiceContent>>>>`

## 请求示例

```javascript
// 新的请求方式
POST /api/admin/list/queryByStatusList/1
Content-Type: application/json

{
  "current": 1,
  "size": 10,
  "orderBy": "createTime",
  "orderDirection": "desc"
}
```

## 响应示例

```json
{
  "code": 0,
  "errorMsg": "",
  "data": {
    "records": [...],      // 数据列表
    "total": 100,          // 总记录数
    "size": 10,            // 每页大小
    "current": 1,          // 当前页
    "pages": 10            // 总页数
  }
}
```

## 修改要点

1. **保留原方法**：原有的非分页方法仍然保留，新增分页版本
2. **正确的修改顺序**：Service 接口 → Service 实现 → Controller
3. **类型安全**：Controller 返回类型明确指定泛型
4. **业务逻辑保持**：分页版本保持了原有的业务逻辑（如图片路径处理）
5. **参数验证**：在 Service 层进行分页参数验证

## 下一步

可以按照这个模式继续修改其他 Controller 中的查询方法。每个方法都需要：

1. 在对应的 Service 接口中添加分页版本方法
2. 在 Service 实现中实现分页逻辑
3. 在 Controller 中调用新的分页方法并更新返回类型
