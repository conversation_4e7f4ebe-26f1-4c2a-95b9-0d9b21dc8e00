# 微信小程序登录接口文档

## 概述

本文档描述了新增的微信小程序登录接口，该接口使用微信小程序的 `wx.login()` 获取的 `code` 来实现用户登录。

## 接口信息

### 接口地址
```
POST /api/WxUsers/miniProgramLogin
```

### 请求参数

#### 请求体 (JSON)
```json
{
  "code": "string",           // 必填：微信小程序登录凭证
  "nickname": "string",       // 可选：用户昵称
  "avatarUrl": "string",      // 可选：用户头像URL
  "mobile": "string",         // 可选：手机号
  "password": "string",       // 可选：密码
  "inviteCode": "string"      // 可选：邀请码
}
```

#### 参数说明
- `code`: 通过微信小程序 `wx.login()` 获取的登录凭证，必填
- `nickname`: 用户昵称，可选，如果不提供会自动生成
- `avatarUrl`: 用户头像URL，可选，如果不提供会使用默认头像
- `mobile`: 手机号，可选
- `password`: 密码，可选，如果提供会进行MD5加密存储
- `inviteCode`: 邀请码，可选

### 响应格式

#### 成功响应
```json
{
  "code": 0,
  "errorMsg": "",
  "data": {
    "id": 123,
    "openid": "wx_openid_xxx",
    "nickname": "用户昵称",
    "customUrl": "头像URL",
    "mobile": "手机号",
    "userPassword": "",        // 密码字段已清空
    "inviteCode": "邀请码",
    "wasInviteCode": "被邀请码",
    "createTime": 1640995200000,
    "updateTime": 1640995200000,
    // ... 其他用户字段
  }
}
```

#### 错误响应
```json
{
  "code": 10001,
  "errorMsg": "登录失败: 具体错误信息",
  "data": null
}
```

## 使用示例

### 微信小程序端代码
```javascript
// 1. 获取登录凭证
wx.login({
  success: function(res) {
    if (res.code) {
      // 2. 调用后端登录接口
      wx.request({
        url: 'https://your-domain.com/api/WxUsers/miniProgramLogin',
        method: 'POST',
        data: {
          code: res.code,
          nickname: '用户昵称',
          avatarUrl: '头像URL'
        },
        success: function(response) {
          if (response.data.code === 0) {
            // 登录成功
            const userInfo = response.data.data;
            console.log('登录成功', userInfo);
            
            // 保存用户信息到本地存储
            wx.setStorageSync('userInfo', userInfo);
          } else {
            // 登录失败
            console.error('登录失败', response.data.errorMsg);
            wx.showToast({
              title: response.data.errorMsg,
              icon: 'none'
            });
          }
        },
        fail: function(error) {
          console.error('请求失败', error);
          wx.showToast({
            title: '网络请求失败',
            icon: 'none'
          });
        }
      });
    } else {
      console.error('获取登录凭证失败', res.errMsg);
    }
  }
});
```

### cURL 测试示例
```bash
curl -X POST "http://localhost:8080/api/WxUsers/miniProgramLogin" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "your_wx_login_code",
    "nickname": "测试用户",
    "avatarUrl": "https://example.com/avatar.jpg"
  }'
```

## 业务逻辑说明

1. **验证登录凭证**: 使用微信小程序的 `code` 调用微信API获取 `openid` 和 `session_key`
2. **用户查询**: 根据 `openid` 查询数据库中是否存在该用户
3. **新用户创建**: 如果用户不存在，创建新用户记录
4. **用户信息更新**: 如果用户已存在，更新用户的登录时间和相关信息
5. **返回结果**: 返回用户信息（密码字段已清空）

## 注意事项

1. **安全性**: 密码会进行MD5加密存储，返回时会清空密码字段
2. **openid唯一性**: 每个微信用户的openid是唯一的，用作用户标识
3. **邀请码**: 新用户的邀请码默认使用openid
4. **头像默认值**: 如果不提供头像URL，会使用系统默认头像
5. **昵称生成**: 如果不提供昵称，会自动生成唯一昵称

## 错误码说明

- `0`: 成功
- `10001`: 登录失败（通用登录错误）
- 其他错误码请参考系统错误码文档

## 配置要求

确保在 `application.yml` 中正确配置了微信小程序的 `appid` 和 `secret`:

```yaml
wx:
  appid: your_miniprogram_appid
  secret: your_miniprogram_secret
```
