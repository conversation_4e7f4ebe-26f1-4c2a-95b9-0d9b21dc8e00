package com.tencent.wxcloudrun.service.impl;

import com.tencent.wxcloudrun.dto.TransferBillsDTO;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.cipher.PrivacyDecryptor;
import com.wechat.pay.java.core.cipher.PrivacyEncryptor;
import com.wechat.pay.java.core.http.*;
import com.wechat.pay.java.core.util.GsonUtil;
import com.wechat.pay.java.service.transferbatch.model.*;

import java.util.Objects;

public class WxMchTransferService {

    private final HttpClient httpClient;
    private final HostName hostName;
    private final PrivacyEncryptor encryptor;
    private final PrivacyDecryptor decryptor;

    private WxMchTransferService(HttpClient httpClient, HostName hostName, PrivacyEncryptor encryptor, PrivacyDecryptor decryptor) {
        this.httpClient = (HttpClient) Objects.requireNonNull(httpClient);
        this.hostName = hostName;
        this.encryptor = (PrivacyEncryptor) Objects.requireNonNull(encryptor);
        this.decryptor = (PrivacyDecryptor) Objects.requireNonNull(decryptor);
    }

    public TransferBillsDTO.TransferBillsResult initiateTransferBills(TransferBillsDTO.TransferBillsRequest request) {
        String requestPath = "https://api.mch.weixin.qq.com/v3/fund-app/mch-transfer/transfer-bills";
        PrivacyEncryptor var10001 = this.encryptor;
        Objects.requireNonNull(var10001);
        TransferBillsDTO.TransferBillsRequest realRequest = request.cloneWithCipher(var10001::encrypt);
        if (this.hostName != null) {
            requestPath = requestPath.replaceFirst(HostName.API.getValue(), this.hostName.getValue());
        }

        HttpHeaders headers = new HttpHeaders();
        headers.addHeader("Accept", MediaType.APPLICATION_JSON.getValue());
        headers.addHeader("Content-Type", MediaType.APPLICATION_JSON.getValue());
        headers.addHeader("Wechatpay-Serial", this.encryptor.getWechatpaySerial());
        HttpRequest httpRequest = (new HttpRequest.Builder()).httpMethod(HttpMethod.POST).url(requestPath).headers(headers).body(this.createRequestBody(realRequest)).build();
        HttpResponse<TransferBillsDTO.TransferBillsResult> httpResponse = this.httpClient.execute(httpRequest, TransferBillsDTO.TransferBillsResult.class);
        return (TransferBillsDTO.TransferBillsResult)httpResponse.getServiceResponse();
    }


    private RequestBody createRequestBody(Object request) {
        return (new JsonRequestBody.Builder()).body(GsonUtil.toJson(request)).build();
    }

    public static class Builder {
        private HttpClient httpClient;
        private HostName hostName;
        private PrivacyEncryptor encryptor;
        private PrivacyDecryptor decryptor;

        public Builder() {
        }

        public WxMchTransferService.Builder config(Config config) {
            this.httpClient = (new DefaultHttpClientBuilder()).config(config).build();
            this.encryptor = config.createEncryptor();
            this.decryptor = config.createDecryptor();
            return this;
        }

        public WxMchTransferService.Builder hostName(HostName hostName) {
            this.hostName = hostName;
            return this;
        }

        public WxMchTransferService.Builder httpClient(HttpClient httpClient) {
            this.httpClient = httpClient;
            return this;
        }

        public WxMchTransferService.Builder encryptor(PrivacyEncryptor encryptor) {
            this.encryptor = encryptor;
            return this;
        }

        public WxMchTransferService.Builder decryptor(PrivacyDecryptor decryptor) {
            this.decryptor = decryptor;
            return this;
        }

        public WxMchTransferService build() {
            return new WxMchTransferService(this.httpClient, this.hostName, this.encryptor, this.decryptor);
        }
    }

}
