package com.tencent.wxcloudrun.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.tencent.wxcloudrun.config.BusinessException;
import com.tencent.wxcloudrun.config.UserContextHolder;
import com.tencent.wxcloudrun.dto.ExternalDTO;
import com.tencent.wxcloudrun.enums.WalletTypeEnum;
import com.tencent.wxcloudrun.model.OrderServiceContent;
import com.tencent.wxcloudrun.model.Orders;
import com.google.gson.Gson;
import com.tencent.wxcloudrun.model.ServiceContent;
import com.tencent.wxcloudrun.dao.ServiceContentMapper;
import com.tencent.wxcloudrun.service.api.ExternalService;
import com.tencent.wxcloudrun.service.api.IOrdersService;
import com.tencent.wxcloudrun.service.api.IServiceContentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tencent.wxcloudrun.service.api.UserWalletService;
import com.tencent.wxcloudrun.vo.ServiceContentUpdateVO;
import com.wechat.pay.java.core.util.GsonUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.tencent.wxcloudrun.enums.ResponseEnum.*;

import static com.tencent.wxcloudrun.enums.OrderStatusEnum.NOT;
import static com.tencent.wxcloudrun.enums.OrderStatusEnum.REPORT_GENERATED;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
@Slf4j
@Service
public class ServiceContentServiceImpl extends ServiceImpl<ServiceContentMapper, ServiceContent> implements IServiceContentService {

    @Autowired
    private IOrdersService ordersService;

    @Autowired
    private UserWalletService userWalletService;

    @Autowired
    private ExternalService externalService;


    @Override
    public List<ServiceContent> queryByOrderNo(String orderNo, Integer serviceContentStatus) {
        LambdaQueryChainWrapper<ServiceContent> eq = this.lambdaQuery().eq(ServiceContent::getOrderNo, orderNo);
        if (Objects.nonNull(serviceContentStatus)) {
            eq.eq(ServiceContent::getServiceContentStatus, serviceContentStatus);
        }
        return eq.list();
    }

    @Override
    public Map<String, List<ServiceContent>> queryByStatus(int status) {
        return this.lambdaQuery().eq(ServiceContent::getServiceContentStatus, status)
                .list().stream().collect(Collectors.groupingBy(ServiceContent::getOrderNo));
    }

    @Override
    public List<ServiceContent> queryByStatusList(int status) {
        List<ServiceContent> list = this.lambdaQuery().eq(ServiceContent::getServiceContentStatus, status).list();
        List<ServiceContent> serviceContentList = new ArrayList<>();
        for (ServiceContent serviceContent : list) {
            serviceContent.setPetUrls(getImagePath(serviceContent.getPetUrls()));
            serviceContent.setDoorUrls(getImagePath(serviceContent.getDoorUrls()));
            serviceContent.setRoomUrls(getImagePath(serviceContent.getRoomUrls()));
            serviceContent.setEatUrls(getImagePath(serviceContent.getEatUrls()));
            serviceContentList.add(serviceContent);
        }
        return serviceContentList;

    }

    private String getImagePath(String doorUrls) {
        Gson gson = GsonUtil.getGson();

        // 解析 JSON 数组
        List<String> doorUrlsList = new ArrayList<>();
        JsonArray jsonArray = gson.fromJson(doorUrls, JsonArray.class);
        for (int i = 0; i < jsonArray.size(); i++) {
            JsonObject jsonObject = jsonArray.get(i).getAsJsonObject();
            doorUrlsList.add(jsonObject.get("url").getAsString());
        }
        List<String> imagePath = externalService.getImagePath(doorUrlsList).stream()
                .filter(imageObj -> imageObj.getStatus() == 0) // 过滤 status == 0 的元素
                .map(ExternalDTO.ImageObj::getDownload_url) // 提取 download_url
                .collect(Collectors.toList()); // 收集为 List<String>
        return gson.toJson(imagePath);
    }

    @Override
    public List<ServiceContent> getList(int status) {
        return this.lambdaQuery().eq(ServiceContent::getServiceContentStatus, status).list();
    }

    @Override
    public Map<String, List<ServiceContent>> queryByOrderNoList(List<String> orderNoList, Integer serviceContentStatus) {

        LambdaQueryChainWrapper<ServiceContent> in = this.lambdaQuery().in(ServiceContent::getOrderNo, orderNoList);
        if (Objects.nonNull(serviceContentStatus)) {
            in.eq(ServiceContent::getServiceContentStatus, serviceContentStatus);
        }
        return in.list().stream()
                .collect(Collectors.groupingBy(ServiceContent::getOrderNo));
    }

    @Override
    public boolean updateStatus(ServiceContentUpdateVO vo) {
        List<ServiceContent> serviceContents = queryByOrderNo(vo.getOrderNo(), 1);
        if (CollectionUtils.isEmpty(serviceContents)) {
            throw new BusinessException(SERVICE_CONTENT_NOT_EXIST);
        }
        List<ServiceContent> contents = serviceContents.stream().peek(serviceContent -> {
            serviceContent.setServiceContentStatus(vo.getServiceContentStatus());
            serviceContent.setUpdateTime(System.currentTimeMillis());
        }).collect(Collectors.toList());

        if (vo.getServiceContentStatus() == 2) {
            Orders orders = ordersService.getByOrderNo(vo.getOrderNo());
            orders.setOrdersStatus(REPORT_GENERATED.getStatusCode());
            orders.setFinishTime(System.currentTimeMillis());
            orders.setUpdateTime(System.currentTimeMillis());
            // 更新订单金额冻结状态
            orders.setWithdrawalStatus(WalletTypeEnum.WALLET_FREEZE.getCouponType());
            orders.setAccountedTime(System.currentTimeMillis());
            // 服务报告审核通过后，更新喂养员钱包
            userWalletService.updateFreezeAmountByUserId(orders.getServiceUserId(), orders.getServicePrice());
            if (!StrUtil.isEmpty(orders.getDistributeUserId())) {
                // 更新分销员钱包
                userWalletService.updateFreezeAmountByUserId(orders.getDistributeUserId(), orders.getDistributorPrice());
            }
            ordersService.updateById(orders);
        }
        this.saveOrUpdateBatch(contents);
        return true;
    }

    @Override
    public boolean create(ServiceContent content) {
        ServiceContent dto = new ServiceContent();
        ServiceContent one = this.lambdaQuery().eq(ServiceContent::getOrderNo, content.getOrderNo())
                .eq(ServiceContent::getServiceType, content.getServiceType())
                .eq(ServiceContent::getWorkerId, content.getWorkerId())
                .eq(ServiceContent::getDate, content.getDate()).one();
        Orders orders = ordersService.getByOrderNo(content.getOrderNo());
        if (Objects.isNull(orders) || StrUtil.isEmpty(content.getDate())) {
            log.error("订单服务日期为空, 用户信息:{}", UserContextHolder.getCurrentUserLog());
            throw new BusinessException(ORDER_SERVICE_TIME_FAILED);
        }
        String serviceTime = orders.getServiceTime();
        Gson gson = new Gson();
        List<String> serviceTimeList = gson.fromJson(serviceTime, new TypeToken<List<String>>() {
        }.getType());
        // 判断时间是否存在
        if (serviceTimeList != null && !serviceTimeList.contains(content.getDate())) {
            log.error("订单服务日期为空, 用户信息:{}", UserContextHolder.getCurrentUserLog());
            throw new BusinessException(ORDER_SERVICE_TIME_FAILED);
        }
        BeanUtil.copyProperties(content, dto);
        if (Objects.nonNull(one)) {
            dto.setId(one.getId());
            dto.setCreateTime(one.getCreateTime());
        } else {
            dto.setCreateTime(System.currentTimeMillis());
        }
        dto.setServiceContentStatus(1);
        dto.setUpdateTime(dto.getCreateTime());
        return this.saveOrUpdate(dto);
    }


    @Override
    public boolean update(ServiceContent content) {
        ServiceContent serverContent = this.lambdaQuery().eq(ServiceContent::getOrderNo, content.getOrderNo())
                .eq(ServiceContent::getServiceType, content.getServiceType())
                .eq(ServiceContent::getWorkerId, content.getWorkerId())
                .eq(ServiceContent::getDate, content.getDate()).one();
        if (Objects.isNull(serverContent)) {
            log.error("服务内容不存在, 用户信息:{}", UserContextHolder.getCurrentUserLog());
            throw new BusinessException(SERVICE_CONTENT_NOT_EXIST);
        }
        if (serverContent.getServiceContentStatus() != 1) {
            log.error("服务内容不存在, 用户信息:{}", UserContextHolder.getCurrentUserLog());
            throw new BusinessException(SERVICE_CONTENT_NOT_EXIST);
        }
        return this.updateById(content);
    }
}
