package com.tencent.wxcloudrun.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.tencent.wxcloudrun.config.BusinessException;
import com.tencent.wxcloudrun.dto.ExternalDTO;
import com.tencent.wxcloudrun.model.ImageItem;
import com.tencent.wxcloudrun.response.TencentLocationParser;
import com.tencent.wxcloudrun.service.api.ExternalService;
import com.tencent.wxcloudrun.utils.HttpClientUtil;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.faceid.v20180301.FaceidClient;
import com.tencentcloudapi.faceid.v20180301.models.IdCardOCRVerificationRequest;
import com.tencentcloudapi.faceid.v20180301.models.IdCardOCRVerificationResponse;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.SendSmsRequest;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.tencent.wxcloudrun.enums.ResponseEnum.*;


@Slf4j
@Service
public class ExternalServiceImpl implements ExternalService {
    //    private static final String AUTH_URL = "http://api.weixin.qq.com/_/cos/getauth";
//    private static final String META_URL = "https://api.weixin.qq.com/_/cos/metaid/encode";
    private static final String ID_CAR_URL = "https://api.weixin.qq.com/cv/ocr/idcard";
    private static final String DOWNLOAD_URL = "http://api.weixin.qq.com/tcb/batchdownloadfile";

    @Value("${TENCENT.SECRET_ID}")
    private String SECRET_ID;
    @Value("${TENCENT.SECRET_KEY}")
    private String SECRET_KEY;
    @Value("${TENCENT.REGION}")
    private String REGION;
    private String PROD_ENV = "prod-4glmr16t2e609766";

    // 短信相关配置
    @Value("${TENCENT.SMS.SDK_APP_ID:1400009099}")
    private String SMS_SDK_APP_ID;
    @Value("${TENCENT.SMS.SIGN_NAME:腾讯云}")
    private String SMS_SIGN_NAME;
    @Value("${TENCENT.SMS.TEMPLATE_ID:449739}")
    private String SMS_TEMPLATE_ID;

    @Autowired
    private WxMaService wxMaService;

    private static final String API_URL = "https://apis.map.qq.com/ws/geocoder/v1/";
    private final String MAP_KEY = "6Q2BZ-QKP3M-IJ76W-66LL5-NEVJK-GFFHQ";

    // 身份证校验接口
    @Override
    public ExternalDTO.IDCardResponse id2MetaVerify(ExternalDTO.IDCard idCard) {
        // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
        // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
        // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
        com.tencentcloudapi.common.Credential cred = new com.tencentcloudapi.common.Credential(SECRET_ID, SECRET_KEY);
        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint(REGION);
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        FaceidClient client = new FaceidClient(cred, "", clientProfile);
        try {
            IdCardOCRVerificationRequest req = new IdCardOCRVerificationRequest();
            req.setIdCard(idCard.getIdNum());
            req.setName(idCard.getUserName());
            IdCardOCRVerificationResponse idCardOCRVerificationResponse = client.IdCardOCRVerification(req);

            ExternalDTO.IDCardResponse idCardResponse = new ExternalDTO.IDCardResponse();
            idCardResponse.setCode(idCardOCRVerificationResponse.getResult());
            idCardResponse.setMessage(idCardOCRVerificationResponse.getDescription());
            idCardResponse.setRequestId(idCardOCRVerificationResponse.getRequestId());
            return idCardResponse;
        } catch (TencentCloudSDKException e) {
            log.error("id2MetaVerify:error {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }


    @Override
    public ExternalDTO.IDCard ocrIdCard(Map<String, String> idCardImages) {
        List<String> collect = new ArrayList<>(idCardImages.values());
        try {
            ExternalDTO.IDCard idCard = new ExternalDTO.IDCard();
            List<ExternalDTO.ImageObj> imagePathList = new ArrayList<>();
            imagePathList = getImagePath(collect);
            if (imagePathList.isEmpty()) {
                throw new BusinessException(IMAGE_NOT_EXIST);

            }
            for (ExternalDTO.ImageObj imageObj : imagePathList) {
                if (imageObj.getStatus() != 0) {
                    idCard.setErrmsg(imageObj.getErrmsg());
                    idCard.setErrcode(imageObj.getStatus());
                    return idCard;
                }
            }
            String accessToken = wxMaService.getAccessToken();

            // 设置请求头
//            HttpHeaders headers = new HttpHeaders();
//            headers.setContentType(MediaType.APPLICATION_JSON);
            for (ExternalDTO.ImageObj imageObj : imagePathList) {
                // 发送请求
                Map<String, String> queryParams = new HashMap<>();
                queryParams.put("type", "photo");
                queryParams.put("img_url", imageObj.getDownload_url());
                queryParams.put("access_token", accessToken);
                String responseBody = HttpClientUtil.post(ID_CAR_URL, queryParams, "{}");
                ExternalDTO.OCRResponse idCardMap = JSONUtil.toBean(responseBody, ExternalDTO.OCRResponse.class);
                BeanUtils.copyProperties(idCardMap, idCard);
                log.error("idCardMap:{}", idCardMap);
                if (idCardMap.getErrcode() != 0) {
                    throw new BusinessException(IMAGE_NOT_VALID);
                }
                String type = idCardMap.getType();
                if ("Front".equals(type)) {
                    String userName = idCardMap.getName();
                    String carId = idCardMap.getId();
                    String addr = idCardMap.getAddr();
                    String gender = idCardMap.getGender();
                    String nationality = idCardMap.getNationality();
                    idCard.setUserName(userName);
                    idCard.setIdNum(carId);
                } else if ("Back".equals(type)) {
                    String valid_date = idCardMap.getValid_date();
                    String[] split = valid_date.split("-");
                    log.info("valid_date:{}", Arrays.toString(split));
                    idCard.setValidityBegin(split[0]);
                    idCard.setValidityEnd(split[1]);
                }
            }
            return idCard;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<ExternalDTO.ImageObj> getImagePath(List<String> idCardImages) {
        // 设置请求体
        List<Map> idCardList = new ArrayList<>();
        for (String imagePath : idCardImages) {
            Map<String, String> imageMap = new HashMap<>();
            imageMap.put("fileid", imagePath);
            imageMap.put("max_age", "7200");
            idCardList.add(imageMap);
        }
        Map<String, Object> map = new HashMap<>();
        map.put("env", PROD_ENV);
        map.put("file_list", idCardList);
        String requestBody = JSON.toJSONString(map, true);
        log.info("requestBody:{}", requestBody);
        String responseBody = null;
        try {
            String accessToken = wxMaService.getAccessToken();
            Map<String, String> params = new HashMap<>();
            params.put("access_token", accessToken);
            responseBody = HttpClientUtil.post(DOWNLOAD_URL, params, requestBody);
        } catch (Exception e) {
            log.error("failed to getImagePath, requestBody = {}, responseBody = {}", requestBody, responseBody);
            return new ArrayList<>();
        }
        ExternalDTO.ImagesResponse bean = JSONUtil.toBean(responseBody, ExternalDTO.ImagesResponse.class);
        log.info("ImagesResponse bean:{}", bean);
        return bean.getFile_list();

    }

    @Override
    public List<String> convertUri(String url) {
        try {
            List<ImageItem> imageItems = ImageItem.parseList(url);
            if (CollectionUtils.isEmpty(imageItems)) {
                return Collections.emptyList();
            }
            List<String> uri = imageItems.stream().map(ImageItem::getUrl).collect(Collectors.toList());
            return getImagePath(uri).stream()
                    .map(ExternalDTO.ImageObj::getDownload_url)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("failed to convertUri, url = {}", url, e);
            return Collections.emptyList();
        }
    }


    /**
     * 逆地址解析方法
     *
     * @param latLong 经度  纬度
     * @return 解析结果的 JSON 字符串
     */
    @Override
    public TencentLocationParser.ReverseGeocodeResult reverseGeocode(Map<String, Double> latLong) {
        // 示例坐标：北京市海淀区
        double latitude = latLong.get("latitude");
        double longitude = latLong.get("longitude");
        // 构建请求参数
        Map<String, String> params = new HashMap<>();
        params.put("location", latitude + "," + longitude);
        params.put("key", MAP_KEY);
        params.put("get_poi", "0"); // 是否返回周边POI信息，0-不返回
        try {
            TencentLocationParser parser = new TencentLocationParser();
            String fullUrl = buildUrlWithParams(params);
            String result = HttpClientUtil.get(fullUrl, null);
            log.info("fullUrl:{}", fullUrl);
            log.info("result:{}", result);
            return parser.parseReverseGeocodeResponse(result);

        } catch (Exception e) {
            log.error("failed to reverseGeocode, params = {}, e = {}", params, e.getMessage());
            log.error("Exception: ", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 构建带参数的URL
     */
    private String buildUrlWithParams(Map<String, String> params) throws UnsupportedEncodingException {
        StringBuilder urlBuilder = new StringBuilder(ExternalServiceImpl.API_URL);
        if (!params.isEmpty()) {
            urlBuilder.append("?");
            boolean first = true;
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (!first) {
                    urlBuilder.append("&");
                }
                urlBuilder.append(URLEncoder.encode(entry.getKey(), String.valueOf(StandardCharsets.UTF_8)))
                        .append("=")
                        .append(URLEncoder.encode(entry.getValue(), String.valueOf(StandardCharsets.UTF_8)));
                first = false;
            }
        }
        return urlBuilder.toString();
    }

    /**
     * 发送短信方法
     * @param phoneNumbers 手机号数组，格式：+8613711112222
     * @param templateParams 模板参数数组
     * @return 发送结果
     */
    public SendSmsResponse sendSms(String[] phoneNumbers, String[] templateParams) {
        return sendSms(phoneNumbers, templateParams, SMS_TEMPLATE_ID);
    }

    /**
     * 发送短信方法（指定模板ID）
     * @param phoneNumbers 手机号数组，格式：+8613711112222
     * @param templateParams 模板参数数组
     * @param templateId 模板ID
     * @return 发送结果
     */
    public SendSmsResponse sendSms(String[] phoneNumbers, String[] templateParams, String templateId) {
        try {
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId，SecretKey
            // 为了保护密钥安全，建议将密钥设置在环境变量中或者配置文件中
            Credential cred = new Credential(SECRET_ID, SECRET_KEY);

            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setReqMethod("POST"); // POST请求
            httpProfile.setConnTimeout(10); // 请求连接超时时间，单位为秒(默认60秒)
            httpProfile.setWriteTimeout(10);  // 设置写入超时时间，单位为秒(默认0秒)
            httpProfile.setReadTimeout(10);  // 设置读取超时时间，单位为秒(默认0秒)

            // 指定接入地域域名，默认就近地域接入域名为 sms.tencentcloudapi.com
            httpProfile.setEndpoint("sms.tencentcloudapi.com");

            // 实例化一个客户端配置对象，可以指定超时时间等配置
            ClientProfile clientProfile = new ClientProfile();
            // SDK默认用TC3-HMAC-SHA256进行签名
            clientProfile.setSignMethod("HmacSHA256");
            clientProfile.setHttpProfile(httpProfile);

            // 实例化要请求产品(以sms为例)的client对象
            // 第二个参数是地域信息，可以直接填写字符串ap-guangzhou
            SmsClient client = new SmsClient(cred, "ap-guangzhou", clientProfile);

            // 实例化一个请求对象
            SendSmsRequest req = new SendSmsRequest();

            // 短信应用ID: 短信SdkAppId在 [短信控制台] 添加应用后生成的实际SdkAppId
            req.setSmsSdkAppId(SMS_SDK_APP_ID);

            // 短信签名内容: 使用 UTF-8 编码，必须填写已审核通过的签名
            req.setSignName(SMS_SIGN_NAME);

            // 模板 ID: 必须填写已审核通过的模板 ID
            req.setTemplateId(templateId);

            // 模板参数: 模板参数的个数需要与 TemplateId 对应模板的变量个数保持一致，若无模板参数，则设置为空
            if (templateParams != null && templateParams.length > 0) {
                req.setTemplateParamSet(templateParams);
            }

            // 下发手机号码，采用 E.164 标准，+[国家或地区码][手机号]
            // 示例如：+8613711112222， 其中前面有一个+号 ，86为国家码，13711112222为手机号
            req.setPhoneNumberSet(phoneNumbers);

            // 用户的 session 内容（无需要可忽略）: 可以携带用户侧 ID 等上下文信息，server 会原样返回
            req.setSessionContext("");

            // 短信码号扩展号（无需要可忽略）: 默认未开通，如需开通请联系腾讯云短信小助手
            req.setExtendCode("");

            // 国内短信无需填写该项；国际/港澳台短信已申请独立 SenderId 需要填写该字段
            req.setSenderId("");

            // 通过 client 对象调用 SendSms 方法发起请求
            SendSmsResponse res = client.SendSms(req);

            // 记录日志
            log.info("短信发送结果: {}", SendSmsResponse.toJsonString(res));

            return res;

        } catch (TencentCloudSDKException e) {
            log.error("发送短信异常: {}", e.getMessage(), e);
            throw new BusinessException(SYSTEM_ERROR.getCode(), "发送短信失败: " + e.getMessage());
        }
    }

    /**
     * 发送验证码短信
     * @param phoneNumber 手机号，格式：+8613711112222
     * @param verificationCode 验证码
     * @return 发送结果
     */
    public SendSmsResponse sendVerificationCodeSms(String phoneNumber, String verificationCode) {
        String[] phoneNumbers = {phoneNumber};
        String[] templateParams = {verificationCode};
        return sendSms(phoneNumbers, templateParams);
    }

}
