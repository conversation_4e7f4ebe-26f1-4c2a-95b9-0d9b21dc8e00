package com.tencent.wxcloudrun.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Maps;
import com.tencent.wxcloudrun.config.BusinessException;
import com.tencent.wxcloudrun.dto.AddressesDTO;
import com.tencent.wxcloudrun.dto.CityProvince;
import com.tencent.wxcloudrun.model.Addresses;
import com.tencent.wxcloudrun.dao.AddressesMapper;
import com.tencent.wxcloudrun.model.WxUsers;
import com.tencent.wxcloudrun.service.api.IAddressesService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tencent.wxcloudrun.service.api.IWxUsersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户地理位置数据存储 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
@Service
public class AddressesServiceImpl extends ServiceImpl<AddressesMapper, Addresses> implements IAddressesService {

    @Autowired
    private IWxUsersService usersService;

    private final List<CityProvince.Province> provinces = new CityProvince().getProvinces();

    @Override
    public Addresses queryByUserIdAndAddressId(String userId, long id) {
        return this.lambdaQuery()
                .eq(Addresses::getUserId, userId)
                .eq(Addresses::getId, id)
                .one();
    }

    @Override
    public boolean delete(long id, String userId) {
        HashMap<String, Object> map = Maps.newHashMap();
        map.put("id", id);
        map.put("user_id", userId);
        return this.baseMapper.deleteByMap(map) > 0;
    }

    @Override
    public Boolean save(AddressesDTO.Add dto) {
        Addresses addresses = new Addresses();
        BeanUtil.copyProperties(dto, addresses);
        addresses.setId(null);
        addresses.setCreateTime(System.currentTimeMillis());
        addresses.setUpdateTime(addresses.getCreateTime());
        addresses.setIsDefault(dto.getIsDefault());
        if (dto.getIsDefault()){
            updateWxUserIfDefault(dto);
            // 更新其他地址为非默认地址
            updateUpdateDefault(dto);
        }
        return this.save(addresses);
    }

    @Override
    public Boolean updateByIdAndUserId(AddressesDTO.Update dto) {
        Addresses addresses = new Addresses();
        BeanUtil.copyProperties(dto, addresses);
        updateWxUserIfDefault(dto);
        LambdaUpdateWrapper<Addresses> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(Addresses::getId, dto.getId())
                .eq(Addresses::getUserId, dto.getUserId());
        if (dto.getIsDefault()){
            updateWxUserIfDefault(dto);
            // 更新其他地址为非默认地址
            updateUpdateDefault(dto);
        }

        return baseMapper.update(addresses, updateWrapper) > 0;
    }

    private void updateWxUserIfDefault(AddressesDTO.Base dto) {
        if (dto.getLatitude() != null && dto.getLongitude() != null) {
            WxUsers byUser = usersService.getByOpenId(dto.getUserId());
            if (byUser != null) { // 防止用户不存在导致空指针异常
                byUser.setLatitude(dto.getLatitude());
                byUser.setLongitude(dto.getLongitude());
                usersService.updateById(byUser);
            }
        }
    }

    private void updateUpdateDefault(AddressesDTO.Base dto) {
        List<Addresses> list = lambdaQuery().eq(Addresses::getUserId, dto.getUserId()).eq(Addresses::getIsDefault, true).list();
        if (!CollectionUtils.isEmpty(list)) {
            for (Addresses addresses : list) {
                addresses.setIsDefault(false);
                baseMapper.updateById(addresses);
            }
        }

    }


    @Override
    public List<Addresses> listOrderDefault(String userId) {
        List<Addresses> addressesList = lambdaQuery().in(Addresses::getUserId, userId).list();
        if (CollectionUtils.isEmpty(addressesList)) {
            return Collections.emptyList();
        }
        return addressesList.stream()
                .sorted(Comparator.comparing(Addresses::getIsDefault))
                .collect(Collectors.toList());
    }

    /**
     * 根据城市名称获取区县列表
     * @param cityName 城市名称
     * @return 区县列表，如果找不到返回null
     */
    @Override
    public List<CityProvince.Area> getAreasByCityName(String cityName) {
        return provinces.stream()
                .flatMap(province -> province.getCity().stream())
                .filter(city -> city.getName().equals(cityName))
                .findFirst()
                .map(CityProvince.City::getArea)
                .orElse(null);
    }

    /**
     * 根据省份名称获取城市列表
     * @param provinceName 省份名称
     * @return 城市列表，如果找不到返回null
     */
    @Override
    public List<CityProvince.City> getCitiesByProvinceName(String provinceName) {
        return provinces.stream()
                .filter(province -> province.getName().equals(provinceName))
                .findFirst()
                .map(CityProvince.Province::getCity)
                .orElse(null);
    }

    /**
     * 获取所有省份列表
     * @return 所有省份列表
     */
    @Override
    public List<CityProvince.Province> getAllProvinces() {
        return provinces;
    }

    /**
     * 根据城市代码获取城市名称和区县列表
     * @param cityCode 城市代码
     * @return 包含城市名称和区县列表的Map
     */
    @Override
    public Map<String, Object> getCityInfoByCode(String cityCode) {
        Map<String, Object> result = new HashMap<>();
        for (CityProvince.Province province : provinces) {
            if (province.getCity() != null) {
                for (CityProvince.City city : province.getCity()) {
                    if (city != null && city.getCode() != null && city.getCode().equals(cityCode)) {
                        result.put("cityName", city.getName());
                        result.put("areas", city.getArea());
                        return result;
                    }
                }
            }
        }
        // 如果未找到城市，检查是否是省代码
        for (CityProvince.Province province : provinces) {
            if (province.getCode() != null && province.getCode().equals(cityCode)) {
                result.put("cityName", province.getName());
                List<CityProvince.City> city = province.getCity();
                if (city.size() == 1){
                    result.put("areas", city.get(0).getArea());
                }
                else {
                    result.put("areas", city);
                }

                return result;
            }
        }
        return null; // 如果未找到任何匹配项，返回null
    }

}
