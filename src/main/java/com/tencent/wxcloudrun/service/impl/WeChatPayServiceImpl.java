package com.tencent.wxcloudrun.service.impl;

import com.google.gson.Gson;
import com.tencent.wxcloudrun.config.BusinessException;
import com.tencent.wxcloudrun.dto.TransferBillsDTO;
import com.tencent.wxcloudrun.service.api.WeChatPayService;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.exception.HttpException;
import com.wechat.pay.java.core.exception.MalformedMessageException;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.core.exception.ValidationException;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.core.util.GsonUtil;
import com.wechat.pay.java.service.payments.jsapi.JsapiServiceExtension;
import com.wechat.pay.java.service.payments.jsapi.model.*;
import com.wechat.pay.java.service.payments.jsapi.model.Amount;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.profitsharing.ProfitsharingService;
import com.wechat.pay.java.service.profitsharing.model.*;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.*;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;

import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

import static com.tencent.wxcloudrun.enums.ResponseEnum.REFUND_FAILED;

@Service
@Slf4j
public class WeChatPayServiceImpl implements WeChatPayService {

    // @Value("${wx.subMchId}")
    private String SUB_MCH_ID = "1713570828";
    // @Value("${wx.appid}")
    private String APPID = "wxe917114e201642c1";
    // @Value("${wx.pay_ip}")
//    private String PAY_IP = "*************";
    private String PAY_IP = "nametxy.cn";

    /**
     * 商户API私钥路径
     */
//     @Value("${wx.privateKeyPath}")
    public String privateKeyPath = "/app/apiclient_key.pem"; // 线上启动路径1111
//    public String privateKeyPath = "src/main/resources/apiclient_key.pem"; // 本地启动路径
    /**
     * 商户证书序列号
     */
//    // @Value("${wx.merchantSerialNumber}")
    public String merchantSerialNumber = "297D9BAE7682BAA198620C2C7B90CA761C5A9469";
    /**
     * 商户APIV3密钥
     */
//    // @Value("${wx.apiV3key}")
    public String apiV3key = "maomaochongwukeji20250413FMCqian";


    // @Value("${wx.prod_env}")
    public String prodEnv = "pord";


    private final RSAAutoCertificateConfig config = new RSAAutoCertificateConfig.Builder()
            .merchantId(SUB_MCH_ID)
            .privateKeyFromPath(privateKeyPath)
            .merchantSerialNumber(merchantSerialNumber)
            .apiV3Key(apiV3key)
            .build();
    private final JsapiServiceExtension jsapiService = new JsapiServiceExtension.Builder().config(config).build();
    private final RefundService refundService = new RefundService.Builder().config(config).build();
    private final ProfitsharingService profitService = new ProfitsharingService.Builder().config(config).build();
    public final WxMchTransferService mchTransferService = new WxMchTransferService.Builder().config(config).build();

    /**
     * 获取预支付请求
     *
     * @param payAmount 支付金额
     * @param orderNo   订单号
     * @param openId    用户ID
     * @return 微信支付
     */

    @Override
    public PrepayWithRequestPaymentResponse prepay(long payAmount, String orderNo, String openId) {
        log.error("payAmount: {},orderNo:{} :openId:{}, prodEnv:{}",payAmount,orderNo, openId, prodEnv.equals("test"));
        if (prodEnv.equals("test")) {
            PrepayWithRequestPaymentResponse prepayWithRequestPaymentResponse = new PrepayWithRequestPaymentResponse();
            prepayWithRequestPaymentResponse.setAppId(APPID);
            return prepayWithRequestPaymentResponse;
        }
        // 构建service
        PrepayRequest request = getPrepayRequest(payAmount, orderNo, openId);
        // 调用下单方法，得到应答
        log.error("request:{}", request.toString());
        return jsapiService.prepayWithRequestPayment(request);
    }

    /**
     * 封装预支付参数
     *
     * @param payAmount 支付金额
     * @param orderNo   订单号
     * @param openId    用户ID
     * @return 微信支付
     */
    private @NotNull PrepayRequest getPrepayRequest(long payAmount, String orderNo, String openId) {
        PrepayRequest request = new PrepayRequest();
        request.setAppid(APPID);
        request.setMchid(SUB_MCH_ID);
        // 回调地址
        //TODO 需要添加回调路径
        String notifyUrl = "http://" + PAY_IP + "/api/UserOrders/wechat/pay/notify";
        request.setNotifyUrl(notifyUrl);
        request.setDescription("毛毛宠物-服务下单");
        request.setOutTradeNo(orderNo);
        Amount amount = new Amount();
        amount.setTotal(Math.toIntExact(payAmount));
        request.setAmount(amount);
        Payer payer = new Payer();
        payer.setOpenid(openId);
        request.setPayer(payer);
        // 设置分账订单
        SettleInfo settleInfo = new SettleInfo();
//        settleInfo.setProfitSharing(true);
        request.setSettleInfo(settleInfo);
        return request;
    }

    /**
     * 微信回调
     *
     * @param httpRequest httpRequest
     * @return Transaction
     */
    @Override
    public Transaction weChatPayCallback(String requestBody, HttpServletRequest httpRequest) {
        if (prodEnv.equals("test")) {
            Gson gson = GsonUtil.getGson();
            Transaction response = gson.fromJson(requestBody, Transaction.class);
            return response;
        }
        // 构造 RequestParam
        String wechatSignature = httpRequest.getHeader("Wechatpay-Signature");
        String wechatpayNonce = httpRequest.getHeader("Wechatpay-Nonce");
        String wechatPaySerial = httpRequest.getHeader("Wechatpay-Serial");
        String wechatTimestamp = httpRequest.getHeader("Wechatpay-Timestamp");
        // 获取原始请求体
        if (wechatSignature == null || wechatpayNonce == null || wechatPaySerial == null || wechatTimestamp == null) {
            log.error("Missing required headers: Signature={}, Nonce={}, Serial={}, Timestamp={}",
                    wechatSignature, wechatpayNonce, wechatPaySerial, wechatTimestamp);
            throw new IllegalArgumentException("Required headers are missing");
        }
        RequestParam requestParam = new RequestParam.Builder()
                .serialNumber(wechatPaySerial)
                .nonce(wechatpayNonce)
                .signature(wechatSignature)
                .timestamp(wechatTimestamp)
                .body(requestBody)
                .build();
        NotificationParser parser = new NotificationParser(config);
        try {
            // 以支付通知回调为例，验签、解密并转换成 Transaction
            Transaction parse = parser.parse(requestParam, Transaction.class);
            log.error("微信回调 parse:{}", parse);
            return parse;
        } catch (ValidationException e) {
            // 签名验证失败，返回 401 UNAUTHORIZED 状态码
            log.error("Signature validation failed: {}", e.getMessage(), e);
            // 返回 401 UNAUTHORIZED 状态码
        } catch (Exception e) {
            log.error("Unexpected error during parsing: {}", e.getMessage(), e);
            throw new RuntimeException("Unexpected error during parsing", e);
        }
        return null;
    }

    /**
     * 查询订单支付结果
     *
     * @param orderId 订单ID
     * @return 支付结果
     */
    @Override
    public Boolean queryWxOrder(String orderId) {
        if (prodEnv.equals("test")) {
            return Boolean.TRUE;
        }
        QueryOrderByOutTradeNoRequest queryRequest = new QueryOrderByOutTradeNoRequest();
        queryRequest.setMchid(SUB_MCH_ID);
        queryRequest.setOutTradeNo(orderId);
        try {
            Transaction transaction = jsapiService.queryOrderByOutTradeNo(queryRequest);
            log.error("查询订单支付结果 transaction:{}", transaction);
            return transaction.getTradeState().equals(Transaction.TradeStateEnum.SUCCESS);
        } catch (ServiceException e) {
            // API返回失败, 例如ORDER_NOT_EXISTS
            log.error("code: {},message:{} ", e.getErrorCode(), e.getErrorMessage());
            log.error("repose body :{} ", e.getResponseBody());
        }
        return Boolean.FALSE;
    }

    /**
     * 退款申请
     *
     * @param orderNo     订单号
     * @param totalPrice  原金额
     * @param finalPrice  退款金额
     * @param outRefundNo 退款订单号，不能是订单号，并且系统唯一
     * @return 退款结果
     */
    @Override
    public Boolean refundOrder(String orderNo, long totalPrice,long finalPrice, String outRefundNo, String reason) {
        if (prodEnv.equals("test")) {
            return Boolean.TRUE;
        }
        CreateRequest createRequest = new CreateRequest();
        // 调用request.setXxx(val)设置所需参数，具体参数可见Request定义
        createRequest.setOutTradeNo(orderNo);
        // 退款单号
        createRequest.setOutRefundNo(outRefundNo);
        // 退款原因
        createRequest.setReason(reason);
        AmountReq amount = new AmountReq();
        amount.setRefund(finalPrice);
        amount.setTotal(totalPrice);
        amount.setCurrency("CNY");
        createRequest.setAmount(amount);
        try {
            log.error("退款申请 transaction:{}", createRequest);
            Refund refund = refundService.create(createRequest);
            log.error("退款返回结果 transaction:{}", refund);
            return refund.getStatus().equals(Status.SUCCESS) || refund.getStatus().equals(Status.PROCESSING);
        } catch (HttpException e) { // 发送HTTP请求失败
            // 调用e.getHttpRequest()获取请求打印日志或上报监控，更多方法见HttpException定义
            log.error("refundOrder getHttpRequest:{} ", e.getHttpRequest());
        } catch (ServiceException e) { // 服务返回状态小于200或大于等于300，例如500
            // 调用e.getResponseBody()获取返回体打印日志或上报监控，更多方法见ServiceException定义
            log.error("refundOrder code:{}  message:{} ", e.getErrorCode(), e.getErrorMessage());
            throw new BusinessException(500, e.getErrorMessage());
        } catch (MalformedMessageException e) { // 服务返回成功，返回体类型不合法，或者解析返回体失败
            // 调用e.getMessage()获取信息打印日志或上报监控，更多方法见MalformedMessageException定义
            log.error("refundOrder  message:{} ", e.getMessage());
        }
        return Boolean.FALSE;
    }

    /**
     * 查询单笔退款（通过商户退款单号）
     *
     * @param outRefundNo 商户订单号
     * @return 退款结果
     */
    @Override
    public Boolean queryByOutRefundNo(String outRefundNo) {
        if (prodEnv.equals("test")) {
            return Boolean.TRUE;
        }
        QueryByOutRefundNoRequest request = new QueryByOutRefundNoRequest();
        request.setOutRefundNo(outRefundNo);
        request.setSubMchid(SUB_MCH_ID);
        // 调用request.setXxx(val)设置所需参数，具体参数可见Request定义
        // 调用接口
        Refund refund = refundService.queryByOutRefundNo(request);
        log.error("查询单笔退款  refund:{}  ", refund.toString());
        return refund.getStatus().equals(Status.SUCCESS);
    }

    /**
     * 添加分账接收方API
     *
     * @param openId 分账用户openId
     * @return 是否添加成功
     */
    @Override
    public Boolean addReceiver(String openId) {
        if (prodEnv.equals("test")) {
            return Boolean.TRUE;
        }
        AddReceiverRequest request = new AddReceiverRequest();
        request.setAppid(APPID);
        request.setType(ReceiverType.PERSONAL_OPENID);
        request.setAccount(openId);
        request.setRelationType(ReceiverRelationType.USER);
        AddReceiverResponse addReceiverResponse = profitService.addReceiver(request);
        log.error("添加分账  openId:{}  message:{} ", openId, addReceiverResponse.toString());
        return addReceiverResponse.getAccount().equals(openId);
    }

    /**
     * 删除分账接收方
     *
     * @param openId 分账用户openId
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteReceiver(String openId) {
        if (prodEnv.equals("test")) {
            return Boolean.TRUE;
        }
        DeleteReceiverRequest request = new DeleteReceiverRequest();
        request.setAppid(APPID);
        request.setType(ReceiverType.PERSONAL_OPENID);
        request.setAccount(openId);
        DeleteReceiverResponse deleteReceiverResponse = profitService.deleteReceiver(request);
        log.error("删除分账 openId:{} message:{} ", openId, deleteReceiverResponse.toString());
        return deleteReceiverResponse.getAccount().equals(openId);
    }

    /**
     * 请求分账API
     * CreateOrderReceiver().setDescription("分账描述").setAccount("分账接收方openId").setAmount("分账金额");
     *
     * @param wxTransactionId      微信订单号
     * @param outOrderNo           分账订单号（需单独生成）
     * @param createOrderReceivers 分账接收方列表
     * @return True 分账完成，False 处理中
     */
    @Override
    public Boolean createOrder(String wxTransactionId, String outOrderNo, List<CreateOrderReceiver> createOrderReceivers) {
        if (prodEnv.equals("test")) {
            return Boolean.TRUE;
        }
        CreateOrderRequest request = new CreateOrderRequest();
        request.setAppid(APPID);
        request.setSubMchid(SUB_MCH_ID);
        request.setTransactionId(wxTransactionId);
        request.setOutOrderNo(outOrderNo);
        request.setUnfreezeUnsplit(Boolean.TRUE);
        for (CreateOrderReceiver createOrderReceiver : createOrderReceivers) {
            createOrderReceiver.setType(ReceiverType.PERSONAL_OPENID.toString());
        }
        request.setReceivers(createOrderReceivers);
        log.error("开始请求分账 {} ", request);
        OrdersEntity order = profitService.createOrder(request);
        log.error("创建分账 分账结果{} ", order.toString());
        return order.getState().equals(OrderStatus.FINISHED);
    }

    /**
     * 查询分账结果API
     *
     * @param wxTransactionId 微信订单号
     * @param outOrderNo      分账订单号
     * @return 分账结果
     */
    @Override
    public Boolean queryOrder(String wxTransactionId, String outOrderNo) {
        if (prodEnv.equals("test")) {
            return Boolean.TRUE;
        }
        QueryOrderRequest request = new QueryOrderRequest();
        request.setSubMchid(SUB_MCH_ID);
        request.setOutOrderNo(outOrderNo);
        request.setTransactionId(wxTransactionId);
        OrdersEntity ordersEntity = profitService.queryOrder(request);
        log.error("查询分账 分账结果{} ", ordersEntity.toString());
        return ordersEntity.getState().equals(OrderStatus.FINISHED);
    }

    /**
     * 发起商家转账
     *
     * @param outBillNo      转账ID
     * @param openid         用户ID
     * @param transferAmount 转账金额
     * @return
     */
    @Override
    public TransferBillsDTO.TransferBillsResult initiateTransferBills(String outBillNo, String openid, Long transferAmount) {
        if (prodEnv.equals("test")) {
            return TransferBillsDTO.TransferBillsResult.builder().build();
        }

        TransferBillsDTO.TransferBillsRequest request = new TransferBillsDTO.TransferBillsRequest();
        request.setAppid(APPID);
        // 商户订单号
        request.setOutBillNo(outBillNo);
        //营销ID
        request.setTransferSceneId("1005");
        request.setOpenid(openid);
        // 转账金额
        request.setTransferAmount(transferAmount);
        // 转账备注
        request.setTransferRemark("订单金额提现");
        TransferBillsDTO.TransferSceneReportInfo workType = TransferBillsDTO.TransferSceneReportInfo.builder().infoContent("喂养师").infoType("岗位类型").build();
        TransferBillsDTO.TransferSceneReportInfo workInfo = TransferBillsDTO.TransferSceneReportInfo.builder().infoContent("喂养订单佣金").infoType("报酬说明").build();
        request.setTransferSceneReportInfos(Arrays.asList(workInfo, workType));
        TransferBillsDTO.TransferBillsResult initiateBatchTransferResponse = mchTransferService.initiateTransferBills(request);
        log.error("发起商家转账 {} ", initiateBatchTransferResponse.toString());
        return initiateBatchTransferResponse;
    }
}
