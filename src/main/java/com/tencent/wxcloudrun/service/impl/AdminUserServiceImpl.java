package com.tencent.wxcloudrun.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.wxcloudrun.model.WorkerCertification;
import com.tencent.wxcloudrun.model.WxUsers;
import com.tencent.wxcloudrun.response.AdminServiceUserRes;
import com.tencent.wxcloudrun.service.api.*;
import com.tencent.wxcloudrun.vo.AdminUserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AdminUserServiceImpl implements AdminUserService {

    @Autowired
    private IWxUsersService usersService;
    @Autowired
    private WorkerCertificationService workerCertificationService;


    @Override
    public Page<AdminServiceUserRes> serviceUserList(AdminUserVO.UserPage userPage) {
        List<WorkerCertification> workerServiceRecords = workerCertificationService.workerCertificationAll(userPage.getUserIdList());
        Page<AdminServiceUserRes> serviceUserPage = new Page<>(userPage.getCurrent(), userPage.getSize());
        // 数据查询出来为空
        if (CollectionUtils.isEmpty(workerServiceRecords)) {
            return serviceUserPage;
        }
        // 获取WorkerCertification 里面 所有的userID
        List<String> userIdList = workerServiceRecords.stream().map(WorkerCertification::getUserId).collect(Collectors.toList());
        userPage.setUserIdList(userIdList);
        Page<WxUsers> temp = usersService.userListPage(userPage);
        List<WxUsers> records = temp.getRecords();

        List<AdminServiceUserRes> res = records.stream()
                .map(wxUser -> {
                    AdminServiceUserRes userRes = new AdminServiceUserRes();
                    // 假设 WxUsers 有 getUsername() 方法，AdminServiceUserRes 有 setUsername() 方法
                    BeanUtil.copyProperties(wxUser, userRes);
                    // 其他字段依次映射
                    return userRes;
                })
                .collect(Collectors.toList());
        BeanUtil.copyProperties(temp, serviceUserPage);
        serviceUserPage.setRecords(res);
        return serviceUserPage;
    }

}
