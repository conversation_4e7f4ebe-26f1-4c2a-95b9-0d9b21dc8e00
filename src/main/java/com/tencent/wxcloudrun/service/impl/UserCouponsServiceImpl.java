package com.tencent.wxcloudrun.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tencent.wxcloudrun.config.BusinessException;
import com.tencent.wxcloudrun.config.UserContextHolder;
import com.tencent.wxcloudrun.dto.CouponsDTO;
import com.tencent.wxcloudrun.model.Coupons;
import com.tencent.wxcloudrun.model.UserCoupons;
import com.tencent.wxcloudrun.dao.UserCouponsMapper;
import com.tencent.wxcloudrun.model.WxUsers;
import com.tencent.wxcloudrun.service.api.ICouponsService;
import com.tencent.wxcloudrun.service.api.IUserCouponsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tencent.wxcloudrun.service.api.IWxUsersService;
import com.tencent.wxcloudrun.vo.UserCouponsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.tencent.wxcloudrun.config.UserContextHolder.getCurrentUserLog;
import static com.tencent.wxcloudrun.enums.ResponseEnum.*;

/**
 * <p>
 * 用户优惠券管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-02
 */
@Slf4j
@Service
public class UserCouponsServiceImpl extends ServiceImpl<UserCouponsMapper, UserCoupons> implements IUserCouponsService {

    @Autowired
    ICouponsService couponsService;

    @Autowired
    IWxUsersService usersService;


    @Override
    public UserCoupons getByOpenIdAndCouponId(String openId, Long couponId) {
        LambdaQueryWrapper<UserCoupons> queryWrapper = new LambdaQueryWrapper<>();
        // 添加 IN 查询条件
        queryWrapper.eq(UserCoupons::getCouponsId, couponId)
                .eq(UserCoupons::getUserId, openId)
                .eq(UserCoupons::getIsUsed, 0);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<UserCoupons> getByOpenIdAndCouponIds(String openId, List<Long> couponIds) {
        if (couponIds == null || couponIds.isEmpty()) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<UserCoupons> queryWrapper = new LambdaQueryWrapper<>();
        // 添加 IN 查询条件
        queryWrapper.in(UserCoupons::getCouponsId, couponIds)
                .eq(UserCoupons::getUserId, openId)
                .eq(UserCoupons::getIsUsed, 0);
        return baseMapper.selectList(queryWrapper);
    }


    @Override
    public List<UserCoupons> getByOpenIdAndCouponIds(String openId, List<Long> couponIds, Integer isUsed) {
        if (couponIds == null || couponIds.isEmpty()) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<UserCoupons> queryWrapper = new LambdaQueryWrapper<>();
        // 添加 IN 查询条件
        queryWrapper.in(UserCoupons::getCouponsId, couponIds)
                .eq(UserCoupons::getUserId, openId)
                .eq(isUsed != null, UserCoupons::getIsUsed, isUsed);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public boolean reBack(String openId, List<Long> couponIds) {
        List<UserCoupons> userCoupons = getByOpenIdAndCouponIds(openId, couponIds, 1);
        List<UserCoupons> res = userCoupons.stream().map(val -> {
            val.setIsUsed(0);
            val.setUpdateTime(System.currentTimeMillis());
            return val;
        }).collect(Collectors.toList());
        return !baseMapper.insertOrUpdate(res).isEmpty();
    }

    @Override
    public List<UserCouponsVO> listByUserId(String userId, Integer userType) {
        List<UserCoupons> list = this.lambdaQuery()
                .eq(UserCoupons::getUserId, userId)
                .eq(UserCoupons::getIsUsed, 0)
                .list();

        Set<Long> ids = list.stream().map(UserCoupons::getCouponsId).collect(Collectors.toSet());
        List<CouponsDTO.Result> couponsList;
        if (ids.isEmpty()) {
            couponsList = new ArrayList<>();
        } else {
            couponsList = couponsService.listByCouponIds(new ArrayList<>(ids), userType);
        }
        Map<Long, CouponsDTO.Result> couponsMap = couponsList.stream().collect(Collectors.toMap(CouponsDTO.Result::getId, c -> c));
        List<UserCouponsVO> userCouponsVOList = new ArrayList<>();
        for (UserCoupons userCoupons : list) {
            UserCouponsVO userCouponsVO = new UserCouponsVO();
            CouponsDTO.Result coupons = couponsMap.getOrDefault(userCoupons.getCouponsId(), null);
            if (coupons == null) {
                continue;
            }
            userCouponsVO.setOpenId(userCoupons.getUserId());
            userCouponsVO.setCouponsId(userCoupons.getCouponsId());
            userCouponsVO.setValidStart(userCoupons.getValidStart());
            userCouponsVO.setValidEnd(userCoupons.getValidEnd());
            userCouponsVO.setExt(coupons.getExt());
            userCouponsVO.setTitle(coupons.getTitle());
            userCouponsVO.setCouponType(coupons.getCouponType());
            userCouponsVOList.add(userCouponsVO);
        }
        return userCouponsVOList;
    }

    @Override
    public Boolean receiveCoupons(Long couponsId, String userId) {
        UserCoupons userCouponsInfo = lambdaQuery().eq(UserCoupons::getCouponsId, couponsId).eq(UserCoupons::getUserId, userId).one();
        Coupons coupons = couponsService.getById(couponsId);
        if (userCouponsInfo != null || coupons == null || coupons.getCouponsNum() <= 0 || coupons.getIsDelete() == 1) {
            throw new BusinessException(COUPON_NOT_RECEIVE);
        }
        List<Long> couponsIdList = Arrays.asList(100L, 101L, 200L, 204L);
        List<Long> couponsIdList2 = Arrays.asList(201L, 202L);
        if (couponsIdList2.contains(couponsId)) {
            QueryWrapper<UserCoupons> objectQueryWrapper = new QueryWrapper<>();
            objectQueryWrapper.in("coupons_id", couponsIdList2);
            objectQueryWrapper.eq("user_id", userId);
            long count = this.count(objectQueryWrapper);
            if (count >= 1) {
                log.error("用户:{},已领取该优惠券:{}，不能重复领取", UserContextHolder.getCurrentUserLog(), couponsId);
                throw new BusinessException(COUPON_NOT_RECEIVE);
            }
        }

        if (couponsIdList.contains(couponsId)) {
            QueryWrapper<UserCoupons> objectQueryWrapper = new QueryWrapper<>();
            objectQueryWrapper.in("coupons_id", couponsIdList);
            objectQueryWrapper.eq("user_id", userId);
            long count = this.count(objectQueryWrapper);
            if (count >= 2) {
                log.error("用户:{},已领取该优惠券:{}大于2张，不能重复领取", UserContextHolder.getCurrentUserLog(), couponsId);
                throw new BusinessException(COUPON_NOT_RECEIVE);
            }
        }

        //修改只锁定这条记录
        couponsService.lambdaUpdate()
                .set(Coupons::getCouponsNum, coupons.getCouponsNum() - 1)
                .eq(Coupons::getId, couponsId).update();
        UserCoupons userCoupons = new UserCoupons();
        userCoupons.setCouponsId(couponsId);
        userCoupons.setUserId(userId);
        long validStart = System.currentTimeMillis();
        long validEnd = Instant.ofEpochMilli(validStart).plus(30, ChronoUnit.DAYS).toEpochMilli();
        userCoupons.setValidStart(validStart);
        userCoupons.setValidEnd(validEnd);
        userCoupons.setIsUsed(0);
        userCoupons.setCreateTime(new Date().getTime());
        userCoupons.setUpdateTime(new Date().getTime());
        return save(userCoupons);
    }

    @Override
    public Boolean adminReceiveCoupons(Long couponsId, String mobile) {
        WxUsers byMobile = usersService.getByMobile(mobile);
        if (ObjectUtil.isEmpty(byMobile)) {
            log.error("用户不存在:手机号 {}, 昵称 {}", mobile, getCurrentUserLog());
            throw new BusinessException(USER_NOT_EXIST);
        }
        return this.receiveCoupons(couponsId, byMobile.getOpenid());
    }

    @Override
    public List<CouponsDTO.Result> listByPage(CouponsDTO.CouponsPage page) {
        List<CouponsDTO.Result> results = couponsService.listByPage(page);
        if (page.getUserId() != null){
            List<UserCoupons> list = this.lambdaQuery()
                    .eq(UserCoupons::getUserId, page.getUserId())
                    .list();


            List<Long> collect = list.stream().map(UserCoupons::getCouponsId).collect(Collectors.toList());
            if (collect.contains(201L) || collect.contains(202L)){
                collect.add(201L);
                collect.add(202L);
            }
            // 判断如果优惠券id在已领取的优惠券id中，则在 results 里面删除
            results.removeIf(result -> collect.contains(result.getId()));
        }
        return results;

    }

}
