package com.tencent.wxcloudrun.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.wxcloudrun.base.FieldOrder;
import com.tencent.wxcloudrun.dto.CouponsDTO;
import com.tencent.wxcloudrun.model.Coupons;
import com.tencent.wxcloudrun.dao.CouponsMapper;
import com.tencent.wxcloudrun.model.UserCoupons;
import com.tencent.wxcloudrun.model.WxUsers;
import com.tencent.wxcloudrun.service.api.ICouponsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tencent.wxcloudrun.utils.ShortSnowflakeId;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户优惠券管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-02
 */
@Service
public class CouponsServiceImpl extends ServiceImpl<CouponsMapper, Coupons> implements ICouponsService {


    @Override
    public List<CouponsDTO.Result> listByPage(CouponsDTO.CouponsPage page) {
        Page<Coupons> couponsPage = new Page<>(page.getCurrent(), page.getSize());
        for (FieldOrder order : page.getOrders()) {
            if ("asc".equals(order.getOrder())) {
                couponsPage.addOrder(OrderItem.asc(order.getField()));
            } else if ("desc".equals(order.getOrder())) {
                couponsPage.addOrder(OrderItem.desc(order.getField()));
            }
        }
        QueryWrapper<Coupons> objectQueryWrapper = new QueryWrapper<>();
        // 添加当前时间在开始和结束时间之间的条件
        // 添加当前时间大于 valid_start 并且小于 valid_end 的条件
        long now = System.currentTimeMillis();
        objectQueryWrapper.gt("valid_end", now);
        objectQueryWrapper.eq("is_delete", 0);
        objectQueryWrapper.eq("enable", 0);
        objectQueryWrapper.lt("enable_time", now);
        IPage<Coupons> resultPage = this.getBaseMapper().selectPage(couponsPage, objectQueryWrapper);
        return getResults(resultPage.getRecords());
    }

    @Override
    public IPage<CouponsDTO.Result> adminCouponsByPage(CouponsDTO.CouponsPage page) {
        Page<Coupons> couponsPage = new Page<>(page.getCurrent(), page.getSize());
        if (page.getOrders() == null) {
            couponsPage.addOrder(OrderItem.desc("create_time"));
        }
        for (FieldOrder order : page.getOrders()) {
            if ("asc".equals(order.getOrder())) {
                couponsPage.addOrder(OrderItem.asc(order.getField()));
            } else if ("desc".equals(order.getOrder())) {
                couponsPage.addOrder(OrderItem.desc(order.getField()));
            }
        }
        QueryWrapper<Coupons> objectQueryWrapper = new QueryWrapper<>();
        // 添加当前时间在开始和结束时间之间的条件
        // 添加当前时间大于 valid_start 并且小于 valid_end 的条件
        long now = System.currentTimeMillis();
        objectQueryWrapper.eq("is_delete", 0);
        IPage<Coupons> resultPage = this.getBaseMapper().selectPage(couponsPage, objectQueryWrapper);
        List<CouponsDTO.Result> results = getResults(resultPage.getRecords());
        IPage<CouponsDTO.Result> couponsResults = new Page<>(page.getCurrent(), page.getSize());
        couponsResults.setRecords(results);
        return couponsResults;

    }

    private List<CouponsDTO.Result> getResults(List<Coupons> couponsPage) {
        return couponsPage.stream().map(couPons -> {
            CouponsDTO.Result coupons = new CouponsDTO.Result();
            BeanUtils.copyProperties(couPons, coupons); // 显式创建目标对象并复制属性
            return coupons;
        }).collect(Collectors.toList());
    }

    @Override
    public List<CouponsDTO.Result> listByCouponIds(List<Long> couponIds, Integer userType) {
        LambdaQueryWrapper<Coupons> queryWrapper = new LambdaQueryWrapper<>();
        // 添加 IN 查询条件
        queryWrapper.in(Coupons::getId, couponIds)
                .eq(Coupons::getIsDelete, 0);
        if (userType != null) {
            queryWrapper.eq(Coupons::getUserType, userType);
        }
        List<Coupons> coupons = baseMapper.selectList(queryWrapper);

        return getResults(coupons);
    }

    @Override
    public Boolean save(CouponsDTO.Add dto) {
        Coupons coupons = new Coupons();
        BeanUtil.copyProperties(dto, coupons);
        coupons.setId(null);
        coupons.setCreateTime(new Date().getTime());
        coupons.setUpdateTime(new Date().getTime());
        return this.save(coupons);
    }

    @Override
    public Boolean updateById(CouponsDTO.Update dto) {
        Coupons coupons = new Coupons();
        BeanUtil.copyProperties(dto, coupons);
        coupons.setUpdateTime(new Date().getTime());
        return this.updateById(coupons);
    }
}
