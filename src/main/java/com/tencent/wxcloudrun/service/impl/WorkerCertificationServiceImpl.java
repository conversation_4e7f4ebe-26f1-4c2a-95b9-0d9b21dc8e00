package com.tencent.wxcloudrun.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tencent.wxcloudrun.dao.WorkerCertificationMapper;
import com.tencent.wxcloudrun.dto.WorkerCertificationDTO;
import com.tencent.wxcloudrun.model.WorkerCertification;
import com.tencent.wxcloudrun.service.api.WorkerCertificationService;
import com.tencent.wxcloudrun.vo.AdminUserVO;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class WorkerCertificationServiceImpl extends ServiceImpl<WorkerCertificationMapper, WorkerCertification> implements WorkerCertificationService {


    @Override
    public boolean save(WorkerCertificationDTO.ADD dto) {
        WorkerCertification workerCertification = new WorkerCertification();
        BeanUtil.copyProperties(dto, workerCertification);
        workerCertification.setId(null);
        workerCertification.setCreateTime(new Date().getTime());
        workerCertification.setUpdateTime(new Date().getTime());
        return this.save(workerCertification);
    }

    @Override
    public boolean updateById(WorkerCertificationDTO.UPDATE dto) {
        WorkerCertification workerCertification = new WorkerCertification();
        BeanUtil.copyProperties(dto, workerCertification);
        workerCertification.setUpdateTime(new Date().getTime());
        return updateById(workerCertification);
    }

    @Override
    public List<Integer> getWorkerType(String openId) {
        List<WorkerCertification> workerList = this.lambdaQuery().eq(WorkerCertification::getUserId, openId).list();
        return workerList.stream().map(WorkerCertification::getCertificationType).collect(Collectors.toList());

    }


    @Override
    public Page<WorkerCertification> scan(int serviceType, int pageSize, long maxId) {
        // 1. 参数安全校验（避免非法参数导致全表扫描）
        int safePageNum = 1;          // 永远都是第一页
        int safePageSize = Math.min(pageSize, 1000);     // 限制每页最多1000条

        // 2. 构建分页对象
        Page<WorkerCertification> page = new Page<>(safePageNum, safePageSize);

        // 3. 设置排序规则（倒序按ID排序，优先用时间字段如 createTime）
        page.addOrder(OrderItem.asc("id"));

        // 4. 构建查询条件
        LambdaQueryWrapper<WorkerCertification> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(WorkerCertification::getCertificationType, serviceType)
                .lt(WorkerCertification::getId, maxId);        // 小于符号

        // 5. 执行分页查询
        return this.page(page, queryWrapper);           // 注意用 this.page() 方法
    }

    @Override
    public Page<WorkerCertification> workerCertificationPageList(AdminUserVO.UserPage userListPage) {

        // 2. 构建分页对象
        Page<WorkerCertification> page = new Page<>(userListPage.getCurrent(), userListPage.getSize());

        // 3. 设置排序规则（倒序按ID排序，优先用时间字段如 createTime）
        page.addOrder(OrderItem.asc("id"));

        // 4. 构建查询条件
        LambdaQueryWrapper<WorkerCertification> queryWrapper = new LambdaQueryWrapper<>();


        if (StrUtil.isNotEmpty(userListPage.getUserId())) {
            // 按照用户ID过滤
            queryWrapper.like(WorkerCertification::getUserId, userListPage.getUserId());
        }
        if (userListPage.getServiceType() != null) {
            // 按照用户认证类型过滤
            queryWrapper.eq(WorkerCertification::getCertificationType, userListPage.getServiceType());
        }
        // 5. 执行分页查询
        return this.page(page, queryWrapper);           // 注意用 this.page() 方法
    }

    @Override
    public List<WorkerCertification> workerCertificationAll(List<String> userIdList) {

        LambdaQueryWrapper<WorkerCertification> queryWrapper = new LambdaQueryWrapper<>();
        if (CollectionUtil.isNotEmpty(userIdList)){
            queryWrapper.in(WorkerCertification::getUserId, userIdList);
        }
        return this.list(queryWrapper);

    }

    @Override
    public List<WorkerCertification> workerServiceType(Integer serviceType) {
        if (serviceType == null) return Collections.emptyList();
        return this.lambdaQuery().eq(WorkerCertification::getCertificationType, serviceType).list();
    }


    @Override
    public boolean selectByIdFlag(String usrId, int serviceType) {
        List<WorkerCertification> list = this.lambdaQuery().eq(WorkerCertification::getUserId, usrId)
                .eq(WorkerCertification::getCertificationType, serviceType)
                .list();
        return !CollectionUtils.isEmpty(list);
    }


}
