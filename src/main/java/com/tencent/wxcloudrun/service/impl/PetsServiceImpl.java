package com.tencent.wxcloudrun.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import com.google.common.collect.Maps;
import com.tencent.wxcloudrun.dto.PetsDTO;
import com.tencent.wxcloudrun.model.Pets;
import com.tencent.wxcloudrun.dao.PetsMapper;
import com.tencent.wxcloudrun.service.api.IPetsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tencent.wxcloudrun.utils.ShortSnowflakeId;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <p>
 * 宠物详细信息档案 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
@Service
public class PetsServiceImpl extends ServiceImpl<PetsMapper, Pets> implements IPetsService {

    @Override
    public Boolean save(PetsDTO.Add dto) {
        Pets pets = new Pets();
        BeanUtil.copyProperties(dto, pets);
        pets.setId(null);
        pets.setCreateTime(new Date().getTime());
        pets.setUpdateTime(new Date().getTime());
        return save(pets);
    }

    @Override
    public Boolean updateById(PetsDTO.Update dto) {
        Pets pets = new Pets();
        BeanUtil.copyProperties(dto, pets);
        pets.setUpdateTime(new Date().getTime());
        return updateById(pets);
    }

    @Override
    public boolean deleteByUserIdAndId(long id, String userId) {
        HashMap<String, Object> map = Maps.newHashMap();
        map.put("id", id);
        map.put("user_id", userId);
        return this.baseMapper.deleteByMap(map) > 0;
    }

    @Override
    public List<Pets> getByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }
        return this.lambdaQuery()
                .in(Pets::getId, ids).list();
    }
}
