package com.tencent.wxcloudrun.service.impl;

import com.tencent.wxcloudrun.model.CaretakersFlow;
import com.tencent.wxcloudrun.dao.CaretakersFlowMapper;
import com.tencent.wxcloudrun.service.api.ICaretakersFlowService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 喂养员服务类型流程 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
@Service
public class CaretakersFlowServiceImpl extends ServiceImpl<CaretakersFlowMapper, CaretakersFlow> implements ICaretakersFlowService {

}
