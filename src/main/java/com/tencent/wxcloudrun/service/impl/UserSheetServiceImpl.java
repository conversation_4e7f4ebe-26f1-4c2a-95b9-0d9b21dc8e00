package com.tencent.wxcloudrun.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tencent.wxcloudrun.config.BusinessException;
import com.tencent.wxcloudrun.config.UserContextHolder;
import com.tencent.wxcloudrun.dao.UserSheetMapper;
import com.tencent.wxcloudrun.dto.ExamQuestionDTO;
import com.tencent.wxcloudrun.enums.UserSheetStatusEnum;
import com.tencent.wxcloudrun.model.ExamQuestion;
import com.tencent.wxcloudrun.model.UserSheet;
import com.tencent.wxcloudrun.model.WorkerCertification;
import com.tencent.wxcloudrun.service.api.*;
import com.tencent.wxcloudrun.utils.SheetUtil;
import com.tencent.wxcloudrun.utils.TimeUtil;
import com.tencent.wxcloudrun.vo.UserSheetRecordVo;
import com.tencent.wxcloudrun.vo.UserSheetSubmitVo;
import com.tencent.wxcloudrun.vo.UserSheetUpdateVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.tencent.wxcloudrun.enums.ResponseEnum.*;

/**
 * <p>
 * 用户地理位置数据存储 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
@Slf4j
@Service
public class UserSheetServiceImpl extends ServiceImpl<UserSheetMapper, UserSheet> implements IUserSheetService {


    @Autowired
    private WorkerCertificationService workerCertificationService;

    @Autowired
    private ExamQuestionService examQuestionService;

    @Override
    public List<UserSheetRecordVo> queryRecordByUserId(String userId) {
        List<UserSheet> list = this.lambdaQuery()
                .eq(UserSheet::getUserId, userId)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().sorted(Comparator.comparingLong(UserSheet::getCreateTime).reversed())
                .map(val -> {
                    UserSheetRecordVo vo = new UserSheetRecordVo();
                    vo.setUserOpenId(userId);
                    vo.setServiceType(val.getServiceType());
                    vo.setStatusDesc(UserSheetStatusEnum.find(val.getStatus()));
                    vo.setCreateTime(TimeUtil.format(val.getCreateTime()));
                    return vo;
                }).collect(Collectors.toList());
    }

    // 提交试卷
    @Override
    public synchronized boolean submit(UserSheetSubmitVo vo) {
        List<UserSheet> dbUserSheets = queryByUserIdAndServiceType(vo.getServiceUserId(), vo.getServiceType());
        if (!CollectionUtils.isEmpty(dbUserSheets)) {
            UserSheet auditUserSheet = dbUserSheets.stream().filter(val -> val.getStatus() == 1).findAny().orElse(null);
            if (auditUserSheet != null) {
                log.error("请勿重复提交试卷，用户信息:{}", UserContextHolder.getCurrentUserLog());
                throw new BusinessException(SHEET_ALREADY_SUBMIT);
            }
            UserSheet passUserSheet = dbUserSheets.stream().filter(val -> val.getStatus() == 2).findAny().orElse(null);
            if (passUserSheet != null) {
                log.error("考试已通过，无需重复考试，用户信息:{}", UserContextHolder.getCurrentUserLog());
                throw new BusinessException(SHEET_EXIST);
            }
        }
        if (Objects.isNull(vo.getSheetAnswers())) {
            log.error("试卷为空，请先答题，用户信息:{}", UserContextHolder.getCurrentUserLog());
            throw new BusinessException(SHEET_NOT_ANSWERS);
        }

        List<UserSheetSubmitVo.Sheet> sheetAnswers = vo.getSheetAnswers();
        //查询数据库补全字段
        Set<Long> idSet = sheetAnswers.stream().map(ExamQuestionDTO.Base::getId).collect(Collectors.toSet());
        List<ExamQuestion> examQuestions = examQuestionService.listByIds(idSet);
        Map<Long, ExamQuestion> examQuestionMap = examQuestions.stream().collect(Collectors.toMap(ExamQuestion::getId, e -> e));
        List<UserSheetSubmitVo.Sheet> newSheetAnswers = new ArrayList<>();
        for (UserSheetSubmitVo.Sheet sheetAnswer : sheetAnswers) {
            //将数据库数据赋值
            ExamQuestion examQuestion = examQuestionMap.get(sheetAnswer.getId());
            UserSheetSubmitVo.Sheet sheet = new UserSheetSubmitVo.Sheet();
            BeanUtil.copyProperties(examQuestion,sheet);
    
            //将答案赋值
            sheet.setSingleChoose(sheetAnswer.getSingleChoose());
            sheet.setMultipleChoose(sheetAnswer.getMultipleChoose());
            sheet.setJudgment(sheetAnswer.getJudgment());
            sheet.setSubjective(sheetAnswer.getSubjective());
            newSheetAnswers.add(sheet);
        }
    
        newSheetAnswers = SheetUtil.calSheetScore(newSheetAnswers);
        
        UserSheet userSheet = new UserSheet();
        userSheet.setUserId(vo.getServiceUserId());
        userSheet.setScore(0);
        userSheet.setStatus(1);
        userSheet.setServiceType(vo.getServiceType());
        userSheet.setSheetId(vo.getSheetId());
        userSheet.setAnswer(JSONUtil.toJsonStr(newSheetAnswers));
        userSheet.setCreateTime(System.currentTimeMillis());
        userSheet.setUpdateTime(userSheet.getCreateTime());
        return this.save(userSheet);
    }

    @Override
    public List<UserSheet> queryByUserIdAndServiceType(String userId, int serviceType) {
        return this.lambdaQuery()
                .eq(UserSheet::getUserId, userId)
                .eq(UserSheet::getServiceType, serviceType).list();

    }

    // 查询喂养员是否存在待审核的试卷
    @Override
    public boolean queryByUserIdStatus(String userId) {
        List<UserSheet> list = this.lambdaQuery()
                .eq(UserSheet::getUserId, userId)
                .eq(UserSheet::getStatus, 1).list();
        return list != null && list.size() >= 1;
    }

    @Override
    public List<UserSheetSubmitVo.Sheet> generateQuestionBank(String userId, int serviceType) {
        List<ExamQuestion> examQuestions = examQuestionService.generateQuestionBank(serviceType);
        // 将 ExamQuestion 转换为 UserSheetSubmitVo.Sheet 类型
        List<UserSheetSubmitVo.Sheet> sheets = examQuestions.stream()
                .map(question -> {
                    UserSheetSubmitVo.Sheet sheet = new UserSheetSubmitVo.Sheet();
                    BeanUtils.copyProperties(question, sheet); // 显式创建目标对象并复制属性
                    return sheet;
                }).collect(Collectors.toList());


        return sheets;
    }

    @Override
    public List<UserSheetSubmitVo> queryByStatus(int status) {
        List<UserSheet> userSheetList = this.lambdaQuery()
                .eq(UserSheet::getStatus, status)
                .list();
        List<UserSheetSubmitVo> userSheetSubmitVos = userSheetList.stream()
                .map(question -> {
                    UserSheetSubmitVo sheetVo = new UserSheetSubmitVo();
                    String answer = question.getAnswer();
                    // 将 answer 字符串解析为 List<UserSheetSubmitVo.Sheet>
                    List<UserSheetSubmitVo.Sheet> sheetAnswers = JSONUtil.toList(JSONUtil.parseArray(answer),
                            UserSheetSubmitVo.Sheet.class);
                    sheetVo.setSheetAnswers(sheetAnswers);
                    sheetVo.setSheetId(question.getSheetId());
                    sheetVo.setServiceUserId(question.getUserId());
                    sheetVo.setServiceType(question.getServiceType());
                    sheetVo.setCreateTime(question.getCreateTime());
                    return sheetVo;
                }).sorted(Comparator.comparingLong(UserSheetSubmitVo::getCreateTime)).collect(Collectors.toList());


        return userSheetSubmitVos;
    }

    @Override
    public boolean updateUserSheet(UserSheetUpdateVO entity) {
        UserSheet userSheet = this.lambdaQuery()
                .eq(UserSheet::getUserId, entity.getServiceUserId())
                .eq(UserSheet::getServiceType, entity.getServiceType())
                .eq(UserSheet::getStatus, 1)
                .one();
        List<UserSheetSubmitVo.Sheet> sheets = entity.getSheets();
        int totalScore = sheets.stream().map(UserSheetSubmitVo.Sheet::getScore)
                .mapToInt(Integer::intValue).sum();
        userSheet.setScore(totalScore);
        if (entity.getStatus() == 0) {
            userSheet.setStatus(1);
        }
        if (totalScore >= 800) {
            userSheet.setStatus(2);
            entity.setStatus(2);
        }
        if (entity.getStatus() == 3) {
            userSheet.setStatus(entity.getStatus());
        }
        if (entity.getStatus() == 2) {
            userSheet.setStatus(entity.getStatus());
        }

        userSheet.setUpdateTime(System.currentTimeMillis());
        userSheet.setAnswer(JSONUtil.toJsonStr(sheets));
        // 添加认证类型
        addWorkerCertification(entity);
        return updateById(userSheet);
    }

    private void addWorkerCertification(UserSheetUpdateVO entity) {
        if (entity.getStatus() != 2) {
            return;
        }
        WorkerCertification add = new WorkerCertification();
        boolean b = workerCertificationService.selectByIdFlag(entity.getServiceUserId(), entity.getServiceType());
        if (b) {
            return;
        }
        add.setUserId(entity.getServiceUserId());
        add.setCertificationType(entity.getServiceType());
        add.setCertificationNumber(UUID.randomUUID().toString());
        add.setCreateTime(System.currentTimeMillis());
        add.setUpdateTime(System.currentTimeMillis());
//        List<WorkerCertification> list = workerCertificationService.lambdaQuery().eq(WorkerCertification::getUserId, entity.getServiceUserId()).list();
//        if (list.isEmpty()) {
//            // 第一次通过则添加收款账号
//            Boolean b = weChatPayService.addReceiver(entity.getServiceUserId());
//        }
        workerCertificationService.save(add);

    }

}
