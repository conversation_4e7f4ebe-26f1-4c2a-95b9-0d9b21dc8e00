package com.tencent.wxcloudrun.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.wxcloudrun.dto.WorkerOrdersDTO;
import com.tencent.wxcloudrun.model.*;
import com.tencent.wxcloudrun.dao.OrdersMapper;
import com.tencent.wxcloudrun.service.api.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tencent.wxcloudrun.vo.OrderAmountUpdateVo;
import com.tencent.wxcloudrun.vo.AdminOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.tencent.wxcloudrun.enums.OrderStatusEnum.PENDING_ACCEPTANCE;
import static com.tencent.wxcloudrun.enums.OrderStatusEnum.getProcessStatues;

/**
 * <p>
 * 订单全生命周期管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
@Slf4j
@Service
public class OrdersServiceImpl extends ServiceImpl<OrdersMapper, Orders> implements IOrdersService {

    @Autowired
    private IUserCouponsService userCouponsService;


    @Override
    public Page<Orders> ordersPage(AdminOrderVO.OrderPage orderPageVO) {
        // 1. 参数安全校验
        int safePageSize = Math.toIntExact(Math.min(orderPageVO.getSize(), 1000));     // 限制每页最多1000条
        // 2. 构建分页对象
        Page<Orders> page = new Page<>(orderPageVO.getCurrent(), safePageSize);
        // 3. 设置排序规则（倒序按ID排序，优先用时间字段如 createTime）
//        page.addOrder(OrderItem.asc("id"));
        // 4. 构建查询条件
        LambdaQueryWrapper<Orders> queryWrapper = new LambdaQueryWrapper<>();

        if (StrUtil.isNotEmpty(orderPageVO.getUserId())) {
            // 按照用户ID过滤
            queryWrapper.like(Orders::getUserId, orderPageVO.getUserId());
        }
        if (CollectionUtil.isNotEmpty(orderPageVO.getUserIdList())) {
            // 按照多用户ID过滤
            queryWrapper.in(Orders::getUserId, orderPageVO.getUserIdList());
        }
        if (orderPageVO.isOrderFlag()) {
            // 查询没有设置喂养员的订单 按照未接单订单过滤
            queryWrapper.isNull(Orders::getServiceUserId);
        }
        if (StrUtil.isNotEmpty(orderPageVO.getServiceUserId())) {
            // 按照喂养员ID过滤
            queryWrapper.eq(Orders::getServiceUserId, orderPageVO.getServiceUserId());
        }
        if (StrUtil.isNotEmpty(orderPageVO.getOrderNo())) {
            // 按照订单号过滤
            queryWrapper.eq(Orders::getOrderNo, orderPageVO.getOrderNo());
        }

        if (StrUtil.isNotEmpty(orderPageVO.getDistributeUserId())) {
            // 按照分销用户ID过滤
            queryWrapper.eq(Orders::getDistributeUserId, orderPageVO.getDistributeUserId());
        }

        if (orderPageVO.getWithdrawalStatus() != null) {
            queryWrapper.eq(Orders::getWithdrawalStatus, orderPageVO.getWithdrawalStatus());
        }
        if (CollectionUtil.isNotEmpty(orderPageVO.getServiceTypes())) {
            // 服务类型过滤
            queryWrapper.in(Orders::getServiceType, orderPageVO.getServiceTypes());
        }
        if (CollectionUtil.isNotEmpty(orderPageVO.getOrderStatus())) {
            // 按照状态过滤
            queryWrapper.in(Orders::getOrdersStatus, orderPageVO.getOrderStatus());
        }
        queryWrapper.eq(Orders::getIsDelete, 0)    // 软删除标记过滤
                .apply("order_no REGEXP '^[0-9]+$'");
        // 5. 执行分页查询
        return this.page(page, queryWrapper);
    }

    @Override
    public Page<Orders> workerOrdersPage(WorkerOrdersDTO.SearchCondition dto) {

        // 创建分页对象，使用DTO中的分页参数
        Page<Orders> page = new Page<>(dto.getCurrent(), dto.getSize());
        // 特殊用户列表
        List<String> openIdList = Arrays.asList("oHPqm7RIGv5gPxNes9rXeVWur2hQ", "oHPqm7bBw-utodsk1WHzvHdpPJ5M",
                "oHPqm7ZBfI6EtfV_V0ZL4VP_nVZE", "oHPqm7QVrdGNVvblICO6h_vGAqd4", "15811111111", "oHPqm7bnsh5l8vWGq2oJaIyPxsr8");
        String userId = dto.getUserId();
        boolean requestingUserIsInOpenIdList = StrUtil.isNotEmpty(userId) && openIdList.contains(userId);
        // 构建查询条件
        LambdaQueryWrapper<Orders> queryWrapper = new LambdaQueryWrapper<Orders>()
                .in(!CollectionUtils.isEmpty(dto.getServiceType()), Orders::getServiceType, dto.getServiceType())
                .ge(dto.getMinPrice() != null && dto.getMinPrice() > 0, Orders::getServicePrice, dto.getMinPrice())
                .le(dto.getMaxPrice() != null && dto.getMaxPrice() > 0, Orders::getServicePrice, dto.getMaxPrice())
                .eq(StrUtil.isNotEmpty(dto.getUserId()), Orders::getUserId, dto.getUserId())
                .eq(Orders::getIsDelete, 0)    // 软删除标记过滤
                .apply("order_no REGEXP '^[0-9]+$'");
        // 如果请求用户不在特殊列表中，则过滤掉特殊用户的订单
        if (!requestingUserIsInOpenIdList) {
            queryWrapper.notIn(Orders::getUserId, openIdList);
        }
        queryWrapper.orderByDesc(Orders::getFinalPrice);
        // 5. 执行分页查询
        return this.page(page, queryWrapper);
    }


    @Override
    public Page<Orders> scan(List<Integer> serviceTypes, int pageSize, long maxId) {
        // 1. 参数安全校验（避免非法参数导致全表扫描）
        int safePageNum = 1;          // 页码最小为1
        int safePageSize = Math.min(pageSize, 1000);     // 限制每页最多1000条

        // 2. 构建分页对象
        Page<Orders> page = new Page<>(safePageNum, safePageSize);

        // 3. 设置排序规则（倒序按ID排序，优先用时间字段如 createTime）
        page.addOrder(OrderItem.asc("id"));

        // 4. 构建查询条件
        LambdaQueryWrapper<Orders> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Orders::getOrdersStatus, PENDING_ACCEPTANCE.getStatusCode())  // 状态过滤
                .eq(Orders::getIsDelete, 0)    // 软删除标记过滤
                .apply("order_no REGEXP '^[0-9]+$'")
                .isNull(Orders::getServiceUserId); //  查询没有设置喂养员的订单
        if (serviceTypes != null && !serviceTypes.isEmpty()) {
            // 服务类型过滤
            queryWrapper.in(Orders::getServiceType, serviceTypes);
        }

        queryWrapper.lt(Orders::getId, maxId);        // 小于符号

        // 5. 执行分页查询
        return this.page(page, queryWrapper);           // 注意用 this.page() 方法
    }

    @Override
    public Orders getByOrderNo(String orderNo) {
        // 查询数据库返回订单信息
        return this.lambdaQuery().eq(Orders::getOrderNo, orderNo)
                .eq(Orders::getIsDelete, 0).one();
    }

    @Override
    public List<Orders> getByServiceUserId(String serviceUserId) {
        return this.lambdaQuery().eq(Orders::getServiceUserId, serviceUserId)
                .list();
    }

    @Override
    public List<Orders> getByStatuses(List<Integer> statuses) {
        return this.lambdaQuery().in(Orders::getOrdersStatus, statuses)
                .eq(Orders::getIsDelete, 0)
                .list();
    }

    @Override
    public List<Orders> getByDistributorIdAndUserId(String distributorId, String userId) {
        return this.lambdaQuery().eq(Orders::getDistributeUserId, distributorId)
                .eq(Orders::getUserId, userId)
                .in(Orders::getDistributeUserId, getProcessStatues())
                .eq(Orders::getIsDelete, 0)
                .orderByDesc(Orders::getCreateTime)
                .list();
    }

    @Override
    public List<Orders> getByServiceUserIdAndStatus(String serviceOpenId, List<Integer> status, int limit, int pageNum) {
        //根据状态 用户查询订单
        Page<Orders> ordersPage = new Page<>(pageNum, limit);
        ordersPage.addOrder(OrderItem.desc("create_time"));
        Page<Orders> page = this.lambdaQuery()
                .eq(Orders::getServiceUserId, serviceOpenId)
                .in(Orders::getOrdersStatus, status)
                .apply("order_no REGEXP '^[0-9]+$'")
                .eq(Orders::getIsDelete, 0)
                .page(ordersPage);

        return page.getRecords();
    }

    @Override
    public List<Orders> getByServiceUserIdAndStatus(List<Integer> status, int limit, int pageNum) {
        //根据状态 用户查询订单
        Page<Orders> ordersPage = new Page<>(pageNum, limit);
        ordersPage.addOrder(OrderItem.desc("create_time"));
        Page<Orders> page = this.lambdaQuery()
                .in(Orders::getOrdersStatus, status)
                .apply("order_no REGEXP '^[0-9]+$'")
                .eq(Orders::getIsDelete, 0)
                .page(ordersPage);

        return page.getRecords();
    }

    @Override
    public List<Orders> getByUserIdAndStatus(String userId, List<Integer> status, int limit, int pageNum) {
        //根据状态 用户查询订单
        Page<Orders> ordersPage = new Page<>(pageNum, limit);
        ordersPage.addOrder(OrderItem.desc("create_time"));
        Page<Orders> page = this.lambdaQuery()
                .eq(Orders::getUserId, userId)
                .eq(Orders::getIsDelete, 0)
                .in(Orders::getOrdersStatus, status)
                .apply("order_no REGEXP '^[0-9]+$'")
                .orderByDesc(Orders::getCreateTime)
                .page(ordersPage);

        return page.getRecords();
    }

    @Override
    public List<Orders> getByUserIdAndStatus(List<Integer> status, int limit, int pageNum) {
        //根据状态 用户查询订单
        Page<Orders> ordersPage = new Page<>(pageNum, limit);
        ordersPage.addOrder(OrderItem.desc("create_time"));
        Page<Orders> page = this.lambdaQuery()
                .eq(Orders::getIsDelete, 0)
                .in(Orders::getOrdersStatus, status)
                .apply("order_no REGEXP '^[0-9]+$'")
                .orderByDesc(Orders::getCreateTime)
                .page(ordersPage);

        return page.getRecords();
    }

    @Override
    @Transactional
    public Boolean save(Orders orders, List<UserCoupons> updateUserCoupons) {
        if (!updateUserCoupons.isEmpty()) {
            userCouponsService.updateBatchById(updateUserCoupons);
        }

        save(orders);
        return true;
    }

    @Override
    @Transactional
    public Boolean create(Orders orders, List<Orders> ordersList) {
        this.updateById(orders);
        saveBatch(ordersList);
        return true;
    }

    @Override
    @Transactional
    public Boolean refund(Orders orders) {
        String couponIds = orders.getCouponIds();
        List<Long> list = JSONUtil.toList(couponIds, Long.class);
        userCouponsService.reBack(orders.getUserId(), list);
        updateById(orders);
        return true;
    }

    @Override
    public boolean fenqian(OrderAmountUpdateVo vo) {
        return false;
    }


    @Override
    public List<Orders> getTimeAndStartOrders(long finishTime, Integer withdrawalStatus) {
        return this.lambdaQuery()
                .le(Orders::getFinishTime, finishTime)
                .eq(Orders::getWithdrawalStatus, withdrawalStatus)
                .eq(Orders::getIsDelete, 0).list();

    }


}
