package com.tencent.wxcloudrun.service.impl;

import com.tencent.wxcloudrun.config.BusinessException;
import com.tencent.wxcloudrun.model.WxSessionCode;
import com.tencent.wxcloudrun.dao.WxSessionCodeMapper;
import com.tencent.wxcloudrun.service.api.IWxSessionCodeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.tencent.wxcloudrun.enums.ResponseEnum.*;

/**
 * <p>
 * 微信会话码表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Service
public class WxSessionCodeServiceImpl extends ServiceImpl<WxSessionCodeMapper, WxSessionCode> implements IWxSessionCodeService {


    @Override
    public WxSessionCode openid2UserSessionId(String openId) {
        if (openId == null) {
            throw new BusinessException(OPENID_ERROR);
        }
        return this.selectOne(WxSessionCode.builder().openid(openId).build());
    }

    @Override
    public WxSessionCode unicode2UserSessionId(String unicode) {
        if (unicode == null) {
            throw new BusinessException(OPENID_ERROR);
        }
        return this.selectOne(WxSessionCode.builder().unionid(unicode).build());
    }


    private WxSessionCode selectOne(WxSessionCode wxSessionCode) {
        List<WxSessionCode> list = this.lambdaQuery()
                .eq(wxSessionCode.getOpenid() != null, WxSessionCode::getOpenid, wxSessionCode.getOpenid())
                .eq(wxSessionCode.getUnionid() != null, WxSessionCode::getUnionid, wxSessionCode.getUnionid())
                .list();
        if (list == null || list.isEmpty()) {
            return null;
        }
        return list.get(0);
    }

}