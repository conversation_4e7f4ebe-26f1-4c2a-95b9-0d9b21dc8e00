package com.tencent.wxcloudrun.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tencent.wxcloudrun.dao.UserWithdrawnRecordMapper;
import com.tencent.wxcloudrun.model.UserWithdrawnRecord;
import com.tencent.wxcloudrun.service.api.UserWithdrawnRecordService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Service
public class UserWithdrawnRecordImpl extends ServiceImpl<UserWithdrawnRecordMapper, UserWithdrawnRecord> implements UserWithdrawnRecordService {

    @Override
    public boolean save(UserWithdrawnRecord entity) {
        return super.save(entity);
    }
    
    @Override
    public long queryDataTime() {
        // 查询UserWithdrawnRecord 表里面当天的提现记录，提现金额的总和
        long startOfDay = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
        long endOfDay = LocalDateTime.of(LocalDate.now(), LocalTime.MAX).atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
        
        List<UserWithdrawnRecord> amounts = lambdaQuery()
                .ge(UserWithdrawnRecord::getCreateTime, startOfDay)
                .le(UserWithdrawnRecord::getCreateTime, endOfDay)
                .list();
        return amounts.stream()
                .mapToLong(UserWithdrawnRecord::getWithdrawnAmount)
                .sum();
    }
}