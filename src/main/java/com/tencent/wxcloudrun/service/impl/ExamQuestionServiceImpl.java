package com.tencent.wxcloudrun.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tencent.wxcloudrun.base.FieldOrder;
import com.tencent.wxcloudrun.config.BusinessException;
import com.tencent.wxcloudrun.dao.ExamQuestionMapper;
import com.tencent.wxcloudrun.dto.ExamQuestionDTO;
import com.tencent.wxcloudrun.model.ExamQuestion;
import com.tencent.wxcloudrun.service.api.ExamQuestionService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.tencent.wxcloudrun.enums.ResponseEnum.*;

/**
 * 试卷表 Service 实现类
 */
@Service
public class ExamQuestionServiceImpl extends ServiceImpl<ExamQuestionMapper, ExamQuestion> implements ExamQuestionService {
    @Override
    public List<ExamQuestion> listByPage(ExamQuestionDTO.ExamListQuery examListQuery) {
        Page<ExamQuestion> examPage = new Page<>(examListQuery.getCurrent(), examListQuery.getSize());
        for (FieldOrder order : examListQuery.getOrders()) {
            if ("asc".equals(order.getOrder())) {
                examPage.addOrder(OrderItem.asc(order.getField()));
            } else if ("desc".equals(order.getOrder())) {
                examPage.addOrder(OrderItem.desc(order.getField()));
            }
        }
        QueryWrapper<ExamQuestion> objectQueryWrapper = new QueryWrapper<>();
        if (examListQuery.getServiceType() != null) {
            objectQueryWrapper.eq("service_type", examListQuery.getServiceType());
        }
        if (examListQuery.getType() != null) {
            objectQueryWrapper.eq("type", examListQuery.getType());
        }
        IPage<ExamQuestion> resultPage = this.getBaseMapper().selectPage(examPage, objectQueryWrapper);
        return resultPage.getRecords();
    }

    @Override
    public boolean save(List<ExamQuestion> examQuestions) {
        if (examQuestions == null || examQuestions.isEmpty()) {
          throw new BusinessException(EXAM_NOT_EXIST);
        }
        for (ExamQuestion examQuestion : examQuestions) {
            // 检查题干是否重复
            QueryWrapper<ExamQuestion> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("content", examQuestion.getContent());
            queryWrapper.eq("service_type", examQuestion.getServiceType());
            long count = this.count(queryWrapper);
            if (count > 0) {
                // 题干重复，不保存
                log.error("题干重复，不保存题目: " + examQuestion.getContent());
                throw new BusinessException(EXAM_COUNT_FAILED);
            }
            examQuestion.setCreateTime(System.currentTimeMillis());
            examQuestion.setUpdateTime(System.currentTimeMillis());
        }
        // 所有题干都不重复，保存题目
        return this.saveBatch(examQuestions);
    }

    @Override
    public boolean deleteExamQuestion(Long id) {
        return this.removeById(id);
    }

    @Override
    public List<ExamQuestion> generateQuestionBank(int serviceType) {
        List<ExamQuestion> questionBank = new ArrayList<>();

        // 获取10道单选题
        questionBank.addAll(getRandomQuestionsByType(serviceType, 1, 20));

        // 获取5道判断题
        questionBank.addAll(getRandomQuestionsByType(serviceType, 2, 20));

        // 获取5道多选题
        questionBank.addAll(getRandomQuestionsByType(serviceType, 3, 20));

        // 获取5道简答题
        questionBank.addAll(getRandomQuestionsByType(serviceType, 4, 20));

        // 其他类型题目，开放回答
        questionBank.addAll(getRandomQuestionsByType(serviceType, 5, 20));


        return questionBank;
    }

    private List<ExamQuestion> getRandomQuestionsByType(int serviceType, int type, int count) {
        QueryWrapper<ExamQuestion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type", type);
        queryWrapper.eq("service_type", serviceType);
        queryWrapper.orderByAsc("number");
        List<ExamQuestion> allQuestions = this.list(queryWrapper);
        if (allQuestions.size() < count) {
            log.error("题库中 " + type + " 类型题目不足，仅返回 " + allQuestions.size() + " 道");
            return allQuestions;
        }
        return allQuestions.subList(0, count);
    }
}
