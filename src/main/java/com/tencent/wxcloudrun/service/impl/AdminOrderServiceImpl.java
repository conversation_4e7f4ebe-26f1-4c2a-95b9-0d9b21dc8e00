package com.tencent.wxcloudrun.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.tencent.wxcloudrun.config.BusinessException;
import com.tencent.wxcloudrun.config.UserContextHolder;
import com.tencent.wxcloudrun.enums.OrderStatusEnum;
import com.tencent.wxcloudrun.model.*;
import com.tencent.wxcloudrun.response.*;
import com.tencent.wxcloudrun.service.api.*;
import com.tencent.wxcloudrun.vo.AdminOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.tencent.wxcloudrun.enums.OrderStatusEnum.*;
import static com.tencent.wxcloudrun.enums.ResponseEnum.*;

@Slf4j
@Service
public class AdminOrderServiceImpl implements AdminOrderService {

    @Autowired
    private IWxUsersService usersService;

    @Autowired
    private IOrdersService ordersService;


    @Autowired
    private UserOrderService userOrderService;
    @Autowired
    private UserWalletService walletService;


    //    @Value("${wx.subMchId}")
    private String SUB_MCH_ID = "1713570828";


    @Override
    public Page<UserOrderRes> ordersPage(AdminOrderVO.OrderPage orderPageVO) {
        Page<UserOrderRes> ordersPageRes = new Page<>();
        List<String> userIdList = Lists.newArrayList();
        if (StrUtil.isNotEmpty(orderPageVO.getMobile())) {
            List<WxUsers> byLikeMobile = usersService.getByLikeMobile(orderPageVO.getMobile());
            if (CollectionUtil.isNotEmpty(byLikeMobile)) {
                ordersPageRes.setRecords(Lists.newArrayList());
                return ordersPageRes;
            }
            List<String> userIds = byLikeMobile.stream().map(WxUsers::getOpenid)
                    .distinct().collect(Collectors.toList());
            userIdList.addAll(userIds);
        }
        if (StrUtil.isNotEmpty(orderPageVO.getNickName())) {

            List<WxUsers> byLikeNikeName = usersService.getByLikeNikeName(orderPageVO.getNickName());
            if (CollectionUtil.isNotEmpty(byLikeNikeName)) {
                ordersPageRes.setRecords(Lists.newArrayList());
                return ordersPageRes;
            }
            List<String> userIds = byLikeNikeName.stream().map(WxUsers::getOpenid)
                    .distinct().collect(Collectors.toList());
            userIdList.addAll(userIds);

        }
        orderPageVO.setUserIdList(userIdList);
        Page<Orders> ordersPage = ordersService.ordersPage(orderPageVO);
        BeanUtil.copyProperties(ordersPage, ordersPageRes);

        List<Orders> orderList = ordersPage.getRecords();
        if (CollectionUtils.isEmpty(orderList)) {
            ordersPageRes.setRecords(Lists.newArrayList());
            return ordersPageRes;
        }
        List<UserOrderRes> orderResList = userOrderService.buildOrderResult(orderList);
        ordersPageRes.setRecords(orderResList);
        return ordersPageRes;
    }

    /**
     * 退款
     *
     * @param orderNo    订单号
     * @param cancelFlag 是否取消
     * @return 结果
     */
    @Transactional
    @Override
    public boolean cancel(String orderNo, boolean cancelFlag) {
        return userOrderService.cancel(orderNo, cancelFlag);

    }

    /**
     * 后台更换喂养员
     *
     * @param orderServiceUser 喂养员信息
     * @return 是否成功
     */
    @Override
    public Boolean changeServiceUser(AdminOrderVO.OrderServiceUser orderServiceUser) {
        Orders byOrderNo = checkOrderInfo(orderServiceUser.getOrderNo());
        byOrderNo.setServiceUserId(orderServiceUser.getServiceUserId());
        byOrderNo.setOrdersStatus(PENDING_SERVICE.getStatusCode());
        byOrderNo.setUpdateTime(System.currentTimeMillis());
        return ordersService.saveOrUpdate(byOrderNo);
    }


    @Override
    public boolean orderUnfreeze(String orderNo) {
        Orders orders = ordersService.getByOrderNo(orderNo);
        if (Objects.isNull(orders)) {
            throw new BusinessException(ORDER_INFO_ERROR);
        }
        // 校验订单状态
        if (orders.getWithdrawalStatus() ==null  ||  orders.getWithdrawalStatus() != 1) {
            throw new BusinessException(ORDER_INFO_ERROR);
        }
        Long servicePrice = orders.getServicePrice();
        String userId = orders.getServiceUserId();



        String distributeUserId = orders.getDistributeUserId();
        walletService.updaterReleaseAmountByUserId(userId, servicePrice);
        if (!StrUtil.isEmpty(distributeUserId)) {
            Long distributorPrice = orders.getDistributorPrice();
            walletService.updaterReleaseAmountByUserId(distributeUserId, distributorPrice);
        }
        orders.setWithdrawalStatus(2);
        orders.setOrdersStatus(SETTLEMENT.getStatusCode());
        return ordersService.updateById(orders);
    }
    /**
     * 后台取消喂养员
     *
     * @param orderNo 订单信息
     * @return 是否成功
     */
    @Override
    public Boolean cancelServiceUser(String orderNo) {
        return ordersService.update().eq("order_no", orderNo)
                .set("service_user_id", null)
                .set("orders_status", PENDING_ACCEPTANCE.getStatusCode())
                .set("update_time", System.currentTimeMillis()).update();

    }

    private Orders checkOrderInfo(String orderNo) {
        Orders byOrderNo = ordersService.getByOrderNo(orderNo);
        if (byOrderNo == null) {
            throw new BusinessException(ORDER_INFO_ERROR);
        }
        if (byOrderNo.getOrdersStatus() != PENDING_SERVICE.getStatusCode() && byOrderNo.getOrdersStatus() != IN_PROGRESS.getStatusCode()) {
            throw new BusinessException(ORDER_NOT_SERVICE_USER_ERROR);
        }
        return byOrderNo;

    }
}
