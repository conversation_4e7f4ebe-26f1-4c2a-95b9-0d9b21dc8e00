package com.tencent.wxcloudrun.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.tencent.wxcloudrun.config.BusinessException;
import com.tencent.wxcloudrun.config.UserContextHolder;
import com.tencent.wxcloudrun.dto.TransferBillsDTO;
import com.tencent.wxcloudrun.dto.UserOrdersDTO;
import com.tencent.wxcloudrun.dto.WorkerOrdersDTO;
import com.tencent.wxcloudrun.enums.OrderStatusEnum;
import com.tencent.wxcloudrun.model.*;
import com.tencent.wxcloudrun.response.*;
import com.tencent.wxcloudrun.service.api.*;
import com.tencent.wxcloudrun.utils.AddressUtil;
import com.tencent.wxcloudrun.utils.ShortSnowflakeId;
import com.tencent.wxcloudrun.utils.TimeUtil;
import com.tencent.wxcloudrun.vo.OrderAmountUpdateVo;
import com.tencent.wxcloudrun.vo.OrderWarp;
import com.tencent.wxcloudrun.vo.WechatPayNotifyVo;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayWithRequestPaymentResponse;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.profitsharing.model.CreateOrderReceiver;
import com.wechat.pay.java.service.profitsharing.model.ReceiverType;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.tencent.wxcloudrun.enums.OrderStatusEnum.*;
import static com.tencent.wxcloudrun.enums.ResponseEnum.*;
import static com.tencent.wxcloudrun.utils.AddressUtil.hideBuildingSimple;
import static com.tencent.wxcloudrun.utils.PriceUtil.*;

@Slf4j
@Service
public class UserOrderServiceImpl implements UserOrderService {

    @Autowired
    private IServiceContentService serviceContentService;

    @Autowired
    private ICouponsService couponsService;

    @Autowired
    private IWxUsersService usersService;

    @Autowired
    private IUserCouponsService userCouponsService;

    @Autowired
    private IOrdersService ordersService;

    @Autowired
    private WeChatPayService wechatPayService;

    @Autowired
    private IPetsService petsService;

    @Autowired
    private IAddressesService addressesService;


    @Autowired
    private UserWalletService userWalletService;

    @Autowired
    private IUserSheetService userSheetService;
    @Autowired
    private UserWithdrawnRecordService userRecordService;

    @Value("${wx.subMchId}")
    private String SUB_MCH_ID;

    @Autowired
    private WorkerCertificationService workerCertificationService;

    @Override
    public ServiceUserPermissions queryPermissions(String serviceUserId) {
        WxUsers byOpenId = usersService.getByOpenId(serviceUserId);
        if (Objects.isNull(byOpenId)) {
            log.warn("认证未通过，用户信息:{}", UserContextHolder.getCurrentUserLog());
            return ServiceUserPermissions.builder().serviceUserId(serviceUserId).certified(false).sheetType(false).serviceType(new ArrayList<>()).build();
        }
        List<Integer> serviceType = workerCertificationService.getWorkerType(serviceUserId);
        boolean certified = byOpenId.getIsCertified() == 1;
        boolean sheetType = userSheetService.queryByUserIdStatus(serviceUserId);
        return ServiceUserPermissions.builder().serviceUserId(serviceUserId).sheetType(sheetType).certified(certified).serviceType(serviceType).build();
    }

    @Override
    public ServiceUserIncomeRes income(String serviceUserId) {
        UserWallet walletByUserId = userWalletService.getWalletByUserId(serviceUserId);
        if (Objects.isNull(walletByUserId)) {
            return ServiceUserIncomeRes.builder()
                    .userId(serviceUserId)
                    .totalAmount(0L)
                    .freezeAmount(0L)
                    .releaseAmount(0L)
                    .withdrawnAmount(0L)
                    .build();
        }
        return ServiceUserIncomeRes.builder()
                .userId(serviceUserId)
                .totalAmount(walletByUserId.getTotalAmount())
                .freezeAmount(walletByUserId.getFreezeAmount())
                .releaseAmount(walletByUserId.getReleaseAmount())
                .withdrawnAmount(walletByUserId.getWithdrawnAmount())
                .build();

    }

    @Override
    public Page<SearchServiceUser> searchServiceUsers(UserOrdersDTO.SearchServiceUserCondition dto) {
        // 获取认证用户
        List<WorkerCertification> usersWorker = workerCertificationService.workerServiceType(dto.getServiceType());
        if (CollectionUtils.isEmpty(usersWorker)) return createEmptyPage(dto.getCurrent(), dto.getSize());

        // 获取用户ID列表
        Set<String> userIdList = usersWorker.stream().map(WorkerCertification::getUserId).collect(Collectors.toSet());

        // 先获取所有符合条件的用户（不分页）
        List<WxUsers> allUsers = usersService.lambdaQuery()
                .in(WxUsers::getOpenid, userIdList)
                .list();
        if (CollectionUtils.isEmpty(allUsers)) return createEmptyPage(dto.getCurrent(), dto.getSize());
        // 先排序：按照距离或评分排序
        List<WxUsers> sortedUsers;
        if (dto.isOrderByDistance()) {
            sortedUsers = allUsers.stream().sorted(Comparator.comparingLong(value ->
                    AddressUtil.calDistance(value.getLatitude(), value.getLongitude(), dto.getLatitude(), dto.getLongitude())
            )).collect(Collectors.toList());
        } else {
            // 按照评分倒序排序
            sortedUsers = allUsers.stream().sorted(Comparator.comparingLong(WxUsers::getRating).reversed()).collect(Collectors.toList());
        }

        // 手动分页：计算分页参数
        long current = dto.getCurrent();
        long size = dto.getSize();
        long total = sortedUsers.size();
        long pages = (total + size - 1) / size; // 向上取整

        // 计算当前页的数据范围
        int start = (int) ((current - 1) * size);
        int end = (int) Math.min(start + size, total);

        List<WxUsers> pagedUsers = new ArrayList<>();
        if (start < total) {
            pagedUsers = sortedUsers.subList(start, end);
        }

        // 转换为返回对象
        List<SearchServiceUser> searchServiceUsers = pagedUsers.stream().map(val ->
                SearchServiceUser.builder()
                        .serviceUserName(val.getNickname())
                        .serviceUserId(val.getOpenid())
                        .serviceUserUrl(val.getCustomUrl())
                        .serviceUserScore(val.getRating())
                        .latitude(val.getLatitude())
                        .longitude(val.getLongitude())
                        .serviceUserScore(val.getRating())
                        .serviceUserDistance(String.valueOf(AddressUtil.calDistance(val.getLatitude(), val.getLongitude(), dto.getLatitude(), dto.getLongitude())))
                        .serviceUserCount(val.getServiceCount())
                        .build()
        ).collect(Collectors.toList());

        // 构建分页结果
        Page<SearchServiceUser> result = new Page<>(current, size);
        result.setRecords(searchServiceUsers);
        result.setTotal(total);
        result.setPages(pages);
        return result;
    }

    /**
     * 根据传入的类型创建并返回一个空的分页对象
     *
     * @param <T>      任意类型
     * @param cursor 分页起始ID
     * @param pageSize 分页大小
     * @return 返回一个空的分页对象
     */
    @Nullable
    private <T> Page<T> createEmptyPage(long cursor, long pageSize) {
        Page<T> result = new Page<>(cursor, pageSize);
        result.setRecords(new ArrayList<>());
        result.setTotal(0);
        return result;
    }


    @Override
    public Page<ServiceOrderRes> search(WorkerOrdersDTO.SearchCondition dto) {
        Page<Orders> orderPage = ordersService.workerOrdersPage(dto);
        List<Orders> orders = orderPage.getRecords();
        if (CollectionUtils.isEmpty(orders)) return createEmptyPage(dto.getCurrent(), dto.getSize());

        // 获取地址信息用于距离和区域过滤
        List<Long> addressIds = orders.stream().map(Orders::getAddressId).distinct().collect(Collectors.toList());
        Map<Long, Addresses> addressId2Info = addressesService.listByIds(addressIds).stream()
                .collect(Collectors.toMap(Addresses::getId, Function.identity()));
        // 应用距离和区域过滤
        List<Orders> filteredOrders = orders.stream().filter(order -> {
            Addresses address = addressId2Info.get(order.getAddressId());
            if (address == null) return true;
            // 区域过滤
            if (StrUtil.isNotEmpty(dto.getDistrict()) && !dto.getDistrict().equals(address.getDistrict())) {
                return false;
            }
            // 距离过滤
            if (dto.getDistance() != null && dto.getDistance() > 1 && dto.getLatitude() != null && dto.getLongitude() != null) {
                long distance = AddressUtil.calDistance(address.getLatitude(), address.getLongitude(), dto.getLatitude(), dto.getLongitude());
                return distance <= dto.getDistance();
            }
            return true;
        }).collect(Collectors.toList());

        // 获取宠物信息
        List<Long> petIds = filteredOrders.stream().map(Orders::getPetId).distinct().collect(Collectors.toList());
        Map<Long, Pets> petId2Info = petsService.getByIds(petIds).stream()
                .collect(Collectors.toMap(Pets::getId, Function.identity()));

        // 转换为返回对象
        List<ServiceOrderRes> serviceOrderRes = filteredOrders.stream().map(val ->
                wrap(val, petId2Info.getOrDefault(val.getPetId(), new Pets()),
                        addressId2Info.getOrDefault(val.getAddressId(), new Addresses()), true)
        ).collect(Collectors.toList());

        // 构建分页结果
        Page<ServiceOrderRes> result = new Page<>(orderPage.getCurrent(), orderPage.getSize());
        result.setRecords(serviceOrderRes);
        result.setTotal(orderPage.getTotal());
        result.setPages(orderPage.getPages());

        return result;
    }


    @Override
    public ServiceOrderDetailRes queryByOrderNo(String orderNo, String serviceUserId, int source) {
        Orders order = ordersService.getByOrderNo(orderNo);
        if (Objects.isNull(order)) {
            throw new BusinessException(ORDER_INFO_ERROR);
        }
        Pets pets = petsService.getById(order.getPetId());
        if (Objects.isNull(pets)) {
            throw new BusinessException(ORDER_INFO_ERROR);
        }
        Addresses addresses = addressesService.getById(order.getAddressId());
        ServiceOrderDetailRes detailRes = ServiceOrderDetailRes.builder()
                .orderNo(order.getOrderNo())
                .serviceAddress(addresses.getFullAddress())
                .serviceContent("固定，待补充")
                .serviceTimes(JSONUtil.toList(order.getServiceTime(), String.class))
                .petUrl(pets.getPetsUrl())
                .petName(pets.getName())
                .serviceType(order.getServiceType())
                .handover(new UserOrdersDTO.Handover(order.getHandoverType(), order.getHandover()))
                .remarks(order.getRemarks())
                .createTime(order.getCreateTime())
                .pets(pets)
                .servicePrice(order.getServicePrice())
                .rewardPrice(calRewardPrice(order.getFinalPrice(), order.getDistributorPrice(), order.getServicePrice()))
                .build();
        return detailRes;
    }

    @Override
    public UserOrderDetailRes queryByOrderNo(String orderNo, String userId) {
        Orders order = ordersService.getByOrderNo(orderNo);
        List<ServiceContent> serviceContents = serviceContentService.queryByOrderNo(orderNo, 2);
        if (Objects.isNull(order) || !order.getUserId().equals(userId)) {
            log.error("订单异常，用户信息:{}", UserContextHolder.getCurrentUserLog());
            throw new BusinessException(ORDER_INFO_ERROR);
        }
        return getUserOrderDetailRes(order, serviceContents);
    }


    @Override
    public UserOrderDetailRes queryByOrderNo(String orderNo) {
        Orders order = ordersService.getByOrderNo(orderNo);
        List<ServiceContent> serviceContents = serviceContentService.queryByOrderNo(orderNo, 2);
        if (Objects.isNull(order)) {
            log.error("订单异常，用户信息:{}", UserContextHolder.getCurrentUserLog());
            throw new BusinessException(ORDER_INFO_ERROR);
        }
        return getUserOrderDetailRes(order, serviceContents);
    }

    private UserOrderDetailRes getUserOrderDetailRes(Orders order, List<ServiceContent> serviceContents) {
        Pets pets = petsService.getById(order.getPetId());
        if (Objects.isNull(pets)) {
            log.error("宠物异常，用户信息:{}", UserContextHolder.getCurrentUserLog());
            throw new BusinessException(PETS_INFO_FAILED);
        }
        List<Orders> serviceUserOrders = ordersService.getByServiceUserId(order.getServiceUserId());
        WxUsers userInfo = usersService.getByOpenId(order.getServiceUserId());
        Addresses addresses = addressesService.getById(order.getAddressId());
        UserOrderDetailRes detailRes = UserOrderDetailRes.builder()
                .orderNo(order.getOrderNo())
                .serviceAddress(addresses.getFullAddress())
                .serviceContent("固定，待补充")
                .serviceReport(serviceContents)
                .serviceTimes(JSONUtil.toList(order.getServiceTime(), String.class))
                .petUrl(pets.getPetsUrl())
                .petName(pets.getName())
                .serviceType(order.getServiceType())
                .handover(new UserOrdersDTO.Handover(order.getHandoverType(), order.getHandover()))
                .remarks(order.getRemarks())
                .createTime(order.getCreateTime())
                .totalPrice(order.getOriginalPrice())
                .payPrice(order.getFinalPrice())
                .discountPrice(order.getOriginalPrice() - order.getFinalPrice())
                .serviceUserId("")
                .status(order.getOrdersStatus())
                .serviceUserName("")
                .serviceUserScore(0)
                .serviceCount(0)
                .build();
        if (Objects.nonNull(userInfo)) {
            detailRes.setServiceUserId(order.getServiceUserId());
            detailRes.setServiceUserName(userInfo.getNickname());
            detailRes.setServiceCustomUrl(userInfo.getCustomUrl());
            if (CollectionUtils.isEmpty(serviceUserOrders)) {
                detailRes.setServiceUserScore(46);
                detailRes.setServiceCount(10);
            } else {
                List<Integer> collect = serviceUserOrders.stream().map(Orders::getServiceUserScore)
                        .filter(Objects::nonNull).collect(Collectors.toList());
                int sum = collect.stream()
                        .mapToInt(Integer::intValue)
                        .sum();
                if (collect.isEmpty() || sum == 0) {
                    detailRes.setServiceUserScore(46);
                } else {
                    detailRes.setServiceUserScore(sum / collect.size());
                }

                detailRes.setServiceCount(10 + serviceUserOrders.size());
            }
        }

        return detailRes;
    }

    @Override
    public Page<ServiceOrderRes> query(WorkerOrdersDTO.OrderListQuery listQuery) {
        // 创建分页对象，使用DTO中的分页参数
        Page<Orders> page = new Page<>(listQuery.getCurrent(), listQuery.getSize());

        List<Integer> orderStatus = getUserAccomplishStatues(listQuery.getOrderStatus());

        // 构建查询条件
        LambdaQueryWrapper<Orders> queryWrapper = new LambdaQueryWrapper<Orders>()
                .in(Orders::getOrdersStatus, orderStatus)
                .eq(StrUtil.isNotEmpty(listQuery.getUserId()), Orders::getServiceUserId, listQuery.getUserId())
                .orderByDesc(Orders::getCreateTime);

        // 使用MyBatis-Plus分页查询
        Page<Orders> orderPage = ordersService.page(page, queryWrapper);
        List<Orders> orderList = orderPage.getRecords();

        if (CollectionUtils.isEmpty(orderList)) return createEmptyPage(listQuery.getCurrent(), listQuery.getSize());

        // 获取宠物信息
        List<Long> petIds = orderList.stream().map(Orders::getPetId).distinct().collect(Collectors.toList());
        Map<Long, Pets> petId2Info = petsService.getByIds(petIds).stream()
                .collect(Collectors.toMap(Pets::getId, Function.identity()));

        // 获取地址信息
        List<Long> addressIds = orderList.stream().map(Orders::getAddressId).distinct().collect(Collectors.toList());
        Map<Long, Addresses> addressId2Info = addressesService.listByIds(addressIds).stream()
                .collect(Collectors.toMap(Addresses::getId, Function.identity()));

        // 获取服务内容
        List<String> orderNoList = orderList.stream().map(Orders::getOrderNo).distinct().collect(Collectors.toList());
        Map<String, List<ServiceContent>> orderId2ServiceContent = serviceContentService.queryByOrderNoList(orderNoList, null);

        // 转换为返回对象
        List<ServiceOrderRes> serviceOrderRes = orderList.stream().map(order -> {
            OrderWarp orderWarp = OrderWarp.builder()
                    .addresses(addressId2Info.getOrDefault(order.getAddressId(), new Addresses()))
                    .pets(petId2Info.getOrDefault(order.getPetId(), new Pets()))
                    .serviceReport(orderId2ServiceContent.getOrDefault(order.getOrderNo(), new ArrayList<>()))
                    .order(order)
                    .build();
            return wrap(orderWarp);
        }).collect(Collectors.toList());

        // 构建分页结果
        Page<ServiceOrderRes> result = new Page<>(orderPage.getCurrent(), orderPage.getSize());
        result.setRecords(serviceOrderRes);
        result.setTotal(orderPage.getTotal());
        result.setPages(orderPage.getPages());

        return result;
    }


    @Override
    public List<UserOrderRes> buildOrderResult(List<Orders> orderList) {
        List<Long> petIds = orderList.stream().map(Orders::getPetId)
                .distinct().collect(Collectors.toList());
        List<String> allUserIds = orderList.stream()
                .flatMap(order -> Stream.of(order.getUserId(), order.getServiceUserId()))
                .distinct()
                .collect(Collectors.toList());

        Map<Long, Pets> petId2Info = petsService.getByIds(petIds).stream().collect(Collectors.toMap(
                Pets::getId, Function.identity()
        ));
        Map<String, WxUsers> userId2Info = usersService.getByOpenIds(allUserIds).stream().collect(Collectors.toMap(
                WxUsers::getOpenid, Function.identity()
        ));

        Map<String, String> openId2Name = usersService.getByOpenIds(allUserIds).stream().collect(Collectors.toMap(
                WxUsers::getOpenid, WxUsers::getNickname
        ));
        List<Long> addressIds = orderList.stream().map(Orders::getAddressId)
                .distinct().collect(Collectors.toList());
        Map<Long, Addresses> addressId2Info = addressesService.listByIds(addressIds).stream().collect(Collectors.toMap(
                Addresses::getId, Function.identity()
        ));
        List<String> orderNoList = orderList.stream().map(Orders::getOrderNo)
                .distinct().collect(Collectors.toList());
        Map<String, List<ServiceContent>> orderId2ServiceContent = serviceContentService.queryByOrderNoList(orderNoList, 2);

        return orderList.stream().map(order -> {
            return UserOrderRes.builder()
                    .orderNo(order.getOrderNo())
                    .serviceType(order.getServiceType())
                    .userScore(order.getUserScore())
                    .serviceUserScore(order.getServiceUserScore())
                    .serviceAddress(addressId2Info.getOrDefault(order.getAddressId(), new Addresses()).getFullAddress())
                    .serviceReport(getServiceReport(orderId2ServiceContent.getOrDefault(order.getOrderNo(), new ArrayList<>())))
                    .payAmount(order.getFinalPrice())
                    .servicePrice(order.getServicePrice())
                    .distributorPrice(order.getDistributorPrice())
                    .serviceTimes(JSONUtil.toList(order.getServiceTime(), String.class))
                    .pets(petId2Info.getOrDefault(order.getPetId(), new Pets()))
                    .petUrl(petId2Info.getOrDefault(order.getPetId(), new Pets()).getPetsUrl())
                    .petName(petId2Info.getOrDefault(order.getPetId(), new Pets()).getName())
                    .serviceUserId(order.getServiceUserId())
                    .userId(order.getUserId())
                    .serviceUserName(openId2Name.getOrDefault(order.getServiceUserId(), "未接单"))
                    .userName(openId2Name.getOrDefault(order.getUserId(), "测试订单！"))
                    .status(order.getOrdersStatus())
                    .withdrawalStatus(order.getWithdrawalStatus())
                    .userMobile(addressId2Info.getOrDefault(order.getAddressId(), new Addresses()).getContactPhone())
                    .serviceMobile(userId2Info.getOrDefault(order.getUserId(), new WxUsers()).getMobile())
                    .build();
        }).collect(Collectors.toList());


    }


    private static List<ServiceContent> getServiceReport(List<ServiceContent> serviceReport) {
        return serviceReport.stream().peek(serviceContent -> {
            if (serviceContent.getServiceContentStatus() == 1) {
                serviceContent.setServiceContentStatusValue("已上传");
            }
            if (serviceContent.getServiceContentStatus() == 2) {
                serviceContent.setServiceContentStatusValue("审核成功");
            }
            if (serviceContent.getServiceContentStatus() == 3) {
                serviceContent.setServiceContentStatusValue("审核失败");
            }
        }).collect(Collectors.toList());
    }

    private static ServiceOrderRes wrap(OrderWarp orderWarp) {
        Addresses addresses = orderWarp.getAddresses();
        Pets pets = orderWarp.getPets();
        Orders order = orderWarp.getOrder();
        List<ServiceContent> serviceReport = orderWarp.getServiceReport();

        return ServiceOrderRes.builder()
                .orderNo(order.getOrderNo())
                .serviceAddress(addresses.getFullAddress())
                .serviceContent("固定，待补充")
                .serviceTimes(JSONUtil.toList(order.getServiceTime(), String.class))
                .servicePrice(order.getServicePrice())
                .serviceType(order.getServiceType())
                .petUrl(pets.getPetsUrl())
                .status(order.getOrdersStatus())
                .accountedTime(order.getAccountedTime())
                .userScore(order.getUserScore())
                .serviceUserScore(order.getServiceUserScore())
                .serviceReport(getServiceReport(serviceReport))
                .rewardPrice(calRewardPrice(order.getFinalPrice(), order.getDistributorPrice(), order.getServicePrice()))
                .build();
    }

    private static ServiceOrderRes wrap(Orders order, Pets pets, Addresses addresses, boolean isAdmin) {
        String serviceAddress = addresses.getFullAddress();
        if (isAdmin) {
            serviceAddress = hideBuildingSimple(serviceAddress);
        }
        addresses.setFullAddress(serviceAddress);
        OrderWarp orderWarp = OrderWarp.builder().addresses(addresses)
                .pets(pets)
                .serviceReport(new ArrayList<>())
                .order(order).build();
        return wrap(orderWarp);
    }

    @Override
    public Page<UserOrderRes> worderQuery(UserOrdersDTO.OrderListQuery listQuery) {
        // 创建分页对象，使用DTO中的分页参数
        Page<Orders> page = new Page<>(listQuery.getCurrent(), listQuery.getSize());
        List<Integer> orderStatus = getUserAccomplishStatues(listQuery.getOrderStatus());

        // 构建查询条件
        LambdaQueryWrapper<Orders> queryWrapper = new LambdaQueryWrapper<Orders>()
                .in(Orders::getOrdersStatus, orderStatus)
                .eq(StrUtil.isNotEmpty(listQuery.getUserId()), Orders::getUserId, listQuery.getUserId())
                .orderByDesc(Orders::getCreateTime);

        // 使用MyBatis-Plus分页查询
        Page<Orders> orderPage = ordersService.page(page, queryWrapper);
        List<Orders> orderList = orderPage.getRecords();

        if (CollectionUtils.isEmpty(orderList)) return createEmptyPage(listQuery.getCurrent(), listQuery.getSize());

        // 构建订单结果
        List<UserOrderRes> userOrderRes = buildOrderResult(orderList);

        // 构建分页结果
        Page<UserOrderRes> result = new Page<>(orderPage.getCurrent(), orderPage.getSize());
        result.setRecords(userOrderRes);
        result.setTotal(orderPage.getTotal());
        result.setPages(orderPage.getPages());

        return result;
    }


    @Override
    public long calPayPrice(UserOrdersDTO.CreateOrderVO createOrderVO) {
        // 校验日期
        if (CollectionUtils.isEmpty(createOrderVO.getServiceTime())) {
            log.error("日期校验失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
            return 0L;
        }
        // 校验优惠券匹配规则
        if (!validaUserCouponIds(createOrderVO.getCouponIds())) {
            log.error("优惠券校验失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
            return 0L;
        }
        // 校验用户信息
        String userId = createOrderVO.getUserId();
        String serviceUserId = createOrderVO.getServiceUserId();
        Map<String, WxUsers> openId2UserInfo = usersService.getByOpenIds(
                Lists.newArrayList(userId, serviceUserId)).stream().collect(Collectors.toMap(
                WxUsers::getOpenid, Function.identity()));
        if (openId2UserInfo.isEmpty()) {
            log.error("用户校验失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
            log.error("failed to get userInfo, userId = {}, serviceUserId = {}", userId, serviceUserId);
            return 0L;
        }

        // 校验优惠券信息
        List<UserCoupons> userCouponsList = userCouponsService.getByOpenIdAndCouponIds(userId, createOrderVO.getCouponIds());
        if (createOrderVO.getCouponIds() != null && !createOrderVO.getCouponIds().isEmpty()) {
            if (userCouponsList.size() != createOrderVO.getCouponIds().size()) {
                log.error("优惠券校验失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
                log.error("failed to get userCoupons,size userId = {}, serviceUserId = {}", userId, serviceUserId);
                return 0L;
            }
            if (userCouponsList.stream().noneMatch(UserOrderServiceImpl::validateUserCoupon)) {
                log.error("优惠券校验失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
                log.error("failed to get validateUserCoupon, userId = {}, serviceUserId = {}", userId, serviceUserId);
                return 0L;
            }
        }
        // 获取优惠券信息
        // 获取优惠券信息
        List<Coupons> coupons = createOrderVO.getCouponIds() == null || createOrderVO.getCouponIds().isEmpty()
                ? new ArrayList<>()
                : couponsService.listByIds(createOrderVO.getCouponIds());

        // 计算总价
        return calCreateOrderPrice(coupons,
                createOrderVO.getServiceTime().size() * createOrderVO.getServicePrice(),
                createOrderVO.getServiceTime());
    }

    @Override
    public boolean reviewServiceUser(UserOrdersDTO.ServiceReview serviceReview) {
        Orders order = ordersService.getByOrderNo(serviceReview.getOrderNo());
        if (Objects.isNull(order) || !order.getUserId().equals(serviceReview.getUserId())) {
            throw new BusinessException(ORDER_INFO_ERROR);
        }
        order.setServiceUserReview(serviceReview.getReview());
        order.setServiceUserScore(serviceReview.getScore());
        order.setUpdateTime(System.currentTimeMillis());
        order.setOrdersStatus(EVALUATED.getStatusCode());
        return ordersService.updateById(order);
    }

    @Override
    @Transactional
    public UserOrderCreateRes createUserOrder(UserOrdersDTO.CreateOrderVO createOrderVO) {
        // 校验日期
        if (CollectionUtils.isEmpty(createOrderVO.getServiceTime())) {
            log.error("failed to get serviceTime");
            log.error("订单服务日期为空，用户信息:{}", UserContextHolder.getCurrentUserLog());
            throw new BusinessException(ORDER_SERVICE_TIME_FAILED);
        }
        // 校验优惠券匹配规则
        if (!validaUserCouponIds(createOrderVO.getCouponIds())) {
            log.error("校验优惠券匹配规则不匹配，用户信息:{}", UserContextHolder.getCurrentUserLog());
            throw new BusinessException(ORDER_COUPON_FAILED);
        }
        // 校验用户信息
        String userId = createOrderVO.getUserId();
        String serviceUserId = createOrderVO.getServiceUserId();
        List<WxUsers> users = usersService.getByOpenIds(Lists.newArrayList(userId, serviceUserId));
        // 下单不匹配
        if (users.stream().noneMatch(val -> val.getOpenid().equals(createOrderVO.getUserId()))) {
            log.error("下单不匹配，用户信息:{}", UserContextHolder.getCurrentUserLog());
            throw new BusinessException(ORDER_USER_ID_FAILED);
        }
        // 校验饲养员资格
        if (!StrUtil.isEmpty(createOrderVO.getServiceUserId())) {
            List<Integer> workerType = workerCertificationService.getWorkerType(createOrderVO.getServiceUserId());
            if (workerType == null || !workerType.contains(createOrderVO.getServiceType())) {
                log.error("没有接单权限，用户信息:{}", UserContextHolder.getCurrentUserLog());
                throw new BusinessException(ORDER_PERMISSION_FAILED);
            }

        }
        // 校验优惠券信息
        List<UserCoupons> userCouponsList = userCouponsService.getByOpenIdAndCouponIds(userId, createOrderVO.getCouponIds());
        if (!CollectionUtils.isEmpty(createOrderVO.getCouponIds()) && userCouponsList.size() != createOrderVO.getCouponIds().size()) {
            log.error("优惠券数量校验失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
            throw new BusinessException(ORDER_COUPON_NUM_FAILED);
        }
        if (!CollectionUtils.isEmpty(createOrderVO.getCouponIds()) && userCouponsList.stream().noneMatch(UserOrderServiceImpl::validateUserCoupon)) {
            log.error("优惠券已过期，用户信息:{}", UserContextHolder.getCurrentUserLog());
            throw new BusinessException(ORDER_COUPON_TIME_FAILED);
        }
        // 获取优惠券信息
        List<Coupons> coupons = createOrderVO.getCouponIds() == null || createOrderVO.getCouponIds().isEmpty()
                ? new ArrayList<>()
                : couponsService.listByIds(createOrderVO.getCouponIds());

        // 校验分销用户信息
        WxUsers wxUsers = users.stream().filter(val -> val.getOpenid().equals(createOrderVO.getUserId())).findAny().get();
        String distributorOpenId = wxUsers.getWasInviteCode();
        WxUsers distributorInfo = usersService.getByOpenId(distributorOpenId);
        boolean distribution = false;
        if (Objects.nonNull(distributorInfo)) {
            List<Orders> byDistributorId = ordersService.getByDistributorIdAndUserId(distributorOpenId, createOrderVO.getUserId());
            distribution = byDistributorId.size() < 3;
        }

        // 计算总价
        long payPrice = calCreateOrderPrice(coupons, createOrderVO.getServiceTime().size() * createOrderVO.getServicePrice(),
                createOrderVO.getServiceTime());
        if (payPrice <= 0) {
            if (CollectionUtils.isEmpty(coupons)) {
                log.error("订单价格计算失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
                throw new BusinessException(ORDER_PRICE_FAILED);
            }
            log.error("优惠券金额超过订单金额，用户信息:{}", UserContextHolder.getCurrentUserLog());
            throw new BusinessException(ORDER_PRICE_COUPONS_FAILED);
        }
        // 写数据库
        Orders orders = buildOrders(createOrderVO, payPrice, distribution, distributorOpenId);
        List<UserCoupons> updateUserCoupons = userCouponsList.stream().peek(userCoupons -> {
            userCoupons.setIsUsed(1);
            userCoupons.setUpdateTime(System.currentTimeMillis());
        }).collect(Collectors.toList());

        ordersService.save(orders, updateUserCoupons);

        // 调微信支付预支付接口
        PrepayWithRequestPaymentResponse prepay = wechatPayService.prepay(payPrice, orders.getOrderNo(), createOrderVO.getUserId());

        UserOrderCreateRes.UserOrderCreateResBuilder userBuilder = UserOrderCreateRes.builder();
        BeanUtil.copyProperties(prepay, userBuilder);
        return userBuilder
                .userOpenId(createOrderVO.getUserId())
                .orderNo(orders.getOrderNo())
                .appId(prepay.getAppId())
                .timestamp(prepay.getTimeStamp())
                .nonceStr(prepay.getNonceStr())
                .packageVal(prepay.getPackageVal())
                .signType(prepay.getSignType())
                .paySign(prepay.getPaySign())
                .build();
    }

    @Override
    public Map preCancel(String orderNo, String userId) {
        Map<String, Object> map = new HashMap<>();
        Orders order = ordersService.getByOrderNo(orderNo);
        if (Objects.isNull(order) || !order.getUserId().equals(userId)) {
            map.put("code", -1);
            map.put("msg", "订单不合法");
            log.error("订单不合法，用户信息:{}", UserContextHolder.getCurrentUserLog());
            throw new BusinessException(ORDER_INFO_ERROR);

        }
        if (order.getOrdersStatus() == REFUSE.getStatusCode()) {
            map.put("code", 0);
            map.put("msg", "已拒单, 可退款");
            return map;

        }
        if (order.getOrdersStatus() == PENDING_SERVICE.getStatusCode()) {
            List<String> serviceTime = JSONUtil.toList(order.getServiceTime(), String.class);
            Long minTime = serviceTime.stream().map(TimeUtil::convertDateToBeijingTimestamp)
                    .min(Comparator.comparingLong(Long::longValue))
                    .get();

            UserOrdersDTO.RatePay ratePay = calCompensationPrice(minTime);
            map.put("code", 0);
            map.put("msg", ratePay.getRatePayString());
            return map;
        }
        if (order.getOrdersStatus() == IN_PROGRESS.getStatusCode()
                || order.getOrdersStatus() == COMPLETED.getStatusCode()
                || order.getOrdersStatus() == REPORT_GENERATED.getStatusCode()
                || order.getOrdersStatus() == EVALUATED.getStatusCode()) {
            throw new BusinessException(REFUND_FAILED);

        }
        map.put("code", 0);
        map.put("msg", "可退款");
        return map;
    }

    @Override
    @Transactional
    public boolean cancel(String orderNo, String userId, String reason) {
        Orders order = ordersService.getByOrderNo(orderNo);
        if (Objects.isNull(order) || !order.getUserId().equals(userId)) {
            throw new BusinessException(ORDER_INFO_ERROR);
        }
        // 状态校验
        int ordersStatus = order.getOrdersStatus();
        validateOrderStatusForRefund(ordersStatus);
        String merchantTransactionRefundId = String.valueOf(ShortSnowflakeId.generate());
        if (ordersStatus == REFUSE.getStatusCode() || ordersStatus == PENDING_ACCEPTANCE.getStatusCode()) {
            if (reason == null) {
                reason = "用户取消";
            }
            return handleRefusedOrderRefund(order, merchantTransactionRefundId, reason);
        } else if (ordersStatus == PENDING_SERVICE.getStatusCode()) {
            return handlePendingServiceOrderRefund(order, merchantTransactionRefundId);
        } else {
            throw new BusinessException(ORDER_STATUS_FAILED);
        }

    }

    private void validateOrderStatusForRefund(int status) {
        if (status == IN_PROGRESS.getStatusCode()
                || status == COMPLETED.getStatusCode()
                || status == REPORT_GENERATED.getStatusCode()
                || status == EVALUATED.getStatusCode()) {
            throw new BusinessException(ORDER_STATUS_FAILED);
        }
    }


    @Transactional
    @Override
    public boolean cancel(String orderNo, boolean cancelFlag) {
        Orders order = ordersService.getByOrderNo(orderNo);
        if (Objects.isNull(order)) {
            throw new BusinessException(ORDER_INFO_ERROR);
        }
        String merchantTransactionRefundId = String.valueOf(ShortSnowflakeId.generate());
        boolean cancel = false;
        String reason = "管理员取消";
        if (cancelFlag) {
            cancel = handleRefusedOrderRefund(order, merchantTransactionRefundId, reason);
        } else {
            cancel = cancel(orderNo, order.getUserId(), reason);
        }
        return cancel;
    }

    private void logRefundFailure() {
        log.error("退款失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
    }

    private boolean handleRefusedOrderRefund(Orders order, String merchantTransactionRefundId, String reason) {
        order.setOrdersStatus(REFUND.getStatusCode());
        order.setUpdateTime(System.currentTimeMillis());
        order.setMerchantTransactionRefundId(merchantTransactionRefundId);
        Boolean result = wechatPayService.refundOrder(
                order.getOrderNo(), order.getFinalPrice(), order.getFinalPrice(), merchantTransactionRefundId, reason);
        if (!result) {
            logRefundFailure();
            throw new BusinessException(REFUND_FAILED);
        }
        ordersService.refund(order);
        return true;
    }

    private boolean handlePendingServiceOrderRefund(Orders order, String merchantTransactionRefundId) {
        List<String> serviceTime = JSONUtil.toList(order.getServiceTime(), String.class);
        Long minTime = serviceTime.stream()
                .map(TimeUtil::convertDateToBeijingTimestamp)
                .min(Comparator.comparingLong(Long::longValue))
                .orElseThrow(() -> new BusinessException(ORDER_TIME_ERROR));

        UserOrdersDTO.RatePay ratePay = calCompensationPrice(minTime);
        long refundPrice = order.getFinalPrice() - calRatePrice(order.getFinalPrice(), ratePay.getRate());

        order.setOrdersStatus(REFUND.getStatusCode());
        order.setUpdateTime(System.currentTimeMillis());
        order.setMerchantTransactionRefundId(merchantTransactionRefundId);

        Boolean result = wechatPayService.refundOrder(
                order.getOrderNo(), order.getFinalPrice(), refundPrice, merchantTransactionRefundId, "用户申请退款");
        if (!result) {
            logRefundFailure();
            throw new BusinessException(REFUND_FAILED);
        }

        ordersService.saveOrUpdate(order);
        return true;
    }


    private UserOrdersDTO.RatePay calCompensationPrice(long minTime) {
        long loneTime = System.currentTimeMillis();
        long oneHoursMillis = TimeUnit.HOURS.toMillis(12);
        long twoHoursMillis = TimeUnit.HOURS.toMillis(24);
        long threeHoursMillis = TimeUnit.HOURS.toMillis(48);
        long fourHoursMillis = TimeUnit.HOURS.toMillis(72);
        long rate = 0L;
        long lowTime = 0L;
        if (loneTime - minTime < oneHoursMillis) {
            rate = 80;
            lowTime = 12;
        } else if (loneTime - minTime < twoHoursMillis) {
            rate = 50;
            lowTime = 24;
        } else if (loneTime - minTime < threeHoursMillis) {
            rate = 20;
            lowTime = 48;
        } else {
            rate = 0;
            lowTime = 72;
        }
        UserOrdersDTO.RatePay ratePay = new UserOrdersDTO.RatePay();
        ratePay.setRate(rate);
        ratePay.setLowTime(lowTime);
        return ratePay;
    }

    @Override
    public void wechatPayNotify(String notifyData, HttpServletRequest httpServletRequest) {
        Transaction transaction = wechatPayService.weChatPayCallback(notifyData, httpServletRequest);
        if (Objects.isNull(transaction)) {
            log.error("微信回调失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
            return;
        }
        // 支付时间
        OffsetDateTime odt = OffsetDateTime.parse(transaction.getSuccessTime());
        long patTime = odt.toInstant().toEpochMilli();
        WechatPayNotifyVo vo = WechatPayNotifyVo.builder()
                .orderNo(transaction.getOutTradeNo())
                .payPrice(transaction.getAmount().getPayerTotal())
                .payUserOpenId(transaction.getPayer().getOpenid())
                .success(transaction.getTradeState() == Transaction.TradeStateEnum.SUCCESS)
                .patTime(patTime)
                .wxTransactionId(transaction.getTransactionId())
                .merchantNo(transaction.getMchid())
                .build();


        Orders order = ordersService.getByOrderNo(vo.getOrderNo());
        // 校验
        if (!validateWechatNotify(order, vo)) {
            log.error("订单校验失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
            return;
        }
        order.setOrdersStatus(OrderStatusEnum.PENDING_ACCEPTANCE.getStatusCode());
        order.setPayTime(vo.getPatTime());
        order.setWxTransactionId(vo.getWxTransactionId());
        order.setUpdateTime(System.currentTimeMillis());
        // 开始分单
        List<Orders> ordersList = spiltOrder(order);
        ordersService.create(order, ordersList);
    }

    @Override
    public boolean receiveOrder(String orderNo, String serviceOpenId, Long couponId) {
        if (couponId != null && couponId == 0) {
            couponId = null;
        }
        Orders order = ordersService.getByOrderNo(orderNo);
        List<Integer> workerType = workerCertificationService.getWorkerType(serviceOpenId);
        // 校验订单信息
        if (!validateReceiveOrder(order, serviceOpenId, OrderStatusEnum.PENDING_ACCEPTANCE.getStatusCode())) {
            log.error("订单校验失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
            return false;
        }
        WxUsers userInfo = usersService.getByOpenId(serviceOpenId);
        // 校验接单人信息
        if (!validateReceiveUser(userInfo, workerType, order.getServiceType())) {
            log.error("校验接单人失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
            throw new BusinessException(WORKER_ORDER_NOT_CERTIFIED);
        }
        if (couponId != null) {
            // 校验优惠券信息
            UserCoupons userCoupons = userCouponsService.getByOpenIdAndCouponId(serviceOpenId, couponId);
            if (!validateUserCoupon(userCoupons)) {
                log.error("校验优惠券失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
                return false;
            }
        }
        order.setDistributorPrice(0L);
        if (!StrUtil.isEmpty(userInfo.getWasInviteCode())) {
            long distributorPrice = calRatePrice(order.getOriginalPrice(), 10);
            order.setDistributorPrice(distributorPrice);
        }
        long serviceUserPrice = calServicePrice(order.getOriginalPrice(), couponId, order.getServiceTime());
        order.setServiceUserId(serviceOpenId);
        order.setServicePrice(serviceUserPrice);
        order.setReceiveTime(System.currentTimeMillis());
        if (couponId != null) {
            order.setServiceCouponIds(JSONUtil.toJsonStr(couponId));
        }
        order.setOrdersStatus(PENDING_SERVICE.getStatusCode());
        order.setUpdateTime(order.getReceiveTime());
        return ordersService.updateById(order);
    }

    @Override
    public boolean refuse(String orderNo, String serviceOpenId) {
        Orders order = ordersService.getByOrderNo(orderNo);
        if (!validateReceiveOrder(order, serviceOpenId, OrderStatusEnum.PENDING_ACCEPTANCE.getStatusCode())) {
            log.error("校验订单失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
            return false;
        }
        // 校验是不是同一个人
        if (order.getServiceUserId() == null || !order.getServiceUserId().equals(serviceOpenId)) {
            log.error("校验用户失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
            return false;
        }
        order.setOrdersStatus(OrderStatusEnum.REFUSE.getStatusCode());
        order.setUpdateTime(System.currentTimeMillis());
        return ordersService.updateById(order);
    }

    @Override
    public boolean begin(String orderNo, String serviceOpenId) {
        Orders order = ordersService.getByOrderNo(orderNo);
        if (!validateReceiveOrder(order, serviceOpenId, PENDING_SERVICE.getStatusCode())) {
            log.error("校验用户失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
            return false;
        }
        // 校验当前时间是否为可服务时间
        if (!validateServiceTime(order)) {
            log.error("校验服务时间，用户信息:{}", UserContextHolder.getCurrentUserLog());
            return false;
        }
        order.setBeginServiceTime(System.currentTimeMillis());
        order.setOrdersStatus(IN_PROGRESS.getStatusCode());
        order.setUpdateTime(System.currentTimeMillis());
        return ordersService.updateById(order);
    }

    @Override
    public boolean finish(String orderNo, String serviceOpenId) {
        Orders order = ordersService.getByOrderNo(orderNo);
        if (!validateReceiveOrder(order, serviceOpenId, IN_PROGRESS.getStatusCode())) {
            log.error("校验订单失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
            return false;
        }
        // 校验服务报告完整性
        List<ServiceContent> serviceContents = serviceContentService.queryByOrderNo(orderNo, null);
        if (CollectionUtils.isEmpty(serviceContents)) {
            log.error("服务报告不完整，用户信息:{}", UserContextHolder.getCurrentUserLog());
            return false;
        }
        List<String> serviceTimes = JSONUtil.toList(order.getServiceTime(), String.class);
        if (serviceTimes.size() != serviceContents.size()) {
            log.error("服务报告不匹配，用户信息:{}", UserContextHolder.getCurrentUserLog());
            return false;
        }
        order.setFinishTime(System.currentTimeMillis());
        order.setOrdersStatus(OrderStatusEnum.COMPLETED.getStatusCode());
        order.setUpdateTime(System.currentTimeMillis());
        return ordersService.updateById(order);
    }

    @Override
    public boolean reviewUser(WorkerOrdersDTO.ServiceUserReview dto) {
        Orders orders = ordersService.getByOrderNo(dto.getOrderNo());
        if (Objects.isNull(orders) || !orders.getServiceUserId().equals(dto.getServiceUserId())) {
            log.error("服务报告不匹配，用户信息:{}", UserContextHolder.getCurrentUserLog());
            return false;
        }
        orders.setUserScore(dto.getScore());
        orders.setUpdateTime(System.currentTimeMillis());
        return ordersService.updateById(orders);
    }

    @Override
    public List<String> preFenqian(OrderAmountUpdateVo vo) {
        List<String> res = new ArrayList<>();
        Orders orders = ordersService.getByOrderNo(vo.getOrderNo());
        if (orders.getOrdersStatus() != REPORT_GENERATED.getStatusCode()
                && orders.getOrdersStatus() != EVALUATED.getStatusCode()) {
            log.error("重复分账，用户信息:{}", UserContextHolder.getCurrentUserLog());
        }
        if (vo.getServiceUserPrice() != orders.getServicePrice()
                || vo.getDistributorPrice() != orders.getDistributorPrice()) {
            res.add("跟之前算出来的钱不一样，确定这样分就再点一下");
        }
        Long finalPrice = orders.getFinalPrice();
        if (finalPrice - vo.getDistributorPrice() < vo.getDistributorPrice()) {
            res.add("账户钱不够分，要给别人微信转账：" + (vo.getDistributorPrice() - (finalPrice - vo.getDistributorPrice())) + "分");
        }
        res.add("够分了，再点一下真分钱");
        return res;
    }

    @Override
    public boolean fenqian(OrderAmountUpdateVo vo) {
        Orders orders = ordersService.getByOrderNo(vo.getOrderNo());
        if (Objects.isNull(orders) || orders.getOrdersStatus() != REPORT_GENERATED.getStatusCode()
                && orders.getOrdersStatus() != EVALUATED.getStatusCode()) {
            log.error("订单校验失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
            return false;
        }
        String outOrderNo = String.valueOf(ShortSnowflakeId.generate());
        orders.setOrdersStatus(SETTLEMENT.getStatusCode());
        orders.setOutOrderNo(outOrderNo);
        orders.setUpdateTime(System.currentTimeMillis());
        ordersService.updateById(orders);
        List<CreateOrderReceiver> createOrderReceivers = new ArrayList<>();
        List<String> openIdList = Arrays.asList(orders.getServiceUserId(), orders.getDistributeUserId());
        for (String openId : openIdList) {
            if (StrUtil.isEmpty(openId)) continue;
            CreateOrderReceiver createOrderReceiver = new CreateOrderReceiver();
            createOrderReceiver.setType(ReceiverType.PERSONAL_OPENID.toString());
            createOrderReceiver.setAccount(openId);
            if (openId.equals(orders.getServiceUserId())) {
                createOrderReceiver.setAmount(orders.getServicePrice());
            } else {
                createOrderReceiver.setAmount(orders.getDistributorPrice());
            }
            createOrderReceivers.add(createOrderReceiver);
        }
        return wechatPayService.createOrder(orders.getWxTransactionId(), outOrderNo, createOrderReceivers);
    }

    @Override
    public boolean queryOrderPrice(OrderAmountUpdateVo vo) {
        Orders orders = ordersService.getByOrderNo(vo.getOrderNo());
        if (Objects.isNull(orders) || orders.getOrdersStatus() != REPORT_GENERATED.getStatusCode()
                && orders.getOrdersStatus() != EVALUATED.getStatusCode()) {
            return false;
        }
        return wechatPayService.queryOrder(orders.getWxTransactionId(), orders.getOutOrderNo());
    }

    @Override
    public List<Orders> getTimeAndStartOrders(Integer withdrawalStatus) {
        // 获取当前时区（可修改为指定时区如 ZoneId.of("Asia/Shanghai")）
        ZoneId zone = ZoneId.systemDefault();
        long sevenDaysAgo = LocalDate.now()
                .minusDays(6)
                .atStartOfDay(zone)
                .toInstant().toEpochMilli(); // 毫秒级
        return ordersService.getTimeAndStartOrders(sevenDaysAgo, withdrawalStatus);
    }


    @Override
    public TransferBillsDTO.TransferBillsResult userWithdrawal(String userId, Long settlementAmount) {
        UserWallet walletByUserId = userWalletService.getWalletByUserId(userId);
        if (Objects.isNull(walletByUserId)) {
            log.error("钱包余额不足，用户信息:{}", UserContextHolder.getCurrentUserLog());
            return TransferBillsDTO.TransferBillsResult.builder().failReason("钱包余额不足").build();
        }
        if (settlementAmount < 10) {
            log.error("提现金额需要大于0.1元，用户信息:{}", UserContextHolder.getCurrentUserLog());
            return TransferBillsDTO.TransferBillsResult.builder().failReason("提现金额需要大于0.1元").build();
        }
        if (walletByUserId.getReleaseAmount() < settlementAmount || settlementAmount > 20000) {
            log.error("超过200无法提现，用户信息:{}", UserContextHolder.getCurrentUserLog());
            return TransferBillsDTO.TransferBillsResult.builder().failReason("超过200无法提现").build();
        }
        String outBillNo = String.valueOf(ShortSnowflakeId.generate());
//        userWalletService.updaterWithdrawnAmountByUserId(settlementAmount, userId);
        TransferBillsDTO.TransferBillsResult transferBillsResult;
        try {
            transferBillsResult = wechatPayService.initiateTransferBills(outBillNo, userId, settlementAmount);
        } catch (Exception e) {
            log.error("提现失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
            log.error("提现失败！", e);
            return TransferBillsDTO.TransferBillsResult.builder().failReason("提现失败！").build();
        }
        userWalletService.updaterWithdrawnAmountByUserId(settlementAmount, userId);
        UserWithdrawnRecord userWithdrawnRecord = UserWithdrawnRecord.builder()
                .userId(userId)
                .withdrawnAmount(settlementAmount)
                .outBillNo(outBillNo)
                .createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis())
                .transferBillNo(transferBillsResult.getTransferBillNo())
                .build();
        userRecordService.save(userWithdrawnRecord);
        return transferBillsResult;
    }


    private static boolean validateServiceTime(Orders order) {
        List<String> serviceTime = JSONUtil.toList(order.getServiceTime(), String.class);
        Long minTime = serviceTime.stream().map(TimeUtil::convertDateToBeijingTimestamp)
                .min(Comparator.comparingLong(Long::longValue))
                .get();
        if (minTime - System.currentTimeMillis() > TimeUnit.HOURS.toMillis(12)) {
            log.error("服务时间校验失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
            return false;
        }
        return true;
    }

    private static boolean validateUserCoupon(UserCoupons userCoupons) {
        if (Objects.isNull(userCoupons)) {
            return false;
        }
        return userCoupons.getValidEnd() >= System.currentTimeMillis() && System.currentTimeMillis() >= userCoupons.getValidStart();
    }


    private static boolean validateReceiveUser(WxUsers userInfo, List<Integer> serviceTypeList, int serviceType) {
        if (Objects.isNull(userInfo)) {
            return false;
        }
        // 校验认证
        if (userInfo.getIsCertified() != 1) {
            throw new BusinessException(USER_NOT_CERTIFIED);
        }
        return !serviceTypeList.isEmpty() && serviceTypeList.contains(serviceType);
    }

    private static boolean validateReceiveOrder(Orders order, String serviceOpenId, int status) {
        if (Objects.isNull(order)) {
            throw new BusinessException(ORDER_INFO_ERROR);
        }
        if (order.getOrdersStatus() != status) {
            log.error("failed to valid receive order status, order = {}", JSONUtil.toJsonStr(order));
            log.error("订单状态校验失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
            throw new BusinessException(ORDER_STATUS_FAILED);

        }
        if (StringUtils.isBlank(order.getServiceUserId())) {
            return true;
        }
        return order.getServiceUserId().equals(serviceOpenId);
    }

    private boolean validateWechatNotify(Orders order, WechatPayNotifyVo vo) {
        if (Objects.isNull(order)) {
            log.error("订单信息错误，用户信息:{}", UserContextHolder.getCurrentUserLog());
            throw new BusinessException(ORDER_INFO_ERROR);
        }
        if (order.getOrdersStatus() != OrderStatusEnum.PENDING_PAYMENT.getStatusCode()) {
            log.error("订单信息错误，用户信息:{}", UserContextHolder.getCurrentUserLog());
            log.error("failed to valid order status, order = {}", JSONUtil.toJsonStr(order));
            throw new BusinessException(ORDER_INFO_ERROR);
        }
        // 校验商户id
        if (!vo.getMerchantNo().equals(SUB_MCH_ID)) {
            log.error("商户信息校验失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
            log.error("failed to valid merchantNo, order = {}, vo = {}", JSONUtil.toJsonStr(order), JSONUtil.toJsonStr(vo));
            return false;
        }
        // 校验支付用户的openId
        if (!vo.getPayUserOpenId().equals(order.getUserId())) {
            log.error("用户id校验失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
            log.error("failed to valid openId, order = {}, vo = {}", JSONUtil.toJsonStr(order), JSONUtil.toJsonStr(vo));
            return false;
        }
        // 校验支付金额
        if (vo.getPayPrice() != order.getFinalPrice()) {
            log.error("支付金额校验失败，用户信息:{}", UserContextHolder.getCurrentUserLog());
            log.error("failed to valid payPrice, order = {}, vo = {}", JSONUtil.toJsonStr(order), JSONUtil.toJsonStr(vo));
            return false;
        }
        return true;
    }

    private static List<Orders> spiltOrder(Orders order) {
        List<Orders> ordersList = new ArrayList<>();
        List<String> serviceTimes = JSONUtil.toList(order.getServiceTime(), String.class);
        Long payPrice = order.getFinalPrice();
        Long singlePrice = order.getOriginalPrice() / serviceTimes.size();
        Long originPrice = 0L;
        for (int i = 0; i < serviceTimes.size(); i++) {
            Orders newOrder = new Orders();
            BeanUtil.copyProperties(order, newOrder);
            newOrder.setId(null);
            if (originPrice + singlePrice < payPrice) {
                // 还能使用原始价格进行分配
                newOrder.setFinalPrice(singlePrice);
            } else {
                if (payPrice - originPrice > 0) {
                    // 支付的钱还有盈余
                    newOrder.setFinalPrice(payPrice - originPrice);
                } else {
                    // 支付的钱没有盈余
                    newOrder.setFinalPrice(0L);
                }
            }
            // 每次都把原始价格加进去
            originPrice = originPrice + singlePrice;
            newOrder.setOrderNo(order.getOrderNo() + "_" + i);
            newOrder.setOriginalPrice(singlePrice);
            newOrder.setServiceTime(JSONUtil.toJsonStr(Collections.singletonList(serviceTimes.get(i))));
            newOrder.setCreateTime(System.currentTimeMillis());
            newOrder.setUpdateTime(newOrder.getUpdateTime());
            ordersList.add(newOrder);
        }
        return ordersList;
    }

    private static Orders buildOrders(UserOrdersDTO.CreateOrderVO vo, Long payPrice, boolean distribution, String distributorOpenId) {
        Orders orders = new Orders();
        orders.setOrderNo(String.valueOf(ShortSnowflakeId.generate()));
        orders.setOrdersStatus(PENDING_PAYMENT.getStatusCode());
        orders.setWxTransactionId("");
        orders.setUserId(vo.getUserId());
        orders.setPetId(vo.getPetId());
        orders.setAddressId(vo.getAddressId());
        orders.setServiceType(vo.getServiceType());
        orders.setServiceFlow("");
        orders.setOriginalPrice(vo.getServicePrice() * vo.getServiceTime().size());
        orders.setFinalPrice(payPrice);
        orders.setServiceTime(JSONUtil.toJsonStr(vo.getServiceTime()));
        orders.setCouponIds(JSONUtil.toJsonStr(vo.getCouponIds()));
        orders.setHandoverType(vo.getHandover().getHandoverType());
        orders.setHandover(vo.getHandover().getHandoverRemark());
        orders.setRemarks(vo.getRemark());
        orders.setCreateTime(System.currentTimeMillis());
        orders.setUpdateTime(orders.getCreateTime());
        orders.setServicePrice(calServicePrice(orders.getOriginalPrice()));
        if (distribution) {
            orders.setDistributeUserId(distributorOpenId);
            orders.setDistributorPrice(calDistributorPrice(orders.getFinalPrice()));
        }
        return orders;
    }
}
