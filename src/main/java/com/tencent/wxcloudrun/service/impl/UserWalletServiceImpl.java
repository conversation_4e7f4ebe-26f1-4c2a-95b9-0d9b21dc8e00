package com.tencent.wxcloudrun.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tencent.wxcloudrun.config.BusinessException;
import com.tencent.wxcloudrun.dao.UserWalletMapper;
import com.tencent.wxcloudrun.model.Orders;
import com.tencent.wxcloudrun.model.UserWallet;
import com.tencent.wxcloudrun.service.api.UserWalletService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.tencent.wxcloudrun.enums.ResponseEnum.*;

@Service
public class UserWalletServiceImpl extends ServiceImpl<UserWalletMapper, UserWallet> implements UserWalletService {


    @Override
    public UserWallet getWalletByUserId(String userId) {
        return this.lambdaQuery().eq(UserWallet::getUserId, userId).one();
    }

    /**
     * 更新可提现金额
     *
     * @param userId 用户ID
     * @param amount 金额
     * @return 更新结果
     */
    @Transactional
    @Override
    public boolean updateFreezeAmountByUserId(String userId, Long amount) {
        // 使用 FOR UPDATE 获取数据
        UserWallet wallet = this.baseMapper.selectForUpdate(userId);
        if (wallet == null) {
            wallet = new UserWallet();
            wallet.setUserId(userId);
            wallet.setFreezeAmount(amount);
            wallet.setTotalAmount(amount);
            wallet.setReleaseAmount(0L);
            wallet.setWithdrawnAmount(0L);
            wallet.setCreateTime(System.currentTimeMillis());
            wallet.setUpdateTime(System.currentTimeMillis());
            return this.save(wallet);
        }
        // 总收入
        long totalAmount = wallet.getTotalAmount() + amount;
        // 冻结金额
        long freezeAmount = wallet.getFreezeAmount() + amount;
        wallet.setTotalAmount(totalAmount);
        wallet.setFreezeAmount(freezeAmount);
        wallet.setUpdateTime(System.currentTimeMillis());
        return this.updateById(wallet);
    }

    /**
     * 更新可提现金额
     *
     * @param userId 用户ID
     * @param amount 金额
     * @return 更新结果
     */
    @Transactional
    @Override
    public boolean updaterReleaseAmountByUserId(String userId, Long amount) {
        // 使用 FOR UPDATE 获取数据
        UserWallet wallet = this.baseMapper.selectForUpdate(userId);
        if (wallet == null) {
            throw new BusinessException(WALLET_NOT_EXIST);
        }
        // 冻结金额
        long freezeAmount = wallet.getFreezeAmount() - amount;
        // 可提现金额
        long releaseAmount = wallet.getReleaseAmount() + amount;
        wallet.setFreezeAmount(freezeAmount);
        wallet.setReleaseAmount(releaseAmount);
        if (freezeAmount < 0 ||
                wallet.getTotalAmount() != (wallet.getReleaseAmount() + wallet.getWithdrawnAmount() + wallet.getFreezeAmount())) {
            throw new BusinessException(WALLET_NOT_ENOUGH);
        }

        wallet.setUpdateTime(System.currentTimeMillis());
        return this.updateById(wallet);
    }

    /**
     * 更新已提现金额
     *
     * @param settlementAmount 结算金额
     * @return 更新结果
     */
    @Transactional
    @Override
    public boolean updaterWithdrawnAmountByUserId(Long settlementAmount, String userId) {
        // 使用 FOR UPDATE 获取数据
        UserWallet wallet = this.baseMapper.selectForUpdate(userId);
        if (wallet == null) {
            throw new BusinessException(WALLET_NOT_EXIST);
        }
        // 可提现金额
        long releaseAmount = wallet.getReleaseAmount() - settlementAmount;
        // 已提现金额
        long withdrawnAmount = wallet.getWithdrawnAmount() + settlementAmount;
        wallet.setReleaseAmount(releaseAmount);
        wallet.setWithdrawnAmount(withdrawnAmount);
        wallet.setUpdateTime(System.currentTimeMillis());
        return this.updateById(wallet);
    }


}
