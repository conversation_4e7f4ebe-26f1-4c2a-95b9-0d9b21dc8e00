package com.tencent.wxcloudrun.service.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.wxcloudrun.response.*;
import com.tencent.wxcloudrun.vo.AdminOrderVO;
import org.springframework.transaction.annotation.Transactional;

public interface AdminOrderService {


    Page<UserOrderRes> ordersPage(AdminOrderVO.OrderPage orderPageVO);


    boolean cancel(String orderNo, boolean cancelFlag);

    Boolean changeServiceUser(AdminOrderVO.OrderServiceUser orderServiceUser);

    boolean orderUnfreeze(String orderNo);

    Boolean cancelServiceUser(String orderNo);
}
