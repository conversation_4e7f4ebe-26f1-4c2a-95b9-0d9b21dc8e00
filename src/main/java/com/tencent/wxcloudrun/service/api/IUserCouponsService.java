package com.tencent.wxcloudrun.service.api;

import com.tencent.wxcloudrun.dto.CouponsDTO;
import com.tencent.wxcloudrun.model.UserCoupons;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tencent.wxcloudrun.vo.UserCouponsVO;
import com.tencent.wxcloudrun.vo.UserOrdersVO;

import java.util.List;

/**
 * <p>
 * 用户优惠券管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-02
 */
public interface IUserCouponsService extends IService<UserCoupons> {

    UserCoupons getByOpenIdAndCouponId(String openId, Long couponId);

    List<UserCoupons> getByOpenIdAndCouponIds(String openId, List<Long> couponIds);

    List<UserCoupons> getByOpenIdAndCouponIds(String openId, List<Long> couponIds, Integer isUsed);

    boolean reBack(String openId, List<Long> couponIds);

    List<UserCouponsVO> listByUserId(String userId,  Integer userType);

    Boolean receiveCoupons(Long couponsId, String userId);
    Boolean adminReceiveCoupons(Long couponsId,String mobile);
    List<CouponsDTO.Result> listByPage(CouponsDTO.CouponsPage page);
}
