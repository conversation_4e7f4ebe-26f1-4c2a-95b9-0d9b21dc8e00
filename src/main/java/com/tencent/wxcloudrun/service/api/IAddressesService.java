package com.tencent.wxcloudrun.service.api;

import com.tencent.wxcloudrun.dto.AddressesDTO;
import com.tencent.wxcloudrun.dto.CityProvince;
import com.tencent.wxcloudrun.model.Addresses;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户地理位置数据存储 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
public interface IAddressesService extends IService<Addresses> {

    Addresses queryByUserIdAndAddressId(String userId, long id);

    boolean delete(long id, String userId);

    Boolean save(AddressesDTO.Add dto);

    Boolean updateByIdAndUserId(AddressesDTO.Update dto);

    List<Addresses> listOrderDefault(String userId);

    List<CityProvince.Area> getAreasByCityName(String cityName);

    List<CityProvince.City> getCitiesByProvinceName(String provinceName);

    List<CityProvince.Province> getAllProvinces();

    Map<String, Object> getCityInfoByCode(String cityCode);

}
