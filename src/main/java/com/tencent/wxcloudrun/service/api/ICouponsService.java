package com.tencent.wxcloudrun.service.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tencent.wxcloudrun.dto.CouponsDTO;
import com.tencent.wxcloudrun.model.Coupons;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户优惠券管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-02
 */
public interface ICouponsService extends IService<Coupons> {

    List<CouponsDTO.Result> listByPage(CouponsDTO.CouponsPage page);

    IPage<CouponsDTO.Result> adminCouponsByPage(CouponsDTO.CouponsPage page);

    List<CouponsDTO.Result> listByCouponIds(List<Long> couponIds, Integer userType);

    Boolean save(CouponsDTO.Add dto);

    Boolean updateById(CouponsDTO.Update dto);
}
