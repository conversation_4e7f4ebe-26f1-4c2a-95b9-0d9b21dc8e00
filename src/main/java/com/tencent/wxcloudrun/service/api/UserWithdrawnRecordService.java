package com.tencent.wxcloudrun.service.api;

import com.tencent.wxcloudrun.model.UserWallet;
import com.tencent.wxcloudrun.model.UserWithdrawnRecord;
import org.springframework.transaction.annotation.Transactional;

public interface UserWithdrawnRecordService {

    boolean save(UserWithdrawnRecord userWithdrawnRecord);
    
    /**
     * 查询UserWithdrawnRecord表里面当天的提现记录，提现金额的总和
     * @return 当天提现金额总和
     */
    long queryDataTime();
}