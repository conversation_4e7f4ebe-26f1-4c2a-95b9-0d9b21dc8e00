package com.tencent.wxcloudrun.service.api;

import com.tencent.wxcloudrun.dto.ExternalDTO;
import com.tencent.wxcloudrun.response.TencentLocationParser;

import java.util.List;
import java.util.Map;

public interface ExternalService {

    // 身份证校验接口
    ExternalDTO.IDCardResponse id2MetaVerify(ExternalDTO.IDCard idCard);

    // OCR 身份证识别接口
    ExternalDTO.IDCard ocrIdCard( Map<String, String> idCardImages);
    // 身份证识别接口

    List<ExternalDTO.ImageObj> getImagePath(List<String> idCardImages);

    List<String> convertUri(String url);

    TencentLocationParser.ReverseGeocodeResult reverseGeocode(Map<String, Double> latLong);
}
