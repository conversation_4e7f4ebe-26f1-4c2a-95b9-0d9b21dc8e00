package com.tencent.wxcloudrun.service.api;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tencent.wxcloudrun.model.UserSheet;
import com.tencent.wxcloudrun.vo.UserSheetRecordVo;
import com.tencent.wxcloudrun.vo.UserSheetSubmitVo;
import com.tencent.wxcloudrun.vo.UserSheetUpdateVO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
public interface IUserSheetService extends IService<UserSheet> {

    List<UserSheetRecordVo> queryRecordByUserId(String userId);

    boolean submit(UserSheetSubmitVo vo);

    List<UserSheet> queryByUserIdAndServiceType(String userId, int serviceType);

    // 查询喂养员是否存在待审核的试卷
    boolean queryByUserIdStatus(String userId);

    List<UserSheetSubmitVo.Sheet> generateQuestionBank(String userId, int serviceType);

    List<UserSheetSubmitVo> queryByStatus(int status);

    boolean updateUserSheet(UserSheetUpdateVO entity);
}
