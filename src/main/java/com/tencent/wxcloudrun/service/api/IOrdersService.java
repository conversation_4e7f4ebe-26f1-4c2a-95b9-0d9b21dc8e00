package com.tencent.wxcloudrun.service.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.wxcloudrun.dto.WorkerOrdersDTO;
import com.tencent.wxcloudrun.model.Orders;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tencent.wxcloudrun.model.UserCoupons;
import com.tencent.wxcloudrun.vo.OrderAmountUpdateVo;
import com.tencent.wxcloudrun.vo.AdminOrderVO;

import java.util.List;

/**
 * <p>
 * 订单全生命周期管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
public interface IOrdersService extends IService<Orders> {

    Page<Orders> ordersPage(AdminOrderVO.OrderPage orderPageVO);

    Page<Orders> workerOrdersPage(WorkerOrdersDTO.SearchCondition dto);

    Page<Orders> scan(List<Integer> serviceTypes, int pageSize, long maxId);

    Orders getByOrderNo(String orderNo);

    List<Orders> getByServiceUserId(String serviceUserId);

    List<Orders> getByStatuses(List<Integer> statuses);

    List<Orders> getByDistributorIdAndUserId(String distributorId, String userId);

    List<Orders> getByServiceUserIdAndStatus(String serviceOpenId,  List<Integer> status, int limit, int page);

    List<Orders> getByServiceUserIdAndStatus(List<Integer> status, int limit, int pageNum);

    List<Orders> getByUserIdAndStatus(String serviceOpenId, List<Integer> status, int limit, int page);

    List<Orders> getByUserIdAndStatus(List<Integer> status, int limit, int pageNum);

    Boolean save(Orders orders, List<UserCoupons> updateUserCoupons);

    /**
     * 分单
     * @param orders
     * @param ordersList
     * @return
     */
    Boolean create(Orders orders, List<Orders> ordersList);

    Boolean refund(Orders orders);

    boolean fenqian(OrderAmountUpdateVo vo);

    List<Orders> getTimeAndStartOrders(long finishTime, Integer withdrawalStatus);
}
