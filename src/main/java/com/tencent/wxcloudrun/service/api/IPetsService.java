package com.tencent.wxcloudrun.service.api;

import com.tencent.wxcloudrun.dto.PetsDTO;
import com.tencent.wxcloudrun.model.Pets;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 宠物详细信息档案 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
public interface IPetsService extends IService<Pets> {

    Boolean save(PetsDTO.Add dto);

    Boolean updateById(PetsDTO.Update dto);

    boolean deleteByUserIdAndId(long id, String userId);

    List<Pets> getByIds(List<Long> ids);

    default Pets getById(Long petId) {
        List<Pets> petsList = getByIds(Collections.singletonList(petId));
        return CollectionUtils.isEmpty(petsList) ? null : petsList.get(0);
    }
}
