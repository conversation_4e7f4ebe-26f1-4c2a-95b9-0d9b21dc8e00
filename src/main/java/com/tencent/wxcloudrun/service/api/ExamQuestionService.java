package com.tencent.wxcloudrun.service.api;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tencent.wxcloudrun.dto.ExamQuestionDTO;
import com.tencent.wxcloudrun.model.ExamQuestion;

import java.util.List;

/**
 * 试卷表 Service 接口
 */
public interface ExamQuestionService extends IService<ExamQuestion> {


    List<ExamQuestion> listByPage(ExamQuestionDTO.ExamListQuery examListQuery);

    boolean save(List<ExamQuestion> examQuestion);

    boolean deleteExamQuestion(Long id);

    List<ExamQuestion> generateQuestionBank(int serviceType);
}
