package com.tencent.wxcloudrun.service.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.wxcloudrun.dto.TransferBillsDTO;
import com.tencent.wxcloudrun.dto.UserOrdersDTO;
import com.tencent.wxcloudrun.dto.WorkerOrdersDTO;
import com.tencent.wxcloudrun.model.Orders;
import com.tencent.wxcloudrun.response.*;
import com.tencent.wxcloudrun.vo.AdminUserVO;
import com.tencent.wxcloudrun.vo.OrderAmountUpdateVo;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

public interface UserOrderService {

    ServiceUserPermissions queryPermissions(String serviceUserId);

    ServiceUserIncomeRes income(String serviceUserId);

    Page<SearchServiceUser> searchServiceUsers(UserOrdersDTO.SearchServiceUserCondition condition);

    Page<ServiceOrderRes> search(WorkerOrdersDTO.SearchCondition dto);

    ServiceOrderDetailRes queryByOrderNo(String orderNo, String serviceUserId, int source);

    UserOrderDetailRes queryByOrderNo(String orderNo, String userId);

    UserOrderDetailRes queryByOrderNo(String orderNo);

    Page<ServiceOrderRes> query(WorkerOrdersDTO.OrderListQuery listQuery);

    List<UserOrderRes> buildOrderResult(List<Orders> orderList);

    Page<UserOrderRes> worderQuery(UserOrdersDTO.OrderListQuery dto);

    long calPayPrice(UserOrdersDTO.CreateOrderVO createOrderVO);

    boolean reviewServiceUser(UserOrdersDTO.ServiceReview serviceReview);

    UserOrderCreateRes createUserOrder(UserOrdersDTO.CreateOrderVO createOrderVO);

    Map preCancel(String orderNo, String userId);

    boolean cancel(String orderNo, String userId, String reason);

    boolean cancel(String orderNo, boolean cancelFlag);

    void wechatPayNotify(String notifyData, HttpServletRequest request);

    boolean receiveOrder(String orderNo, String serviceOpenId, Long couponId);

    boolean refuse(String orderNo, String serviceOpenId);

    boolean begin(String orderNo, String serviceOpenId);

    boolean finish(String orderNo, String serviceOpenId);

    boolean reviewUser(WorkerOrdersDTO.ServiceUserReview dto);

    List<String> preFenqian(OrderAmountUpdateVo vo);

    boolean fenqian(OrderAmountUpdateVo vo);

    boolean queryOrderPrice(OrderAmountUpdateVo vo);

    List<Orders> getTimeAndStartOrders(Integer withdrawalStatus);


    TransferBillsDTO.TransferBillsResult userWithdrawal(String userId, Long settlementAmount);
}
