package com.tencent.wxcloudrun.service.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tencent.wxcloudrun.dto.WorkerCertificationDTO;
import com.tencent.wxcloudrun.model.WorkerCertification;
import com.tencent.wxcloudrun.model.WxUsers;
import com.tencent.wxcloudrun.vo.AdminUserVO;

import java.util.List;

public interface WorkerCertificationService extends IService<WorkerCertification> {
    boolean save(WorkerCertificationDTO.ADD dto);

    boolean updateById(WorkerCertificationDTO.UPDATE dto);

    List<Integer> getWorkerType(String openId);

    Page<WorkerCertification> scan(int serviceType, int pageSize, long maxId);

    Page<WorkerCertification> workerCertificationPageList(AdminUserVO.UserPage userListPage);

    List<WorkerCertification> workerCertificationAll(List<String> userIdList);
    List<WorkerCertification> workerServiceType(Integer serviceType);

    boolean selectByIdFlag(String usrId, int serviceType);
}
