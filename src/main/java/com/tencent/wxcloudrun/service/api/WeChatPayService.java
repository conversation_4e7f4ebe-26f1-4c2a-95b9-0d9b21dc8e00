package com.tencent.wxcloudrun.service.api;

import com.tencent.wxcloudrun.dto.TransferBillsDTO;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayWithRequestPaymentResponse;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.profitsharing.model.CreateOrderReceiver;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface WeChatPayService {
    // 微信支付
    PrepayWithRequestPaymentResponse prepay(long payAmount, String orderNo, String openId);
    // 微信回调
    Transaction weChatPayCallback(String notifyData, HttpServletRequest httpRequest);
    // 查询订单支付结果
    Boolean queryWxOrder(String orderId);
    // 退款申请
    Boolean refundOrder(String orderNo, long totalPrice, long finalPrice, String outRefundNo, String reason);
    // 查询退款结果
    Boolean queryByOutRefundNo(String outRefundNo);

    Boolean addReceiver(String openId);

    // 删除分账接收方
    Boolean deleteReceiver(String openId);

    // 开始分账
    Boolean createOrder(String wxTransactionId, String outOrderNo, List<CreateOrderReceiver> createOrderReceivers);

    //查询分账结果
    Boolean queryOrder(String wxTransactionId, String outOrderNo);

    TransferBillsDTO.TransferBillsResult initiateTransferBills(String outBillNo, String openid, Long transferAmount);
}
