package com.tencent.wxcloudrun.service.api;

import com.tencent.wxcloudrun.model.ServiceContent;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tencent.wxcloudrun.vo.ServiceContentUpdateVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
public interface IServiceContentService extends IService<ServiceContent> {

    List<ServiceContent> queryByOrderNo(String orderNo,  Integer serviceContentStatus);

    Map<String, List<ServiceContent>> queryByStatus(int status);

    List<ServiceContent> queryByStatusList(int status);

    List<ServiceContent> getList(int status);

    Map<String, List<ServiceContent>> queryByOrderNoList(List<String> orderNoList, Integer serviceContentStatus);

    boolean updateStatus(ServiceContentUpdateVO vo);

    boolean create(ServiceContent content);

    boolean update(ServiceContent content);
}
