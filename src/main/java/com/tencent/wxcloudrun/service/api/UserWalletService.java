package com.tencent.wxcloudrun.service.api;

import com.tencent.wxcloudrun.model.Orders;
import com.tencent.wxcloudrun.model.UserWallet;
import org.springframework.transaction.annotation.Transactional;

public interface UserWalletService {
    UserWallet getWalletByUserId(String userId);

    @Transactional
    boolean updateFreezeAmountByUserId(String userId, Long amount);

    @Transactional
    boolean updaterReleaseAmountByUserId(String userId, Long amount);

    @Transactional
    boolean updaterWithdrawnAmountByUserId(Long settlementAmount, String userId);
}
