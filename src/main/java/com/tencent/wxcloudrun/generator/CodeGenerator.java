package com.tencent.wxcloudrun.generator;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.builder.*;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

import java.util.HashMap;
import java.util.Map;

public class CodeGenerator {

    //======================== 全局常量配置 ========================
    private static final class Global {
        static final String PROJECT_PATH = System.getProperty("user.dir");
        static final String JAVA_OUTPUT_DIR = PROJECT_PATH + "/src/main/java";
        static final String XML_OUTPUT_DIR = PROJECT_PATH + "/src/main/resources/mapper"; // XML路径修正
        static final String AUTHOR = "hf";
        static final String DATE_FORMAT = "yyyy-MM-dd";
    }

    //======================== 数据库配置 ========================
    private static final class DB {
        static final String URL = "*******************************************************************************************************";
        static final String USERNAME = "root";
        static final String PASSWORD = "<EMAIL>";
    }

    //======================== 包路径配置 ========================
    private static final class Package {
        static final String BASE_PACKAGE = "com.tencent.wxcloudrun";
        static final String ENTITY = "model";
        static final String MAPPER = "dao";
        static final String SERVICE = "service.api";
        static final String SERVICE_IMPL = "service.impl";
        static final String CONTROLLER = "controller";
    }

    //======================== 生成策略配置 ========================
    private static final class Strategy {
        static final String[] INCLUDE_TABLES = {"pet_technology_service_type","pet_technology_service_flow"};
        static final String[] TABLE_PREFIX = {"pet_technology_"};
        static final boolean ENABLE_LOMBOK = true;
        static final boolean REST_CONTROLLER = true;
    }

    //======================== 覆盖策略配置 ========================
    private static final class OverridePolicy {
        static final boolean ENTITY = true;
        static final boolean MAPPER = false;
        static final boolean XML = false;
        static final boolean SERVICE = false;
        static final boolean CONTROLLER = false;
    }

    public static void main(String[] args) {
        AutoGenerator generator = new AutoGenerator(createDataSourceConfig());
        generator.global(buildGlobalConfig());
        generator.packageInfo(buildPackageConfig());
        generator.strategy(buildStrategyConfig());
    
        // 自定义模板路径
        FreemarkerTemplateEngine templateEngine = new FreemarkerTemplateEngine();
        templateEngine.templateFilePath("templates/controller.java.ftl");
        generator.execute(templateEngine);
    }

    private static DataSourceConfig createDataSourceConfig() {
        return new DataSourceConfig.Builder(
                DB.URL,
                DB.USERNAME,
                DB.PASSWORD
        ).build();
    }

    private static GlobalConfig buildGlobalConfig() {
        return new GlobalConfig.Builder()
                .outputDir(Global.JAVA_OUTPUT_DIR)
                .author(Global.AUTHOR)
                .enableSwagger()
                .disableOpenDir()
                .commentDate(Global.DATE_FORMAT).build();
    }

    private static PackageConfig buildPackageConfig() {
        // 设置XML文件路径到resources/mapper目录
        Map<OutputFile, String> pathInfo = new HashMap<>();
        pathInfo.put(OutputFile.xml, Global.XML_OUTPUT_DIR);

        return new PackageConfig.Builder()
                .parent(Package.BASE_PACKAGE)
                .entity(Package.ENTITY)
                .mapper(Package.MAPPER)
                .service(Package.SERVICE)
                .serviceImpl(Package.SERVICE_IMPL)
                .controller(Package.CONTROLLER)
                .pathInfo(pathInfo)
                .build();
    }

    private static StrategyConfig buildStrategyConfig() {
        StrategyConfig.Builder builder = new StrategyConfig.Builder()
         //生成全部表 注释掉下面这行
                .addInclude(Strategy.INCLUDE_TABLES)
                .addTablePrefix(Strategy.TABLE_PREFIX);

        Entity.Builder entityBuilder = builder.entityBuilder()
                .naming(NamingStrategy.underline_to_camel)
                .columnNaming(NamingStrategy.underline_to_camel)
                .enableTableFieldAnnotation()
                .enableChainModel()
                .formatFileName("%s");
        if (OverridePolicy.ENTITY) entityBuilder.enableFileOverride();
        if (Strategy.ENABLE_LOMBOK) entityBuilder.enableLombok();

        Mapper.Builder mapperBuilder = builder.mapperBuilder()
                .superClass(BaseMapper.class) // 继承 BaseMapper
                .enableMapperAnnotation()     // 添加 @Mapper 注解
                .formatMapperFileName("%sMapper");
        if (OverridePolicy.MAPPER) mapperBuilder.enableFileOverride();

        // 在 StrategyConfig 中添加：
        Service.Builder serviceBuilder = builder.serviceBuilder()
                .formatServiceFileName("I%sService")  // 接口命名
                .formatServiceImplFileName("%sServiceImpl")
                .superServiceClass("com.baomidou.mybatisplus.extension.service.IService")  // 继承 IService
                .superServiceImplClass("com.baomidou.mybatisplus.extension.service.impl.ServiceImpl");
        if (OverridePolicy.SERVICE) serviceBuilder.enableFileOverride();

        Controller.Builder controllerBuilder = builder.controllerBuilder()
                .formatFileName("%sController");
        if (OverridePolicy.CONTROLLER) controllerBuilder.enableFileOverride();
        if (Strategy.REST_CONTROLLER) controllerBuilder.enableRestStyle();

        return builder.build();
    }
}