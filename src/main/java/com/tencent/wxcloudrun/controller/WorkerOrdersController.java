package com.tencent.wxcloudrun.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.wxcloudrun.config.ApiResponse;
import com.tencent.wxcloudrun.dto.WorkerOrdersDTO;
import com.tencent.wxcloudrun.response.ServiceOrderRes;
import com.tencent.wxcloudrun.service.api.IUserSheetService;
import com.tencent.wxcloudrun.service.api.UserOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/WorkerOrders")
@Api(tags = "喂养员订单管理接口")
public class WorkerOrdersController {

    @Autowired
    private UserOrderService userOrderService;

    @Autowired
    private IUserSheetService userSheetService;

    @GetMapping("/permissions/{serviceUserId}")
    @ApiOperation(value = "查看接单权限")
    public ApiResponse queryPermissions(@PathVariable String serviceUserId) {
        return ApiResponse.ok(userOrderService.queryPermissions(serviceUserId));
    }

    @GetMapping("/income/{serviceUserId}")
    @ApiOperation(value = "查看收入")
    public ApiResponse income(@PathVariable String serviceUserId) {
        return ApiResponse.ok(userOrderService.income(serviceUserId));
    }

    @PostMapping("/search")
    @ApiOperation(value = "分页搜索订单")
    public ApiResponse<Page<ServiceOrderRes>> search(
            @RequestBody @ApiParam(value = "搜索条件", required = true) WorkerOrdersDTO.SearchCondition dto) {
        return ApiResponse.ok(userOrderService.search(dto));
    }

    @GetMapping("/orderNo/{orderNo}/{source}/{serviceUserId}")
    @ApiOperation(value = "获取订单详细")
    public ApiResponse getObjectById(@PathVariable String orderNo, @PathVariable int source, @PathVariable String serviceUserId) {
        return ApiResponse.ok(userOrderService.queryByOrderNo(orderNo, serviceUserId, source));
    }

    @PostMapping("/list")
    @ApiOperation(value = "分页获取订单列表")
    public ApiResponse<Page<ServiceOrderRes>> list(
            @RequestBody @ApiParam(value = "订单查询条件", required = true) WorkerOrdersDTO.OrderListQuery dto) {
        return ApiResponse.ok(userOrderService.query(dto));
    }


    @GetMapping("/receive/{orderNo}/{userId}/{couponId}")
    @ApiOperation(value = "接单")
    public ApiResponse receive(@PathVariable String orderNo, @PathVariable String userId, @PathVariable Long couponId) {
        return ApiResponse.ok(userOrderService.receiveOrder(orderNo, userId, couponId));
    }

    @GetMapping("/receive/{orderNo}/{userId}")
    @ApiOperation(value = "接单不使用优惠券")
    public ApiResponse receive(@PathVariable String orderNo, @PathVariable String userId) {
        return ApiResponse.ok(userOrderService.receiveOrder(orderNo, userId, null));
    }

    @GetMapping("/back/{orderNo}/{userId}")
    @ApiOperation(value = "退单")
    public ApiResponse back(@PathVariable String orderNo, @PathVariable String userId) {
        return ApiResponse.ok(userOrderService.refuse(orderNo, userId));
    }

    @GetMapping("/begin/{orderNo}/{userId}")
    @ApiOperation(value = "我已上门")
    public ApiResponse begin(@PathVariable String orderNo, @PathVariable String userId) {
        return ApiResponse.ok(userOrderService.begin(orderNo, userId));
    }

    @GetMapping("/finish/{orderNo}/{userId}")
    @ApiOperation(value = "完成")
    public ApiResponse finish(@PathVariable String orderNo, @PathVariable String userId) {
        return ApiResponse.ok(userOrderService.finish(orderNo, userId));
    }

    @PostMapping("/review")
    @ApiOperation(value = "评价")
    public ApiResponse review(@RequestBody @ApiParam(value = "评价", required = true) WorkerOrdersDTO.ServiceUserReview dto) {
        return ApiResponse.ok(userOrderService.reviewUser(dto));
    }

    @GetMapping("/withdrawal/{userId}/{settlementAmount}")
    @ApiOperation(value = "提现")
    public ApiResponse withdrawal(@PathVariable String userId, @PathVariable Long settlementAmount) {
        return ApiResponse.ok(userOrderService.userWithdrawal(userId, settlementAmount));
    }

    @GetMapping("/userSheet/record/{userId}")
    @ApiOperation(value = "查看考试记录")
    public ApiResponse getUserSheetRecord(@PathVariable String userId) {
        return ApiResponse.ok(userSheetService.queryRecordByUserId(userId));
    }
}