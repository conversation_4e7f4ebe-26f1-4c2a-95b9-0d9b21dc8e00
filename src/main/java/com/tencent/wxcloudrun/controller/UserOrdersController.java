package com.tencent.wxcloudrun.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.wxcloudrun.config.ApiResponse;
import com.tencent.wxcloudrun.dto.UserOrdersDTO;
import com.tencent.wxcloudrun.response.SearchServiceUser;
import com.tencent.wxcloudrun.response.UserOrderRes;
import com.tencent.wxcloudrun.service.api.UserOrderService;
import com.tencent.wxcloudrun.service.api.WeChatPayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/api/UserOrders")
@Api(tags = "用户订单管理接口")
public class UserOrdersController {

    @Autowired
    private UserOrderService userOrderService;
    @Autowired
    private WeChatPayService weChatPayService;

    @PostMapping("/search")
    @ApiOperation(value = "分页搜索喂养员")
    public ApiResponse<Page<SearchServiceUser>> searchServiceUsers(
            @RequestBody @ApiParam(value = "搜索条件", required = true) UserOrdersDTO.SearchServiceUserCondition condition) {
        return ApiResponse.ok(userOrderService.searchServiceUsers(condition));
    }

    @GetMapping("/orderNo/{orderNo}/{userId}")
    @ApiOperation(value = "获取订单详情")
    public ApiResponse getObjectById(@PathVariable String orderNo, @PathVariable String userId) {
        return ApiResponse.ok(userOrderService.queryByOrderNo(orderNo, userId));
    }


    @GetMapping("/orderNo/{orderNo}")
    @ApiOperation(value = "获取订单详情")
    public ApiResponse getObjectById(@PathVariable String orderNo) {
        return ApiResponse.ok(userOrderService.queryByOrderNo(orderNo));
    }

    @PostMapping("/list")
    @ApiOperation(value = "分页获取订单列表")
    public ApiResponse<Page<UserOrderRes>> list(
            @RequestBody @ApiParam(value = "订单列表查询条件", required = true) UserOrdersDTO.OrderListQuery dto) {
//        dto.setUserId(null);
        return ApiResponse.ok(userOrderService.worderQuery(dto));
    }

    @PostMapping("/wechat/pay/notify")
    @ApiOperation(value = "微信支付回调")
    public void pay(@RequestBody String notifyData, HttpServletRequest request) {
        userOrderService.wechatPayNotify(notifyData, request);
    }

    @PostMapping("/save")
    @ApiOperation(value = "新增订单")
    public ApiResponse save(@RequestBody @ApiParam(value = "Orders对象", required = true) UserOrdersDTO.CreateOrderVO dto) {
        return ApiResponse.ok(userOrderService.createUserOrder(dto));
    }

    @GetMapping("/orderNo/pre/cancel/{orderNo}/{userId}")
    @ApiOperation(value = "预取消订单")
    public ApiResponse preCancel(@PathVariable String orderNo, @PathVariable String userId) {
        return ApiResponse.ok(userOrderService.preCancel(orderNo, userId));
    }

    @GetMapping("/orderNo/cancel/{orderNo}/{userId}")
    @ApiOperation(value = "取消订单")
    public ApiResponse cancel(@PathVariable String orderNo, @PathVariable String userId) {
        return ApiResponse.ok(userOrderService.cancel(orderNo, userId, null));
    }

    @PostMapping("/order/price/cal")
    @ApiOperation(value = "价格计算")
    public ApiResponse calPayPrice(@RequestBody @ApiParam(value = "订单价格计算", required = true) UserOrdersDTO.CreateOrderVO dto) {
        return ApiResponse.ok(userOrderService.calPayPrice(dto));
    }

    @PostMapping("/order/reviewServiceUser")
    @ApiOperation(value = "评价")
    public ApiResponse reviewServiceUser(@RequestBody @ApiParam(value = "用户订单评价", required = true) UserOrdersDTO.ServiceReview dto) {
        return ApiResponse.ok(userOrderService.reviewServiceUser(dto));
    }


    @GetMapping("/orderNo/queryWxOrder/{orderNo}")
    @ApiOperation(value = "查询微信支付结果")
    public ApiResponse queryWxOrder(@PathVariable String orderNo) {
        return ApiResponse.ok(weChatPayService.queryWxOrder(orderNo));
    }

    @GetMapping("/orderNo/queryByOutRefundNo/{orderNo}")
    @ApiOperation(value = "查询微信退款结果")
    public ApiResponse queryByOutRefundNo(@PathVariable String orderNo) {
        return ApiResponse.ok(weChatPayService.queryByOutRefundNo(orderNo));
    }
}