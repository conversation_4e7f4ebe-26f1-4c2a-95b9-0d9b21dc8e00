package com.tencent.wxcloudrun.controller;

import com.tencent.wxcloudrun.config.ApiResponse;
import com.tencent.wxcloudrun.dto.AddressesDTO;
import com.tencent.wxcloudrun.service.api.IAddressesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/Addresses")
@Api(tags = "地址管理接口", description = "提供Addresses相关的增删改查服务")
public class AddressesController {

    @Autowired
    private IAddressesService service;

    @GetMapping("/id/{id}/{userId}")
    @ApiOperation(value = "获取地址", notes = "获取地址")
    public ApiResponse getObjectById(@PathVariable long id, @PathVariable String userId) {
        return ApiResponse.ok(service.queryByUserIdAndAddressId(userId, id));
    }

    @GetMapping("/listByUser/{userId}")
    @ApiOperation(value = "获取地址列表", notes = "分页查询所有Addresses信息")
    public ApiResponse listByUser(@PathVariable String userId) {
        return ApiResponse.ok(service.listOrderDefault(userId));
    }


    @PostMapping("/save")
    @ApiOperation(value = "新增地址", notes = "保存新的Addresses记录")
    public ApiResponse save(@RequestBody @ApiParam(value = "Addresses对象", required = true) AddressesDTO.Add dto) {
        return ApiResponse.ok(service.save(dto));
    }

    @GetMapping("/delete/{id}/{userId}")
    @ApiOperation(value = "删除地址", notes = "根据ID删除指定Addresses记录")
    public ApiResponse delete(@PathVariable long id, @PathVariable String userId) {
        return ApiResponse.ok(service.delete(id, userId));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新地址", notes = "根据ID修改Addresses记录")
    public ApiResponse update(@RequestBody @ApiParam(value = "Addresses对象", required = true) AddressesDTO.Update dto) {
        return ApiResponse.ok(service.updateByIdAndUserId(dto));
    }

    @GetMapping("/provinces")
    @ApiOperation(value = "获取所有的城市信息", notes = "获取所有的城市信息")
    public ApiResponse getAllProvinces() {
        return ApiResponse.ok(service.getAllProvinces());
    }

    @GetMapping("/cities/{provinceName}")
    @ApiOperation(value = "根据省份获取城市列表", notes = "根据省份获取城市列表")
    public ApiResponse getCitiesByProvince(@PathVariable String provinceName) {
        return ApiResponse.ok(service.getCitiesByProvinceName(provinceName));
    }

    @GetMapping("/areas/{cityName}")
    @ApiOperation(value = "根据城市获取区县", notes = "根据城市获取区县")
    public ApiResponse getAreasByCity(@PathVariable String cityName) {
        return ApiResponse.ok(service.getAreasByCityName(cityName));
    }
    @GetMapping("/cityCode/{cityCode}")
    @ApiOperation(value = "根据城市获取区县", notes = "根据城市获取区县")
    public ApiResponse getSubCitiesByCode(@PathVariable String cityCode) {
        return ApiResponse.ok(service.getCityInfoByCode(cityCode));
    }
}