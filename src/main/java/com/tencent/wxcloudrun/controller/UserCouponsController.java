package com.tencent.wxcloudrun.controller;

import cn.hutool.core.bean.BeanUtil;
import com.tencent.wxcloudrun.config.ApiResponse;
import com.tencent.wxcloudrun.model.Coupons;
import com.tencent.wxcloudrun.model.UserCoupons;
import com.tencent.wxcloudrun.service.api.IUserCouponsService;
import com.tencent.wxcloudrun.vo.CouponsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/UserCoupons")
@Api(tags = "用户优惠券管理接口", description = "提供UserCoupons相关的增删改查服务")
public class UserCouponsController {

    @Autowired
    private IUserCouponsService service;

    @GetMapping("/receiveCoupons/{couponsId}/{userId}")
    @ApiOperation(value = "领取优惠券")
    public ApiResponse receiveCoupons(@PathVariable Long couponsId, @PathVariable String userId) {
        return ApiResponse.ok(service.receiveCoupons(couponsId, userId));
    }

    @GetMapping("/listByUser/{userType}/{userId}")
    @ApiOperation(value = "获取用户优惠券列表", notes = "查询所有Coupons信息")
    public ApiResponse listByUser( @PathVariable Integer userType, @PathVariable String userId) {
        return ApiResponse.ok(service.listByUserId(userId, userType));
    }
}