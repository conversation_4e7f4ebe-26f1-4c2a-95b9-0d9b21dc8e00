package com.tencent.wxcloudrun.controller;

import com.tencent.wxcloudrun.config.ApiResponse;
import com.tencent.wxcloudrun.dto.CouponsDTO;
import com.tencent.wxcloudrun.model.Coupons;
import com.tencent.wxcloudrun.service.api.ICouponsService;
import com.tencent.wxcloudrun.service.api.IUserCouponsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/Coupons")
@Api(tags = "优惠券管理接口")
public class CouponsController {

    @Autowired
    private ICouponsService service;

    @Autowired
    private IUserCouponsService userCouponsService;

    @GetMapping("/id/{id}")
    @ApiOperation(value = "获取优惠券", notes = "获取Coupons")
    public ApiResponse getObjectById(@PathVariable String id) {
        return ApiResponse.ok(service.getById(id));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获取优惠券列表", notes = "分页查询所有Coupons信息")
    public ApiResponse list(@RequestBody @ApiParam(value = "优惠券列表", required = true) CouponsDTO.CouponsPage page) {
        return ApiResponse.ok(userCouponsService.listByPage(page));
    }
    @PostMapping("/admin_list")
    @ApiOperation(value = "后台获取优惠券列表", notes = "分页查询所有Coupons信息")
    public ApiResponse adminList(@RequestBody @ApiParam(value = "优惠券列表", required = true) CouponsDTO.CouponsPage page) {
        return ApiResponse.ok(service.adminCouponsByPage(page));
    }

    @PostMapping("/save")
    @ApiOperation(value = "新增优惠券", notes = "保存新的Coupons记录")
    public ApiResponse save(@RequestBody @ApiParam(value = "Coupons对象", required = true) CouponsDTO.Add dto) {
        return ApiResponse.ok(service.save(dto));
    }

    @GetMapping("/adminReceiveCoupons/{couponsId}/{mobile}")
    @ApiOperation(value = "领取优惠券")
    public ApiResponse adminReceiveCoupons( @PathVariable Long couponsId, @PathVariable String mobile) {
        return ApiResponse.ok(userCouponsService.adminReceiveCoupons(couponsId, mobile));
    }

    @GetMapping("/delete/{id}")
    @ApiOperation(value = "删除优惠券", notes = "根据ID删除指定Coupons记录")
    public ApiResponse delete(@PathVariable String id) {
        return ApiResponse.ok(service.removeById(id));
}

    @PostMapping("/update")
    @ApiOperation(value = "更新优惠券", notes = "根据ID修改Coupons记录")
    public ApiResponse update(@RequestBody @ApiParam(value = "Coupons对象", required = true) CouponsDTO.Update dto) {
        return ApiResponse.ok(service.updateById(dto));
    }
}