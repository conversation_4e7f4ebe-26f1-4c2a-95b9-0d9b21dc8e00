package com.tencent.wxcloudrun.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.wxcloudrun.config.ApiResponse;
import com.tencent.wxcloudrun.model.WxUsers;
import com.tencent.wxcloudrun.response.AdminServiceUserRes;
import com.tencent.wxcloudrun.response.UserOrderRes;
import com.tencent.wxcloudrun.service.api.*;
import com.tencent.wxcloudrun.vo.AdminOrderVO;
import com.tencent.wxcloudrun.vo.AdminUserVO;
import com.tencent.wxcloudrun.vo.ServiceContentUpdateVO;
import com.tencent.wxcloudrun.vo.UserSheetUpdateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/admin")
@Api(tags = "管理后台", description = "管理后台接口")
public class AdminController {

    @Autowired
    private IServiceContentService service;

    @Autowired
    private IUserSheetService userSheetService;
    @Autowired
    private IWxUsersService usersService;

    @Autowired
    private AdminUserService adminUserService;


    @Autowired
    private AdminOrderService adminOrderService;

    @GetMapping("/list/serviceContent/{status}")
    @ApiOperation(value = "获取待审核服务内容", notes = "获取待审核服务内容,接口传1")
    public ApiResponse getServiceContent(@PathVariable int status) {
        return ApiResponse.ok(service.queryByStatus(status));
    }

    @GetMapping("/list/queryByStatusList/{status}")
    @ApiOperation(value = "获取待审核服务内容", notes = "获取待审核服务内容,接口传1")
    public ApiResponse queryByStatusList(@PathVariable int status) {
        return ApiResponse.ok(service.queryByStatusList(status));
    }

    @PostMapping("/update/serviceContent")
    @ApiOperation(value = "审核服务内容", notes = "审核")
    public ApiResponse updateServiceContent(@RequestBody @ApiParam(value = "审核服务内容", required = true) ServiceContentUpdateVO entity) {
        return ApiResponse.ok(service.updateStatus(entity));
    }

    @PostMapping("/update/userSheet")
    @ApiOperation(value = "审核考试试卷", notes = "审核")
    public ApiResponse updateUserSheet(@RequestBody @ApiParam(value = "审核考试试卷", required = true) UserSheetUpdateVO entity) {
        return ApiResponse.ok(userSheetService.updateUserSheet(entity));
    }

    @GetMapping("/list/userSheet/{status}")
    @ApiOperation(value = "获取考试数据", notes = "获取考试数据,接口传1")
    public ApiResponse getUserSheet(@PathVariable int status) {
        return ApiResponse.ok(userSheetService.queryByStatus(status));
    }

    @PostMapping("/list/queryOrder")
    @ApiOperation(value = "后台获取订单管理", notes = "管理端订单查询")
    public ApiResponse<Page<UserOrderRes>> queryOrder(@RequestBody @ApiParam(value = "订单查询", required = true) AdminOrderVO.OrderPage orderPageVO) {

        return ApiResponse.ok(adminOrderService.ordersPage(orderPageVO));
    }

    @GetMapping("/order/orderUnfreeze/{orderNo}")
    @ApiOperation(value = "后台解冻订单", notes = "后台解冻订单")
    public ApiResponse orderUnfreeze(@PathVariable String orderNo) {

        return ApiResponse.ok(adminOrderService.orderUnfreeze(orderNo));
    }


    @PostMapping("/order/cancel")
    @ApiOperation(value = "后台管理退款", notes = "后台管理退款")
    public ApiResponse<Boolean> cancel(@RequestBody @ApiParam(value = "退款", required = true) AdminOrderVO.OrderCancel orderPageVO) {
        return ApiResponse.ok(adminOrderService.cancel(orderPageVO.getOrderNo(), orderPageVO.isCancelFlag()));
    }

    @PostMapping("/order/changeServiceUser")
    @ApiOperation(value = "后台更换喂养员", notes = "后台更换喂养员")
    public ApiResponse<Boolean> changeServiceUser(@RequestBody @ApiParam(value = "后台更换喂养员", required = true) AdminOrderVO.OrderServiceUser orderServiceUser) {
        return ApiResponse.ok(adminOrderService.changeServiceUser(orderServiceUser));
    }

    @GetMapping("/order/cancelServiceUser/{orderNo}")
    @ApiOperation(value = "后台取消喂养员", notes = "后台取消喂养员")
    public ApiResponse<Boolean> cancelServiceUser(@PathVariable String orderNo) {
        return ApiResponse.ok(adminOrderService.cancelServiceUser(orderNo));
    }


    @PostMapping("/user/userListPage")
    @ApiOperation(value = "用户列表", notes = "用户列表")
    public ApiResponse<Page<WxUsers>> userListPage(@RequestBody @ApiParam(value = "用户列表", required = true) AdminUserVO.UserPage userPage) {
        return ApiResponse.ok(usersService.userListPage(userPage));
    }


    @PostMapping("/user/serviceUserListPage")
    @ApiOperation(value = "喂养员用户列表", notes = "喂养员用户列表")
    public ApiResponse<Page<AdminServiceUserRes>> serviceUserListPage(@RequestBody @ApiParam(value = "喂养员用户列表", required = true) AdminUserVO.UserPage userPage) {
        return ApiResponse.ok(adminUserService.serviceUserList(userPage));
    }


}