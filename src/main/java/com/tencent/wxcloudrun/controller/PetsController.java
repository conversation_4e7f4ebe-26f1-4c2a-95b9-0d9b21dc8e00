package com.tencent.wxcloudrun.controller;

import com.tencent.wxcloudrun.config.ApiResponse;
import com.tencent.wxcloudrun.dto.PetsDTO;
import com.tencent.wxcloudrun.model.Pets;
import com.tencent.wxcloudrun.service.api.IPetsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/Pets")
@Api(tags = "宠物管理接口", description = "提供Pets相关的增删改查服务")
public class PetsController {

    @Autowired
    private IPetsService service;

    @GetMapping("/id/{id}")
    @ApiOperation(value = "获取宠物", notes = "获取Pets")
    public ApiResponse getObjectById(@PathVariable String id) {
        return ApiResponse.ok(service.getById(id));
    }

    @GetMapping("/listByUserId/{userId}")
    @ApiOperation(value = "获取宠物列表", notes = "分页查询所有Pets信息")
    public ApiResponse list(@PathVariable String userId) {
        List<Pets> list = service.lambdaQuery().eq(Pets::getUserId, userId).list();
        return ApiResponse.ok(list);
    }

    @PostMapping("/save")
    @ApiOperation(value = "新增宠物", notes = "保存新的Pets记录")
    public ApiResponse save(@RequestBody @ApiParam(value = "Pets对象", required = true) PetsDTO.Add dto) {
        return ApiResponse.ok(service.save(dto));
    }

    @GetMapping("/delete/{id}/{userId}")
    @ApiOperation(value = "删除宠物", notes = "根据ID删除指定Pets记录")
    public ApiResponse delete(@PathVariable long id, @PathVariable String userId) {
        return ApiResponse.ok(service.deleteByUserIdAndId(id, userId));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新宠物", notes = "根据ID修改Pets记录")
    public ApiResponse update(@RequestBody @ApiParam(value = "Pets对象", required = true) PetsDTO.Update dto) {
        return ApiResponse.ok(service.updateById(dto));
    }
}