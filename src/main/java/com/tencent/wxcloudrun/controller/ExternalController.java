package com.tencent.wxcloudrun.controller;

import com.tencent.wxcloudrun.config.ApiResponse;
import com.tencent.wxcloudrun.dto.AddressesDTO;
import com.tencent.wxcloudrun.dto.ExternalDTO;
import com.tencent.wxcloudrun.service.api.ExternalService;
import com.tencent.wxcloudrun.service.api.IAddressesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/External")
@Api(tags = "第三方服务接口", description = "第三方服务接口")
public class ExternalController {

    @Autowired
    private ExternalService externalService;

    @PostMapping("/id2MetaVerify")
    @ApiOperation(value = "验证身份证信息", notes = "验证身份证信息")
    public ApiResponse id2MetaVerify(@RequestBody @ApiParam(value = "验证身份证信息对象", required = true) ExternalDTO.IDCard idCard) {
        return ApiResponse.ok(externalService.id2MetaVerify(idCard));
    }

    @PostMapping("/ocrIdCard")
    @ApiOperation(value = "OCR获取身份证信息", notes = "OCR获取身份证信息")
    public ApiResponse ocrIdCard(@RequestBody Map<String, String> idCardImages) {
        return ApiResponse.ok(externalService.ocrIdCard(idCardImages));
    }
    @PostMapping("/reverseGeocode")
    @ApiOperation(value = "逆地址解析", notes = "逆地址解析")
    public ApiResponse reverseGeocode(@RequestBody Map<String, Double> latLong) {
        return ApiResponse.ok(externalService.reverseGeocode(latLong));
    }
}