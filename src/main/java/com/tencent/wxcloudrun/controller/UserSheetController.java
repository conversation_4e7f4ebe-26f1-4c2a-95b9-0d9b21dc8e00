package com.tencent.wxcloudrun.controller;

import com.tencent.wxcloudrun.config.ApiResponse;
import com.tencent.wxcloudrun.service.api.IServiceContentService;
import com.tencent.wxcloudrun.service.api.IUserSheetService;
import com.tencent.wxcloudrun.vo.ServiceContentUpdateVO;
import com.tencent.wxcloudrun.vo.UserSheetSubmitVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/user/sheet")
@Api(tags = "用户试卷", description = "用户试卷接口")
public class UserSheetController {

    @Autowired
    private IUserSheetService userSheetService;

    @PostMapping("/submit")
    @ApiOperation(value = "试卷提交", notes = "试卷提交")
    public ApiResponse save(@RequestBody @ApiParam(value = "试卷提交", required = true) UserSheetSubmitVo vo) {
        return ApiResponse.ok(userSheetService.submit(vo));
    }

    @GetMapping("/{userId}/{serviceType}")
    @ApiOperation(value = "获取考试试卷", notes = "获取考试试卷")
    public ApiResponse getServiceContent(@PathVariable String userId, @PathVariable int serviceType) {
        return ApiResponse.ok(userSheetService.generateQuestionBank(userId, serviceType));
    }

}