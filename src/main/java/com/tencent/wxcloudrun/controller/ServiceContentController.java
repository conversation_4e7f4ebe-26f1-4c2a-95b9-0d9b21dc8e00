package com.tencent.wxcloudrun.controller;

import com.tencent.wxcloudrun.config.ApiResponse;
import com.tencent.wxcloudrun.model.ServiceContent;
import com.tencent.wxcloudrun.service.api.IServiceContentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/ServiceContent")
@Api(tags = "服务内容接口", description = "提供服务内容相关的增删改查服务")
public class ServiceContentController {

    @Autowired
    private IServiceContentService service;

    @PostMapping("/save")
    @ApiOperation(value = "新增ServiceFlow", notes = "保存新的ServiceFlow记录")
    public ApiResponse save(@RequestBody @ApiParam(value = "ServiceFlow对象", required = true) ServiceContent entity) {
        return ApiResponse.ok(service.create(entity));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新服务报告", notes = "更新")
    public ApiResponse update(@RequestBody @ApiParam(value = "ServiceFlow对象", required = true) ServiceContent entity) {
        return ApiResponse.ok(service.update(entity));
    }

    @GetMapping("/list/{orderNo}")
    @ApiOperation(value = "获取服务内容", notes = "获取服务内容")
    public ApiResponse list(@PathVariable String orderNo) {
        return ApiResponse.ok(service.queryByOrderNo(orderNo, null));
    }
}