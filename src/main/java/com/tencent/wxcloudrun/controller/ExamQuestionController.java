package com.tencent.wxcloudrun.controller;

import com.tencent.wxcloudrun.config.ApiResponse;
import com.tencent.wxcloudrun.dto.ExamQuestionDTO;
import com.tencent.wxcloudrun.model.ExamQuestion;
import com.tencent.wxcloudrun.service.api.ExamQuestionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 试卷表 Controller
 */
@RestController
@RequestMapping("/api/exam")
@Api(tags = "试卷管理", description = "试卷管理接口")
public class ExamQuestionController {

    @Autowired
    private ExamQuestionService examQuestionService;

    @PostMapping("/list")
    @ApiOperation(value = "获取所有试卷题目", notes = "获取所有试卷题目")
    public ApiResponse getAllExamQuestions(@RequestBody  ExamQuestionDTO.ExamListQuery examListQuery) {
        List<ExamQuestion> examQuestions = examQuestionService.listByPage(examListQuery);
        return ApiResponse.ok(examQuestions);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID获取试卷题目", notes = "根据ID获取试卷题目")
    public ApiResponse getExamQuestionById(@PathVariable Long id) {
        ExamQuestion examQuestion = examQuestionService.getById(id);
        return ApiResponse.ok(examQuestion);
    }

    @PostMapping("/add")
    @ApiOperation(value = "添加试卷题目", notes = "添加试卷题目")
    public ApiResponse addExamQuestion(@RequestBody List<ExamQuestion> examQuestion) {
        return ApiResponse.ok(examQuestionService.save(examQuestion));
    }


    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除试卷题目", notes = "删除试卷题目")
    public ApiResponse deleteExamQuestion(@PathVariable Long id) {

        return ApiResponse.ok(examQuestionService.deleteExamQuestion(id));
    }
}
