package com.tencent.wxcloudrun.controller;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.tencent.wxcloudrun.dto.ExamQuestionDTO;
import com.tencent.wxcloudrun.model.OrderServiceContent;
import com.tencent.wxcloudrun.model.Orders;
import com.tencent.wxcloudrun.model.ServiceContent;
import com.tencent.wxcloudrun.service.api.ExternalService;
import com.tencent.wxcloudrun.service.api.IOrdersService;
import com.tencent.wxcloudrun.service.api.IServiceContentService;
import com.tencent.wxcloudrun.service.api.IUserSheetService;
import com.tencent.wxcloudrun.utils.ExcelExportUtils;
import com.tencent.wxcloudrun.vo.OrderExportVo;
import com.tencent.wxcloudrun.vo.UserSheetExportVo;
import com.tencent.wxcloudrun.vo.UserSheetSubmitVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/order")
public class OrderExportController {

    @Autowired
    private IServiceContentService service;

    @Autowired
    private ExternalService externalService;

    @Autowired
    private IUserSheetService userSheetService;

    @Autowired
    private IOrdersService ordersService;

    @GetMapping("/export-service-content")
    public void exportServiceContent(HttpServletResponse response) throws IOException {
        // 1. 获取数据（根据实际业务调整）
        List<ServiceContent> dataList = service.getList(1);
        List<OrderServiceContent> collected = dataList.stream().map(val -> {
            OrderServiceContent orderServiceContent = new OrderServiceContent();
            BeanUtils.copyProperties(val, orderServiceContent);
            List<String> newPetUrls = externalService.convertUri(orderServiceContent.getPetUrls());
            List<String> newDoors = externalService.convertUri(orderServiceContent.getDoorUrls());
            List<String> newRooms = externalService.convertUri(orderServiceContent.getRoomUrls());
            List<String> newEats = externalService.convertUri(orderServiceContent.getEatUrls());
            orderServiceContent.setPetUrls(JSONUtil.toJsonStr(newPetUrls));
            orderServiceContent.setDoorUrls(JSONUtil.toJsonStr(newDoors));
            orderServiceContent.setRoomUrls(JSONUtil.toJsonStr(newRooms));
            orderServiceContent.setEatUrls(JSONUtil.toJsonStr(newEats));
            return orderServiceContent;
        }).collect(Collectors.toList());
        // 2. 执行导出
        ExcelExportUtils.export(
                "上门喂养服务记录",  // 文件名
                collected,         // 数据列表
                response,         // 响应对象
                30                // 设置图片列宽度为30字符
        );
    }

    @GetMapping("/export-order")
    public void exportorder(HttpServletResponse response) throws IOException {
        // 1. 获取数据（根据实际业务调整）
        List<Orders> orders = ordersService.getByStatuses(Lists.newArrayList(5, 6));

        // 3. 数据转换（包含关联信息查询）
        List<OrderExportVo> exportData = orders.stream()
                .map(order -> {
                    return OrderExportVo.fromOrder(order);
                })
                .collect(Collectors.toList());

        // 4. 执行导出
        ExcelExportUtils.export(
                "订单数据_" + System.currentTimeMillis(),
                exportData,
                response,
                18  // 默认列宽
        );
    }

    @GetMapping("/export")
    public void export(HttpServletResponse response) {
        try {
            List<UserSheetSubmitVo> userSheetSubmitVos = userSheetService.queryByStatus(1);

            // 转换嵌套结构为平铺结构
            List<UserSheetExportVo> exportData = userSheetSubmitVos.stream()
                    .flatMap(vo -> vo.getSheetAnswers().stream()
                            .map(sheet -> {
                                UserSheetExportVo exportVo = new UserSheetExportVo();

                                // 复制喂养员信息
                                exportVo.setServiceUserId(vo.getServiceUserId());
                                exportVo.setSheetId(vo.getSheetId());
                                exportVo.setServiceType(vo.getServiceType());

                                // 处理题目信息
                                exportVo.setNumber(sheet.getNumber());
                                exportVo.setTypeStr(convertType(sheet.getType()));
                                exportVo.setSingleChoose(sheet.getCorrectAnswer());
                                exportVo.setMultipleChoose(sheet.getMultiCorrectAnswer());
                                exportVo.setJudgment(sheet.getJudgmentAnswer());
                                exportVo.setSubjective(sheet.getSubjectiveAnswer());
                                exportVo.setScore(sheet.getScore());
                                exportVo.setUserScore(sheet.getStandardScore());

                                return exportVo;
                            }))
                    .collect(Collectors.toList());

            // 执行导出
            ExcelExportUtils.export(
                    "喂养员试卷记录",
                    exportData,
                    response,
                    30  // 设置默认列宽
            );
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            log.error("试卷导出失败", e);
        }
    }

    private String convertType(Integer type) {
        switch (type) {
            case 1: return "单选题";
            case 2: return "多选题";
            case 3: return "判断题";
            case 4: return "主观题";
            default: return "未知题型";
        }
    }
}