package com.tencent.wxcloudrun.enums;

import lombok.Getter;

@Getter
public enum ServiceTypeEnum {

    // 待支付，状态码为 0
    CAT(1, "猫"),
    // 待接单，状态码为 1
    DOG(2, "狗"),
    ;

    // 状态码
    private final int serviceType;
    // 状态描述
    private final String desc;

    // 构造函数，用于初始化状态码和状态描述
    ServiceTypeEnum(int statusCode, String desc) {
        this.serviceType = statusCode;
        this.desc = desc;
    }
}