package com.tencent.wxcloudrun.enums;

public enum OrderTypeEnum {
    // 全部订单，type 为 0
    ALL(0, "all"),
    // 待接单订单，type 为 1
    PENDING(1, "pending"),
    // 进行中订单，type 为 2
    IN_PROGRESS(2, "inProgress"),
    // 已完成订单，type 为 3
    COMPLETED(3, "completed");

    // 订单类型对应的整数标识
    private final int type;
    // 订单类型的中文描述
    private final String description;

    // 构造函数，用于初始化枚举实例的 type 和描述
    OrderTypeEnum(int type, String description) {
        this.type = type;
        this.description = description;
    }

    // 获取订单类型对应的整数标识
    public int getType() {
        return type;
    }

    // 获取订单类型的中文描述
    public String getDescription() {
        return description;
    }

    // 重写 toString 方法，返回订单类型的中文描述
    @Override
    public String toString() {
        return description;
    }

    // 根据 type 值获取对应的枚举实例
    public static OrderTypeEnum getByType(int type) {
        for (OrderTypeEnum orderType : values()) {
            if (orderType.type == type) {
                return orderType;
            }
        }
        return null;
    }
}