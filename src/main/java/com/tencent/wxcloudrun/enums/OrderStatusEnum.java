package com.tencent.wxcloudrun.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
public enum OrderStatusEnum {
    // 待支付，状态码为 0
    PENDING_PAYMENT(0, "待支付"),
    // 待接单，状态码为 1
    PENDING_ACCEPTANCE(1, "待接单//已支付"),
    // 待服务，状态码为 2
    PENDING_SERVICE(2, "待服务//已接单"),
    // 进行中，状态码为 3
    IN_PROGRESS(3, "进行中//服务中"),
    // 已完成，状态码为 4
    COMPLETED(4, "已完成//待审核"),
    // 已生成报告，状态码为 5
    REPORT_GENERATED(5, "已生成报告"),
    // 已评价，状态码为 6
    EVALUATED(6, "已评价"),
    // 已拒绝，状态码为 7
    REFUSE(7, "已拒绝"),
    // 已付款，状态码为 8
    SETTLEMENT(8, "已结算"),
    // 已退款，状态码为 9
    REFUND(9, "已退款"),
    // 非法状态，状态码为 99
    NOT(99, "非法状态"),
    ;


    // 状态码
    private final int statusCode;
    // 状态描述
    private final String statusDescription;

    // 构造函数，用于初始化状态码和状态描述
    OrderStatusEnum(int statusCode, String statusDescription) {
        this.statusCode = statusCode;
        this.statusDescription = statusDescription;
    }

    public static List<Integer> getProcessStatues() {
        return Lists.newArrayList(
                EVALUATED.getStatusCode(), REPORT_GENERATED.getStatusCode(), COMPLETED.getStatusCode(),
                IN_PROGRESS.getStatusCode(), PENDING_SERVICE.getStatusCode(), SETTLEMENT.getStatusCode()
        );
    }

    public static List<Integer> getAllProcessStatues() {
        return Lists.newArrayList(PENDING_ACCEPTANCE.getStatusCode(), REFUSE.getStatusCode(),
                EVALUATED.getStatusCode(), REPORT_GENERATED.getStatusCode(), COMPLETED.getStatusCode(),
                IN_PROGRESS.getStatusCode(), PENDING_SERVICE.getStatusCode(), SETTLEMENT.getStatusCode(), REFUND.getStatusCode()
        );
    }

    // 进行中
    public static List<Integer> getInStatues() {
        return Lists.newArrayList(PENDING_SERVICE.getStatusCode(), IN_PROGRESS.getStatusCode()
        );
    }

    public static String find(int statusCode) {
        return Arrays.stream(OrderStatusEnum.values()).filter(val -> val.getStatusCode() == statusCode)
                .findFirst().map(val -> val.getStatusDescription())
                .orElse("未知");
    }

    // 已完成
    public static List<Integer> getAccomplishStatues() {
        return Lists.newArrayList(COMPLETED.getStatusCode(), REPORT_GENERATED.getStatusCode(),
                EVALUATED.getStatusCode(), SETTLEMENT.getStatusCode()
        );
    }
    // 服务状态
    public static List<Integer> getUserAccomplishStatues(int orderStatusCode) {
        Map<Integer, List<Integer>> objectObjectHashMap = new HashMap<>();
        // 全部
        objectObjectHashMap.put(0, getAllProcessStatues());
        // 待接单
        objectObjectHashMap.put(1, Lists.newArrayList(PENDING_ACCEPTANCE.getStatusCode()));
        // 进行中
        objectObjectHashMap.put(2, getInStatues());
        // 已完成
        objectObjectHashMap.put(3, getAccomplishStatues());
        // 待评价
        objectObjectHashMap.put(4, Lists.newArrayList(REPORT_GENERATED.getStatusCode()));

        return objectObjectHashMap.getOrDefault(orderStatusCode, Lists.newArrayList(NOT.getStatusCode()));
    }
}