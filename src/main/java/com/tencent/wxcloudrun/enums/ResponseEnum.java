package com.tencent.wxcloudrun.enums;

import lombok.Getter;

@Getter
public enum ResponseEnum {

    SUCCESS(0, "成功"),
    // 登录相关
    LOGIN_ERROR(10001, "登录失败"),
    USER_INFO_NOT_EXIST(10002, "用户信息不一致"),
    PASSWORD_ERROR(10003, "密码错误"),
    MOBILE_ERROR(10004, "手机号错误"),
    OPENID_ERROR(10005, "openID不存在"),

    // 用户相关
    USER_NOT_LOGIN(10005, "用户未登录"),
    USER_PASSWORD_REQUIRED(10006, "用户密码不能为空"),
    USER_MOBILE_REQUIRED(10007, "用户手机号不能为空"),
    USER_NOT_EXIST(10008, "用户不存在"),


    // 钱包相关
    WALLET_NOT_EXIST(20001, "钱包不存在"),
    WALLET_NOT_ENOUGH(20002, "钱包金额错误"),

    // 考试相关
    SHEET_EXIST(30001, "考试已通过，无需重复考试"),
    SHEET_NOT_ANSWERS(30002, "试卷为空，请先答题"),
    SHEET_ALREADY_SUBMIT(30002, "请勿重复提交试卷"),

    // 订单/支付相关
    ORDER_INFO_ERROR(40001, "订单信息错误"),
    PAYMENT_FAILED(40002, "支付失败"),
    REFUND_FAILED(40003, "退款失败"),
    TRANSFER_FAILED(40004, "转账失败"),
    PROFIT_SHARING_FAILED(40005, "分账失败"),
    ORDER_SERVICE_TIME_FAILED(40006, "订单服务日期为空"),
    ORDER_COUPON_FAILED(40007, "订单优惠券验证失败"),
    ORDER_USER_ID_FAILED(40008, "用户ID不一致"),
    ORDER_PERMISSION_FAILED(40009, "不具备接单权限"),
    ORDER_COUPON_NUM_FAILED(40010, "优惠券数量校验失败"),
    ORDER_COUPON_TIME_FAILED(40011, "优惠券已过期"),
    ORDER_STATUS_FAILED(40012, "订单状态不合法"),
    ORDER_PRICE_FAILED(40013, "订单价格不合法"),
    ORDER_PRICE_COUPONS_FAILED(40014, "优惠券小于订单价格"),
    ORDER_TIME_ERROR(40015, "订单服务时间异常"),
    ORDER_NOT_SERVICE_USER_ERROR(40016, "订单状态不支持更换喂养员"),

    // 宠物相关
    PETS_INFO_FAILED(50001, "宠物信息错误"),
    PETS_NOT_INFO(50002, "宠物不存在"),

    // 优惠券相关

    COUPON_NOT_EXIST(60001, "优惠券不存在"),
    COUPON_NOT_ENOUGH(60002, "优惠券数量不足"),
    COUPON_NOT_START(60003, "优惠券未开始"),
    COUPON_NOT_END(60004, "优惠券已结束"),
    COUPON_NOT_RECEIVE(60005, "优惠券已领取"),
    COUPON_NOT_USED(60006, "优惠券未使用"),
    COUPON_NOT_EXPIRED(60007, "优惠券未过期"),
    COUPON_NOT_VALID(60008, "优惠券未生效"),

    //  服务内容相关
    SERVICE_CONTENT_NOT_EXIST(70001, "服务内容不存在"),
    SERVICE_CONTENT_NOT_VALID(70002, "服务内容未生效"),
    SERVICE_CONTENT_NOT_EXPIRED(70003, "服务内容未过期"),
    SERVICE_CONTENT_NOT_START(70004, "服务内容未开始"),

    // 其他相关
    IMAGE_NOT_EXIST(80001, "图片不存在"),
    IMAGE_NOT_VALID(80002, "图片验证失败"),

    // 试卷相关
    EXAM_NOT_EXIST(90001, "试卷不存在"),
    EXAM_COUNT_FAILED(90002, "题干重复"),
    // 喂养员接单错误
    WORKER_ORDER_ERROR(1000001, "接单失败"),
    WORKER_ORDER_NOT_CERTIFIED(1000002, "喂养员没有接单权限"),
    USER_NOT_CERTIFIED(1000002, "用户未认证"),

    // 系统错误
    SYSTEM_ERROR(999999, "系统异常，请稍后再试");


    // 状态码
    private final int code;
    // 状态描述
    private final String message;

    // 构造函数，用于初始化状态码和状态描述
    ResponseEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }
}