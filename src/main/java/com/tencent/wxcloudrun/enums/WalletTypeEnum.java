package com.tencent.wxcloudrun.enums;

import lombok.Getter;

@Getter
public enum WalletTypeEnum {

    WALLET_FREEZE(1, "钱包冻结"),
    WALLET_SETTLEMENT(2, "订单结算"),
    ;

    // 状态码
    private final int couponType;
    // 状态描述
    private final String desc;

    // 构造函数，用于初始化状态码和状态描述
    WalletTypeEnum(int statusCode, String desc) {
        this.couponType = statusCode;
        this.desc = desc;
    }
}