package com.tencent.wxcloudrun.enums;

import lombok.Getter;
import org.bouncycastle.pqc.crypto.newhope.NHSecretKeyProcessor;

import java.util.Arrays;

@Getter
public enum UserSheetStatusEnum {
    // 待支付，状态码为 0
    AUDIT(1, "审核中"),
    // 待接单，状态码为 1
    PASS(2, "通过"),
    FAIL(3, "未通过"),
    ;

    // 状态码
    private final int userSheetStatus;
    // 状态描述
    private final String desc;

    // 构造函数，用于初始化状态码和状态描述
    UserSheetStatusEnum(int userSheetStatus, String desc) {
        this.userSheetStatus = userSheetStatus;
        this.desc = desc;
    }

    public static String find(int userSheetStatus) {
       return Arrays.stream(UserSheetStatusEnum.values()).filter(val -> val.getUserSheetStatus() == userSheetStatus)
                .findFirst().map(UserSheetStatusEnum::getDesc).orElse("未知");
    }
}
