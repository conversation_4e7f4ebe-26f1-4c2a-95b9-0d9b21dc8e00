package com.tencent.wxcloudrun.enums;

import lombok.Getter;

@Getter
public enum CouponTypeEnum {
    // 100-199 宠物主人优惠券
    FREE_FOR_C_TEN(100, "无门槛减10"),
    FREE_FOR_C_EIGHT(101, "无门槛减8"),
    FREE_FOR_C_FIVE(102, "无门槛减5"),

    // 200-299 饲养员优惠券
    FREE_FOR_B(200, "免20%佣金"),
    ;

    // 状态码
    private final int couponType;
    // 状态描述
    private final String desc;

    // 构造函数，用于初始化状态码和状态描述
    CouponTypeEnum(int statusCode, String desc) {
        this.couponType = statusCode;
        this.desc = desc;
    }
}