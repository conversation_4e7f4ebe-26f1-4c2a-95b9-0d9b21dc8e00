package com.tencent.wxcloudrun.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.models.auth.In;
import lombok.Data;

import java.util.List;

@Data
public class ExternalDTO {

    @Data
    @ApiModel(value = "认证身份证类")
    public static class WeCatRes {
        //结果状态码
        private Integer errcode;
        //错误信息。
        private String errmsg;
    }


    @Data
    @ApiModel(value = "认证身份证类")
    public static class IDCard extends WeCatRes {
        //姓名。
        private String userName;
        //身份证号。
        private String idNum;
        //身份证开始时间。
        private String validityBegin;
        //身份证结束时间。
        private String validityEnd;

    }

    @Data
    @ApiModel(value = "身份证校验结果")
    public static class IDCardResponse {
        //请求 ID。
        private String requestId;
        //认证状态码。
        private String code;
        //认证结果。
        private String message;
    }

    @Data
    @ApiModel(value = "OCR结果类")
    public static class OCRResponse extends WeCatRes {
        private String type;
        private String name;
        private String id;
        private String addr;
        private String gender;
        private String nationality;
        private String valid_date;
    }

    @Data
    @ApiModel(value = "图像下载")
    public static class ImagesResponse extends WeCatRes {
        private List<ImageObj> file_list;
    }

    @Data
    @ApiModel(value = "图像下载")
    public static class ImageObj {
        // 文件ID
        private String fileid;
        // 下载地址
        private String download_url;
        // 文件状态
        private Integer status;
        // 状态说明
        private String errmsg;
    }


}
