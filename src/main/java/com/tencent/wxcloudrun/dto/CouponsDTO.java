package com.tencent.wxcloudrun.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.tencent.wxcloudrun.base.FieldOrder;
import com.tencent.wxcloudrun.model.Coupons;
import com.tencent.wxcloudrun.vo.PageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 优惠券表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-02
 */

@Data
public class CouponsDTO {

        @Data
        @ApiModel(value = "优惠券列表, 第一页从1开始")
        public static class CouponsPage  extends PageSize {

                @ApiModelProperty(value = "用户ID", example = "1")
                private String userId;
                @ApiModelProperty(value = "排序方式", example = "1")
                private List<FieldOrder> orders;
        }

        @Data
        @ApiModel(value = "优惠券对象新增")
        public static class Add extends CouponsDTO.Base {
        
        }

        @Data
        @ApiModel(value = "优惠券返回对象")
        public static class Result extends CouponsDTO.Base {
                /**
                 * 自增长
                 */
                @ApiModelProperty(value = "自增长", example = "1")
                private Long id;

                /**
                 * 自增长
                 */
                @ApiModelProperty(value = "是否已领取", example = "1")
                private boolean collectFlag;

        }
        
        @Data
        @ApiModel(value = "优惠券修改")
        public static class Update extends CouponsDTO.Base {
                
                /**
                 * 自增长
                 */
                @ApiModelProperty(value = "自增长", example = "1")
                private Long id;
        }
        
        @Data
        @ApiModel(value = "优惠券对象")
        public static class Base {
                /**
                 * 优惠类型
                 */
                @ApiModelProperty(value = "优惠券类型  1 满减 2 打折", example = "0")
                private Integer couponType;
                
                /**
                 * 优惠券名称
                 */
                @ApiModelProperty(value = "优惠券名称", example = "新人专享券")
                private String title;

                /**
                 * 折扣率折扣几几折
                 */
                @ApiModelProperty(value = "折扣率折扣几几折", example = "10")
                private int discount;
                
                /**
                 * 数量
                 */
                @ApiModelProperty(value = "数量", example = "100")
                private Integer couponsNum;
                
                /**
                 * 生效时间
                 */
                @ApiModelProperty(value = "生效时间", example = "1730301600000")
                private Long validStart;
                
                /**
                 * 过期时间
                 */
                @ApiModelProperty(value = "过期时间", example = "1731079199000")
                private Long validEnd;

                @ApiModelProperty("0 展示 1 不展示")
                private int enable;

                @ApiModelProperty("优惠券有效时间 默认30天")
                private int effectiveTime;
                @ApiModelProperty("优惠券信息")
                private String ext;
        }
}