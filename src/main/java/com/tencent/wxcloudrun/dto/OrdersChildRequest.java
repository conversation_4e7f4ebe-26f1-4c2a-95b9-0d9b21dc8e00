package com.tencent.wxcloudrun.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description pet_technology_orders_child
 * @date 2025-03-02
 */
@Data
public class OrdersChildRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业务订单号（规则生成）
     */
    private String orderNo;

    /**
     * 子订单号（规则生成）
     */
    private String childOrderNo;

    /**
     * 总微信支付单号
     */
    private String wxTransactionId;

    /**
     * 下单用户ID
     */
    private String userId;

    /**
     * 喂养员ID
     */
    private String caretakersId;

    /**
     * 服务宠物ID，可能有多个
     */
    private String petId;

    /**
     * 服务地址ID
     */
    private String addressId;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 服务流程
     */
    private String serviceFlow;

    /**
     * 子订单价格
     */
    private String originalPrice;


    /**
     * 交接方式
     */
    private String handover;

    /**
     * 订单备注
     */
    private String remarks;

    /**
     * 服务过程图片，多张图片，和文字描述
     */
    private String caretakersProcess;

    /**
     * 评价，用户和喂养员评价，文字描述
     */
    private String evaluateNotes;

    /**
     * 支付完成时间
     */
    private String payTime;

    /**
     * 退款时间
     */
    private String refundTime;

    /**
     * 预约服务时间
     */
    private String serviceTime;

    /**
     * 服务实际开始时间
     */
    private String serviceStartTime;

    /**
     * 服务实际结束时间
     */
    private String serviceEndTime;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * json格式数据
     */
    private String ext;

    /**
     * 更新时间
     */
    private Long updateTime;

}