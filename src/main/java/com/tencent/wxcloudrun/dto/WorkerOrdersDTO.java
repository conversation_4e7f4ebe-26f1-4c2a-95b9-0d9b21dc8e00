package com.tencent.wxcloudrun.dto;

import com.tencent.wxcloudrun.vo.PageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Data
public class WorkerOrdersDTO {

    @Data
    @ApiModel(value = "搜索条件")
    public static class SearchCondition extends PageSize {

        @ApiModelProperty(value = "搜索类型")
        private List<Integer> serviceType;

        @ApiModelProperty(value = "最低价格")
        private Long minPrice;

        @ApiModelProperty(value = "最高价格")
        private Long maxPrice;

        @ApiModelProperty(value = "距离")
        private Long distance;

        @ApiModelProperty(value = "区域")
        private String district;

        @ApiModelProperty(value = "用户ID")
        private String userId;

        /**
         * 纬度（WGS84坐标系）
         */
        @ApiModelProperty(value = "纬度（WGS84坐标系）", example = "39.9042")
        private BigDecimal latitude;

        /**
         * 经度（WGS84坐标系）
         */
        @ApiModelProperty(value = "经度（WGS84坐标系）", example = "116.4074")
        private BigDecimal longitude;

    }

    @Data
    @ApiModel(value = "服务内容")
    public static class ServiceReport {

    }

    @Data
    @ApiModel(value = "饲养员评价")
    public static class ServiceUserReview {
        @ApiModelProperty(value = "订单编号")
        private String orderNo;

        @ApiModelProperty(value = "饲养员id")
        private String serviceUserId;

        @ApiModelProperty(value = "评价分")
        private int score;
    }

    @Data
    @ApiModel(value = "用户订单对象支付")
    public static class Pay{

        @ApiModelProperty(value = "订单编号")
        private String orderNo;

        @ApiModelProperty(value = "用户id")
        private String userId;

        @ApiModelProperty(value = "价格")
        private String price;
    }

    @Data
    @ApiModel(value = "用户订单对象列表")
    public static class OrderListQuery extends PageSize {

        @ApiModelProperty(value = "订单状态 全部 0, 待接单 1, 进行中 2 已完成 3, 待评价 4 ")
        private int orderStatus;
        @ApiModelProperty(value = "用户id", example = "user_001")
        private String userId;
    }

    @Getter
    @Setter
    @ApiModel(value = "用户订单对象新增")
    public static class Add extends WorkerOrdersDTO.Base {

    }

    @Getter
    @Setter
    @ApiModel(value = "用户订单对象修改")
    public static class Update extends WorkerOrdersDTO.Base {

    }

    @Data
    @ApiModel(value = "用户订单对象")
    public static class Base {


        @ApiModelProperty(value = "订单编号, 32位随机字母")
        private String orderNo;
        /**
         * 微信支付单号
         */
        @ApiModelProperty("微信支付单号")
        private String wxTransactionId;

        /**
         * 下单用户ID
         */
        @ApiModelProperty("下单用户ID")
        private String userId;

        /**
         * 服务宠物ID,可能有多个
         */
        @ApiModelProperty("服务宠物ID,可能有多个")
        private String petId;

        /**
         * 服务地址ID
         */
        @ApiModelProperty("服务地址ID")
        private String addressId;

        /**
         * 服务类型
         */
        @ApiModelProperty("服务类型")
        private String serviceType;

        /**
         * 服务流程
         */
        @ApiModelProperty("服务流程")
        private String serviceFlow;

        /**
         * 订单原始价格
         */
        @ApiModelProperty("订单原始价格")
        private Long originalPrice;

        /**
         * 实际支付价格
         */
        @ApiModelProperty("实际支付价格")
        private Long finalPrice;

        /**
         * 服务时间
         */
        @ApiModelProperty("服务时间")
        private String serviceTime;

        /**
         * 使用的优惠券ID
         */
        @ApiModelProperty("使用的优惠券ID")
        private String couponId;

        /**
         * 交接方式 (0 钥匙, 1密码)
         */
        @ApiModelProperty("交接方式 (0 钥匙, 1密码)")
        private Integer handoverType;

        /**
         * 交接方式详细
         */
        @ApiModelProperty("交接方式详细")
        private String handover;

        /**
         * 订单备注
         */
        @ApiModelProperty("订单备注")
        private String remarks;

        /**
         * 支付完成时间
         */
        @ApiModelProperty("支付完成时间")
        private Long payTime;


    }
}
