package com.tencent.wxcloudrun.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.JsonNode;
import com.tencent.wxcloudrun.base.FieldOrder;
import com.tencent.wxcloudrun.vo.PageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ExamQuestionDTO {

    @Data
    @ApiModel(value = "试卷列表查询")
    public static class ExamListQuery  extends PageSize {

        @ApiModelProperty(value = "试卷类型，1单选 2多选 3判断 4主观题")
        private Integer type;
        @ApiModelProperty(value = "考试类型 1，猫 2 狗")
        private Integer serviceType;
        private List<FieldOrder> orders;

    }

    @Data
    @ApiModel(value = "试卷")
    public static class Base {

        /**
         * 主键ID，自增长
         */

        @ApiModelProperty(value = "主键ID，自增长", example = "1")
        private Long id;

        /**
         * 题号
         */

        @ApiModelProperty(value = "题号", example = "1")
        private Integer number;

        /**
         * 试卷类型，1单选 2多选 3判断 4主观题
         */

        @ApiModelProperty(value = "试卷类型，1单选 2多选 3判断 4主观题", example = "1")
        private Integer type;

        /**
         * 试卷类型 1，猫 2 狗
         */

        @ApiModelProperty(value = "试卷类型 1，猫 2 狗", example = "1")
        private Integer serviceType;

        /**
         * 题干
         */

        @ApiModelProperty(value = "题干", example = "这是题目内容")
        private String content;

        /**
         * 选项(JSON格式: {"A":"选项内容",...})
         */

        @ApiModelProperty(value = "选项(JSON格式: {\"A\":\"选项内容\",...})", example = "{\"A\":\"选项A\",\"B\":\"选项B\"}")
        private String optionsType;

        /**
         * 单选正确答案
         */

        @ApiModelProperty(value = "单选正确答案", example = "A")
        private String correctAnswer;

        /**
         * 多选答案
         */

        @ApiModelProperty(value = "多选答案", example = "[\"A\",\"B\"]")
        private String multiCorrectAnswer;

        /**
         * 判断答案
         */

        @ApiModelProperty(value = "判断答案", example = "true")
        private String judgmentAnswer;

        /**
         * 主观题答案
         */

        @ApiModelProperty(value = "主观题答案", example = "这是主观题答案")
        private String subjectiveAnswer;

        /**
         * 本题分值
         */

        @ApiModelProperty(value = "本题分值", example = "10")
        private Integer standardScore;

        /**
         * 答案解析
         */

        @ApiModelProperty(value = "答案解析", example = "这是答案解析")
        private String analysis;

        /**
         * 创建时间
         */

        @ApiModelProperty(value = "创建时间", example = "1672531200000")
        private Long createTime;

        /**
         * 更新时间
         */

        @ApiModelProperty(value = "更新时间", example = "1672531200000")
        private Long updateTime;

        /**
         * 扩展字段，JSON格式数据
         */

        @ApiModelProperty(value = "扩展字段，JSON格式数据", example = "{\"key\":\"value\"}")
        private String ext;

    }
}
