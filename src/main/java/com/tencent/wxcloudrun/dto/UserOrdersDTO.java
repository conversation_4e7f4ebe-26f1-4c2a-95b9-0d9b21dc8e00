package com.tencent.wxcloudrun.dto;

import com.tencent.wxcloudrun.vo.PageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

@Data
public class UserOrdersDTO {

    @Data
    @ApiModel(value = "搜索喂养员")
    public static class SearchServiceUserCondition extends PageSize {

        @ApiModelProperty(value = "按照距离正序排序")
        private boolean orderByDistance;

        @ApiModelProperty(value = "按评分倒序排序")
        private boolean orderByScore;

        @ApiModelProperty(value = "服务类型")
        private int serviceType;

        /**
         * 纬度（WGS84坐标系）
         */
        @ApiModelProperty(value = "纬度（WGS84坐标系）", example = "39.9042")
        private BigDecimal latitude;

        /**
         * 经度（WGS84坐标系）
         */
        @ApiModelProperty(value = "经度（WGS84坐标系）", example = "116.4074")
        private BigDecimal longitude;

    }

    @Data
    @ApiModel(value = "用户订单对象支付")
    public static class Pay{

        @ApiModelProperty(value = "订单编号")
        private String orderNo;

        @ApiModelProperty(value = "用户id")
        private String userId;

        @ApiModelProperty(value = "价格")
        private String price;
    }

    @Data
    @ApiModel(value = "退款说明")
    public static class RatePay{

        @ApiModelProperty(value = "退款百分比")
        private long rate;

        @ApiModelProperty(value = "退款剩余时长")
        private long lowTime;

        public String getRatePayString() {
            if (rate == 0) {
                return "可退款";
            }
            else {
                return String.format("拒绝上门时间不足%d小时，取消需扣款%d%%",lowTime, rate);
            }
        }
    }

    @Data
    @ApiModel(value = "用户订单对象列表")
    public static class OrderListQuery extends  PageSize {
        @ApiModelProperty(value = "订单状态 全部 0, 待接单 1, 进行中 2 已完成 3, 待评价 4")
        private int orderStatus;
        @ApiModelProperty(value = "用户id", example = "user_001")
        private String userId;
    }

    @Getter
    @Setter
    @ApiModel(value = "用户订单对象新增")
    public static class Add extends UserOrdersDTO.Base {

    }

    @Getter
    @Setter
    @ApiModel(value = "用户订单对象修改")
    public static class Update extends UserOrdersDTO.Base {

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ServiceReview {
        private int score;
        private String review;
        private String orderNo;
        private String userId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateOrderVO {

        @ApiModelProperty("下单用户id--openId")
        private String userId;

        /**
         * 服务类型
         */
        @ApiModelProperty("服务类型")
        private Integer serviceType;

        @ApiModelProperty("服务时间")
        private List<String> serviceTime; // 格式 2022-01-01

        /**
         * 交接方式
         */
        @ApiModelProperty("交接方式")
        private Handover handover;

        @ApiModelProperty("单次服务报价-单位分")
        private Long servicePrice;

        /**
         * 服务宠物ID
         */
        @ApiModelProperty("服务宠物ID-暂时只有一个")
        private long petId;

        /**
         * 服务地址ID
         */
        @ApiModelProperty("服务地址ID")
        private long addressId;

        @ApiModelProperty("指定喂养员id--openId")
        private String serviceUserId;

        /**
         * 订单备注
         */
        @ApiModelProperty("订单备注")
        private String remark;

        /**
         * 使用的优惠券ID
         */
        @ApiModelProperty("使用的优惠券ID")
        private List<Long> couponIds;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Handover {

        @ApiModelProperty("交接方式 ( 1密码，2 钥匙)")
        private Integer handoverType;

        private String handoverRemark;
    }

    @Data
    @ApiModel(value = "用户订单对象")
    public static class Base {


        @ApiModelProperty(value = "订单编号, 32位随机字母")
        private String orderNo;
        /**
         * 微信支付单号
         */
        @ApiModelProperty("微信支付单号")
        private String wxTransactionId;

        /**
         * 下单用户ID
         */
        @ApiModelProperty("下单用户ID")
        private String userId;

        /**
         * 服务宠物ID,可能有多个
         */
        @ApiModelProperty("服务宠物ID,可能有多个")
        private String petId;

        /**
         * 服务地址ID
         */
        @ApiModelProperty("服务地址ID")
        private String addressId;

        /**
         * 服务类型
         */
        @ApiModelProperty("服务类型")
        private String serviceType;

        /**
         * 服务流程
         */
        @ApiModelProperty("服务流程")
        private String serviceFlow;

        /**
         * 订单原始价格
         */
        @ApiModelProperty("订单原始价格")
        private Long originalPrice;

        /**
         * 实际支付价格
         */
        @ApiModelProperty("实际支付价格")
        private Long finalPrice;

        /**
         * 服务时间
         */
        @ApiModelProperty("服务时间")
        private String serviceTime;

        /**
         * 使用的优惠券ID
         */
        @ApiModelProperty("使用的优惠券ID")
        private String couponId;

        /**
         * 交接方式 (0 钥匙, 1密码)
         */
        @ApiModelProperty("交接方式 (0 钥匙, 1密码)")
        private Integer handoverType;

        /**
         * 交接方式详细
         */
        @ApiModelProperty("交接方式详细")
        private String handover;

        /**
         * 订单备注
         */
        @ApiModelProperty("订单备注")
        private String remarks;

        /**
         * 支付完成时间
         */
        @ApiModelProperty("支付完成时间")
        private Long payTime;


    }
}
