
package com.tencent.wxcloudrun.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

public class WxPayOrder {
    @Data
    @ApiModel(value = "用户订单对象支付")
    public static class WxOrder{

        @ApiModelProperty(value = "结果通知类型", required = true, example = "2", notes = "1: 云函数, 2: 云托管")
        private int callbackType;

        @ApiModelProperty(value = "结果通知回调云函数环境", required = true, example = "test-123", notes = "接收微信支付异步通知回调的云函数所在的环境 ID")
        private String envId;

        @ApiModelProperty(value = "结果通知回调云函数名", required = true, example = "paycallback", notes = "接收微信支付异步通知回调的云函数名")
        private String functionName;

        @ApiModelProperty(value = "子商户号", required = true, example = "1900000109", notes = "微信支付分配的子商户号")
        private String subMchId;

        @ApiModelProperty(value = "随机字符串", required = true, example = "5K8264ILTKCH16CQ2502SI8ZNMTM67VS", notes = "随机字符串，不长于32位")
        private String nonceStr;

        @ApiModelProperty(value = "商品描述", required = true, example = "腾讯充值中心-QQ会员充值", notes = "商品简单描述")
        private String body;

        @ApiModelProperty(value = "商户订单号", required = true, example = "1217752501201407033233368018", notes = "商户系统内部订单号，要求32个字符内")
        private String outTradeNo;

        @ApiModelProperty(value = "总金额", required = true, example = "888", notes = "订单总金额，单位为分")
        private Long totalFee;

        @ApiModelProperty(value = "终端IP", required = true, example = "*************", notes = "支持IPV4和IPV6两种格式的IP地址")
        private String spbillCreateIp;

        @ApiModelProperty(value = "交易类型", required = true, example = "JSAPI", notes = "小程序取值如下：JSAPI")
        private String tradeType;

        // 可选字段
        @ApiModelProperty(value = "结果通知回调云托管信息", notes = "接收微信支付异步通知回调的云托管信息")
        private Map<String, Object> container;

        @ApiModelProperty(value = "设备号", example = "111", notes = "终端设备号(门店号或收银设备ID)，PC网页或公众号内支付请传WEB")
        private String deviceInfo;

        @ApiModelProperty(value = "商品详情", notes = "商品详细描述，对于使用单品优惠的商户，该字段必须按照规范上传")
        private String detail;

        @ApiModelProperty(value = "附加数据", example = "说明", notes = "附加数据，在查询API和支付通知中原样返回")
        private String attach;

        @ApiModelProperty(value = "货币类型", example = "CNY", notes = "符合ISO 4217标准的三位字母代码，默认人民币：CNY")
        private String feeType;

        @ApiModelProperty(value = "交易起始时间", example = "20091225091010", notes = "订单生成时间，格式为yyyyMMddHHmmss")
        private String timeStart;

        @ApiModelProperty(value = "交易结束时间", example = "20091227091010", notes = "订单失效时间，格式为yyyyMMddHHmmss")
        private String timeExpire;

        @ApiModelProperty(value = "订单优惠标记", example = "WXG", notes = "订单优惠标记，代金券或立减优惠功能的参数")
        private String goodsTag;

        @ApiModelProperty(value = "指定支付方式", example = "no_credit", notes = "no_credit--指定不能使用信用卡支付")
        private String limitPay;

        @ApiModelProperty(value = "用户标识", example = "oUpF8uMuAJO_M2pxb1Q9zNjWeS6o", notes = "trade_type=JSAPI，此参数必传，用户在商户appid下的唯一标识")
        private String openid;

        @ApiModelProperty(value = "用户子标识", example = "oUpF8uMuAJO_M2pxb1Q9zNjWeS6o", notes = "trade_type=JSAPI，此参数必传，用户在子商户appid下的唯一标识")
        private String subOpenid;

        @ApiModelProperty(value = "电子发票入口开放标识", example = "Y", notes = "Y，传入Y时，支付成功消息和支付详情页将出现开票入口")
        private String receipt;

        @ApiModelProperty(value = "场景信息", notes = "该字段常用于线下活动时的场景信息上报，支持上报实际门店信息")
        private Map<String, Object> sceneInfo;

        @Override
        public String toString() {
            return "WeChatPayRequest{" +
                    "callbackType=" + callbackType +
                    ", envId='" + envId + '\'' +
                    ", functionName='" + functionName + '\'' +
                    ", subMchId='" + subMchId + '\'' +
                    ", nonceStr='" + nonceStr + '\'' +
                    ", body='" + body + '\'' +
                    ", outTradeNo='" + outTradeNo + '\'' +
                    ", totalFee=" + totalFee +
                    ", spbillCreateIp='" + spbillCreateIp + '\'' +
                    ", tradeType='" + tradeType + '\'' +
                    ", container=" + container +
                    ", deviceInfo='" + deviceInfo + '\'' +
                    ", detail='" + detail + '\'' +
                    ", attach='" + attach + '\'' +
                    ", feeType='" + feeType + '\'' +
                    ", timeStart='" + timeStart + '\'' +
                    ", timeExpire='" + timeExpire + '\'' +
                    ", goodsTag='" + goodsTag + '\'' +
                    ", limitPay='" + limitPay + '\'' +
                    ", openid='" + openid + '\'' +
                    ", subOpenid='" + subOpenid + '\'' +
                    ", receipt='" + receipt + '\'' +
                    ", sceneInfo=" + sceneInfo +
                    '}';
        }
    }

    @Data
    public static class WeChatPayResponse {

        // 必填字段
        @ApiModelProperty(value = "返回状态码", required = true, example = "SUCCESS", notes = "SUCCESS/FAIL 此字段是通信标识，非交易标识")
        private String returnCode;

        @ApiModelProperty(value = "返回信息", example = "签名失败", notes = "返回信息，如非空，为错误原因")
        private String returnMsg;

        // 以下字段在 returnCode 为 SUCCESS 时返回
        @ApiModelProperty(value = "小程序中发起支付所需信息", required = true, notes = "小程序端调用 wx.requestPayment 所需信息")
        private Payment payment;

        @ApiModelProperty(value = "服务商的APPID", required = true, example = "wxd678efh567hg6787", notes = "服务商商户的APPID")
        private String appid;

        @ApiModelProperty(value = "商户号", required = true, example = "1900000109", notes = "调用接口提交的商户号")
        private String mchId;

        @ApiModelProperty(value = "小程序的APPID", required = true, example = "wx8888888888888888", notes = "微信分配的小程序ID")
        private String subAppid;

        @ApiModelProperty(value = "子商户号", required = true, example = "1900000109", notes = "微信支付分配的子商户号")
        private String subMchId;

        @ApiModelProperty(value = "设备号", example = "013467007045764", notes = "调用接口提交的终端设备号")
        private String deviceInfo;

        @ApiModelProperty(value = "随机字符串", required = true, example = "5K8264ILTKCH16CQ2502SI8ZNMTM67VS", notes = "微信返回的随机字符串")
        private String nonceStr;

        @ApiModelProperty(value = "签名", required = true, example = "C380BEC2BFD727A4B6845133519F3AD6", notes = "微信返回的签名，详见签名算法")
        private String sign;

        @ApiModelProperty(value = "业务结果", required = true, example = "SUCCESS", notes = "SUCCESS/FAIL")
        private String resultCode;

        @ApiModelProperty(value = "错误代码", example = "SYSTEMERROR", notes = "详细参见第6节错误列表")
        private String errCode;

        @ApiModelProperty(value = "错误代码描述", example = "系统错误", notes = "错误返回的信息描述")
        private String errCodeDes;

        // 以下字段在 returnCode 和 resultCode 都为 SUCCESS 时返回
        @ApiModelProperty(value = "交易类型", required = true, example = "JSAPI", notes = "调用接口提交的交易类型，取值如下：JSAPI")
        private String tradeType;

        @ApiModelProperty(value = "预支付交易会话标识", required = true, example = "wx201410272009395522657a690389285100", notes = "微信生成的预支付回话标识，用于后续接口调用中使用，该值有效期为2小时")
        private String prepayId;

    }
    @Data
    public static class Payment {
        @ApiModelProperty(value = "时间戳", required = true, example = "1696924800", notes = "时间戳，单位：秒")
        private String timeStamp;

        @ApiModelProperty(value = "随机字符串", required = true, example = "5K8264ILTKCH16CQ2502SI8ZNMTM67VS", notes = "随机字符串，不长于32位")
        private String nonceStr;

        @ApiModelProperty(value = "支付包", required = true, example = "prepay_id=wx201410272009395522657a690389285100", notes = "统一下单接口返回的 prepay_id 参数值")
        private String packageValue;

        @ApiModelProperty(value = "签名类型", required = true, example = "MD5", notes = "签名类型，默认为MD5")
        private String signType;

        @ApiModelProperty(value = "签名", required = true, example = "C380BEC2BFD727A4B6845133519F3AD6", notes = "签名，详见签名算法")
        private String paySign;
    }
}
