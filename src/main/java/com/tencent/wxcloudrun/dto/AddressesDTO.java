package com.tencent.wxcloudrun.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 用户地理位置数据存储
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
@Data
//@Accessors(chain = true)
//@ApiModel(value = "地址对象")
public class AddressesDTO {
    
    @Data
    @ApiModel(value = "地址对象新增")
    public static class Add extends Base {
    
    }
    
    @Data
    @ApiModel(value = "地址对象修改")
    public static class Update extends Base {
        
        /**
         * 地址唯一标识
         */
        @ApiModelProperty(value = "地址唯一标识", example = "123456")
        private String id;
    }
    
    @Data
    @ApiModel(value = "地址对象")
    public static class Base {
        
        /**
         * 关联用户ID
         */
        @ApiModelProperty(value = "关联用户ID", example = "user_001")
        private String userId;
        
        /**
         * 地址别名（如：公司/家）
         */
        @ApiModelProperty(value = "地址别名（如：公司/家）", example = "公司")
        private String alias;
        
        /**
         * 地址类型,1 用户地址2，喂养员地址
         */
        @ApiModelProperty(value = "地址类型,1 用户地址2，喂养员地址", example = "1")
        private String addressesType;
        
        /**
         * 联系人姓名
         */
        @ApiModelProperty(value = "联系人姓名", example = "张三")
        private String contactName;
        
        /**
         * 联系电话
         */
        @ApiModelProperty(value = "联系电话", example = "13800138000")
        private String contactPhone;
        
        /**
         * 详细地址
         */
        @ApiModelProperty(value = "详细地址", example = "北京市朝阳区XX路XX号")
        private String fullAddress;
        
        /**
         * 省
         */
        @ApiModelProperty(value = "省", example = "北京市")
        private String province;
        
        /**
         * 市
         */
        @ApiModelProperty(value = "市", example = "北京市")
        private String city;
        
        /**
         * 区
         */
        @ApiModelProperty(value = "区", example = "朝阳区")
        private String district;
        
        /**
         * 纬度（WGS84坐标系）
         */
        @ApiModelProperty(value = "纬度（WGS84坐标系）", example = "39.9042")
        private BigDecimal latitude;
        
        /**
         * 经度（WGS84坐标系）
         */
        @ApiModelProperty(value = "经度（WGS84坐标系）", example = "116.4074")
        private BigDecimal longitude;
        
        /**
         * 是否默认地址（0否 1是）
         */
        @ApiModelProperty(value = "是否默认地址（0否 1是）", example = "true")
        private Boolean isDefault;
        
    }
}