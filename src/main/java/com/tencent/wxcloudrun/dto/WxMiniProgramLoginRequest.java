package com.tencent.wxcloudrun.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 微信小程序登录请求DTO
 */
@Data
@ApiModel(value = "微信小程序登录请求", description = "微信小程序登录请求参数")
public class WxMiniProgramLoginRequest {

    @Data
    @ApiModel(value = "登录信息")
    public static class LoginCode {
        /**
         * 微信小程序登录凭证code
         */
        @ApiModelProperty(value = "微信小程序登录凭证code", required = true)
        private String code;

    }

    @Data
    @ApiModel(value = "登录状态")
    public static class loginUser {

        /**
         * 用户昵称
         */
        @ApiModelProperty(value = "用户昵称")
        private String nickname;

        /**
         * 用户头像URL
         */
        @ApiModelProperty(value = "用户头像URL")
        private String avatarUrl;

        /**
         * 手机号
         */
        @ApiModelProperty(value = "手机号")
        private String mobile;
        /**
         * 手机号
         */
        @ApiModelProperty(value = "openid")
        private String openId;

        /**
         * 邀请码
         */
        @ApiModelProperty(value = "邀请码")
        private String inviteCode;


    }

}
