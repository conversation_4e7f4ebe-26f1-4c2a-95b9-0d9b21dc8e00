package com.tencent.wxcloudrun.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 宠物详细信息档案
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
@Data
public class PetsDTO {
        
        @Data
        @ApiModel(value = "宠物对象新增")
        public static class Add extends PetsDTO.Base {
        
        }
        
        @Data
        @ApiModel(value = "宠物修改")
        public static class Update extends PetsDTO.Base {
                
                /**
                 * 自增长
                 */
                @ApiModelProperty(value = "id", example = "1")
                private long id;
        }
        
        @Data
        @ApiModel(value = "宠物对象")
        public static class Base {
                /**
                 * 所属用户ID
                 */
                @ApiModelProperty(value = "所属用户ID", example = "user_001")
                private String userId;
                
                /**
                 * 宠物名称
                 */
                @ApiModelProperty(value = "宠物名称", example = "毛毛")
                private String name;
                
                /**
                 * 宠物类型
                 */
                @ApiModelProperty(value = "宠物类型 ()0 猫, 1 狗)", example = "0")
                private String petType;
                
                /**
                 * 年龄（单位：岁）
                 */
                @ApiModelProperty(value = "年龄（单位：岁）", example = "3")
                private String age;
                /**
                 * 年龄（单位：岁）
                 */
                @ApiModelProperty(value = "生日", example = "时间戳格式")
                private String petsDatetime;
                
                /**
                 * 性格
                 */
                @ApiModelProperty(value = "性格", example = "温顺")
                private String personality;
                
                /**
                 * 性别
                 */
                @ApiModelProperty(value = "性别", example = "公")
                private String gender;
                
                /**
                 * 是否绝育（0否 1是）
                 */
                @ApiModelProperty(value = "是否绝育（0否 1是）", example = "1")
                private Integer isNeutered;
                
                /**
                 * 是否疫苗（0否 1是）
                 */
                @ApiModelProperty(value = "是否疫苗（0否 1是）", example = "1")
                private Integer isVaccine;
                
                /**
                 * 是否健康（0否 1是）
                 */
                @ApiModelProperty(value = "是否健康（0否 1是）", example = "1")
                private Integer isHealthy;
                
                /**
                 * 健康状态备注
                 */
                @ApiModelProperty(value = "健康状态备注", example = "偶尔会有轻微皮肤病，已治愈")
                private String healthyRemarks;
                
                /**
                 * 宠物照片 可以是多个根据逗号分割
                 */
                @ApiModelProperty(value = "宠物照片 可以是多个根据逗号分割", example = "http://example.com/pet1.jpg,http://example.com/pet2.jpg")
                private String petsUrl;
                
                /**
                 * 备注
                 */
                @ApiModelProperty(value = "备注", example = "喜欢玩球")
                private String remarks;
        }
}