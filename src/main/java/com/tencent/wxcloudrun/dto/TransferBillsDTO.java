package com.tencent.wxcloudrun.dto;

import com.wechat.pay.java.core.cipher.Encryption;
import com.wechat.pay.java.core.util.StringUtil;
import com.wechat.pay.java.service.transferbatch.model.InitiateBatchTransferRequest;
import com.wechat.pay.java.service.transferbatch.model.TransferDetailInput;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;
import java.util.function.UnaryOperator;
@Data
public class TransferBillsDTO {


    @Data
    @ApiModel(value = "转账回调")
    @Builder
    public static class TransferBillsResult {
        @ApiModelProperty(value = "商户订单号")
        @SerializedName("out_bill_no")
        private String out_bill_no;
        @ApiModelProperty(value = "微信转账单号")
        @SerializedName("transfer_bill_no")
        private String transferBillNo;
        @ApiModelProperty(value = "单据创建时间")
        @SerializedName("create_time")
        private String createTime;
        @ApiModelProperty(value = "单据状态")
        @SerializedName("state")
        private String state;
        @ApiModelProperty(value = "失败原因")
        @SerializedName("fail_reason")
        private String failReason;
        @ApiModelProperty(value = "跳转领取页面的package信息")
        @SerializedName("package_info")
        private String packageInfo;

    }

    @Data
    @ApiModel(value = "转账入参")
    public static class TransferBillsRequest {
        @SerializedName("appid")
        private String appid;
        @ApiModelProperty(value = "商户订单号")
        @SerializedName("out_bill_no")
        private String outBillNo;
        @ApiModelProperty(value = "营销ID")
        @SerializedName("transfer_scene_id")
        private String transferSceneId;

        @SerializedName("openid")
        private String openid;
        @ApiModelProperty(value = "转账金额")
        @SerializedName("transfer_amount")
        private Long transferAmount;
        @ApiModelProperty(value = "转账备注")
        @SerializedName("transfer_remark")
        private String transferRemark;


        @SerializedName("transfer_scene_report_infos")
        private List<TransferSceneReportInfo> transferSceneReportInfos = new ArrayList<>();

        @SerializedName("notify_url")
        private String notifyUrl;


        public TransferBillsRequest cloneWithCipher(UnaryOperator<String> s) {
            TransferBillsRequest copy = new TransferBillsRequest();
            copy.appid = this.appid;
            copy.outBillNo = this.outBillNo;
            copy.transferSceneId = this.transferSceneId;
            copy.openid = this.openid;
            copy.transferAmount = this.transferAmount;
            copy.transferRemark = this.transferRemark;
            if (this.transferSceneReportInfos != null && !this.transferSceneReportInfos.isEmpty()) {
                copy.transferSceneReportInfos = new ArrayList<>();

                for (TransferSceneReportInfo val : this.transferSceneReportInfos) {
                    if (val != null) {
                        copy.transferSceneReportInfos.add(val);
                    }
                }
            }
            copy.transferSceneId = this.transferSceneId;
            copy.notifyUrl = this.notifyUrl;
            return copy;
        }

    }
    @Builder
    @Data
    @ApiModel(value = "transfer_scene_report_infos")
    public static class TransferSceneReportInfo {
        @SerializedName("info_type")
        private String infoType;

        @SerializedName("info_content")
        private String infoContent;
    }

}
