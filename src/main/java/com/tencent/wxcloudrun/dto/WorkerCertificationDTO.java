package com.tencent.wxcloudrun.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
public class WorkerCertificationDTO implements Serializable {

    @Data
    @ApiModel(value = "添加喂养员认证类")
    public static class ADD extends Base {
    }

    @Data
    @ApiModel(value = "添加喂养员认证类")
    public static class UPDATE extends Base {
    }

    @Data
    @ApiModel(value = "喂养员认证类")
    public static class Base {

        @ApiModelProperty(value = "ID ")
        private Long id;

        @ApiModelProperty(value = "用户ID ")
        private String userId;
        @ApiModelProperty(value = "认证类型 0猫，1狗，2其他")
        private int certificationType;
        @ApiModelProperty(value = "认证编号")
        private String certificationNumber;
        @ApiModelProperty(value = "添加时间")
        private Long createTime;
        @ApiModelProperty(value = "更新时间")
        private Long updateTime;
        private String ext;

    }
}
