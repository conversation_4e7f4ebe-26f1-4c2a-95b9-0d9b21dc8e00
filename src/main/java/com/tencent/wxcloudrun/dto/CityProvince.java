package com.tencent.wxcloudrun.dto;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

@Data
public class CityProvince {
    private List<Province> provinces;

    @Data
    public static class City {
        private String name;
        private String code;
        private List<Area> area;
    }

    @Data
    public static class Area {
        private String name;
        private String code;

    }

    @Data
    public static class Province {
        private String name;
        private String code;
        private List<City> city;
    }

    public CityProvince() {
        this.provinces = loadData();
    }

    private List<CityProvince.Province> loadData() {
        ObjectMapper mapper = new ObjectMapper();
        ClassPathResource resource = new ClassPathResource("data/cities.json");

        try (InputStream inputStream = resource.getInputStream()) {
            return mapper.readValue(inputStream, new TypeReference<List<Province>>() {
            });
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}