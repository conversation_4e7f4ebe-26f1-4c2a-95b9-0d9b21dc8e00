package com.tencent.wxcloudrun;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@MapperScan(basePackages = {"com.tencent.wxcloudrun.dao"})
@EnableScheduling
public class WxCloudRunApplication {  

  public static void main(String[] args) {
    ConfigurableApplicationContext context = SpringApplication.run(WxCloudRunApplication.class, args);
  
    Environment environment = context.getEnvironment();
    String serverPort = environment.getProperty("server.port");
    System.out.println("Swagger 地址: http://localhost:" + serverPort + "/doc.html");
  
  }
}
