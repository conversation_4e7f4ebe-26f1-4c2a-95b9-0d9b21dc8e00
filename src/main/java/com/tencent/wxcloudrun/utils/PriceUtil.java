package com.tencent.wxcloudrun.utils;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.Lists;
import com.tencent.wxcloudrun.enums.CouponTypeEnum;
import com.tencent.wxcloudrun.model.Coupons;
import com.tencent.wxcloudrun.model.UserCoupons;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

public class PriceUtil {

    private static final List<Long> SERVICE_COUPON_IDS = Lists.newArrayList(101L, 100L);

    private static final List<Long> USER_REWARD_COUPON_IDS = Lists.newArrayList(200L, 204L);

    private static final List<Long> USER_COUPON_IDS = Lists.newArrayList(201L, 202L);

    public static boolean validaUserCouponIds(List<Long> couponIds) {
        if (CollectionUtils.isEmpty(couponIds)) {
            return true;
        }
        if (couponIds.size() == 1) {
            return true;
        }
        if (couponIds.size() > 2) {
            return false;
        }
        // 全匹配在无门槛减10里面
        if (couponIds.stream().allMatch(USER_REWARD_COUPON_IDS::contains)) {
            return false;
        }
        // 全匹配在减5和减8里面
        if (couponIds.stream().allMatch(USER_COUPON_IDS::contains)) {
            return false;
        }
        return true;
    }

    /**
     * 计算下单金额
     *
     * @param
     * @param total
     * @return
     */
    public static long calCreateOrderPrice(List<Coupons> coupons, long total, List<String> serviceTimes) {
        long[] res = {total};
        // 端午假期期间 满三天 100-5
        if (serviceTimes.size() >= 3) {
            if (System.currentTimeMillis() > 1748620800000L && System.currentTimeMillis() < 1748880000000L) {
                if (total > 10000) {
                    res[0] = res[0] - 500;
                }
            }
        }
        if (CollectionUtils.isEmpty(coupons)) {
            return res[0];
        }

        coupons.forEach(val -> {
            if (USER_REWARD_COUPON_IDS.contains(val.getId())) {
                res[0] = res[0] - 1000L;
            }
            if (val.getId() == 202) {
                res[0] = res[0] - 500L;
            }
            if (val.getId() == 201) {
                res[0] = res[0] - 800L;
            }
        });

        return res[0];
    }

    /**
     * 计算奖励金额接口
     *
     * @param
     * @return
     */
    public static long calRewardPrice(Long pay, Long distributionPrice, Long servicePrice) {
        if (pay == null) {
            return 0;
        }
        if (distributionPrice == null) {
            distributionPrice = 0L;
        }
        if (servicePrice == null) {
            servicePrice = 0L;
        }
        if (pay >= distributionPrice + servicePrice) {
            return 0;
        }
        return distributionPrice + servicePrice - pay;
    }

    /**
     * 喂养员分账收口
     *
     * @param total
     * @return
     */
    public static long calServicePrice(long total) {
        return calRatePrice(total, 80);
    }

    /**
     * 喂养员分账收口
     *
     * @param total
     * @return
     */
    public static long calServicePrice(long total, Long couponId, String serviceTimes) {
        List<String> times = JSONUtil.toList(serviceTimes, String.class);
        if (couponId != null && SERVICE_COUPON_IDS.contains(couponId)) {
            // 只免第一天佣金
            long perPrice = total / times.size();
            return perPrice + calRatePrice(total - perPrice, 80);
        }
        return calRatePrice(total, 80);
    }

    /**
     * 分销分账收口
     *
     * @param total
     * @return
     */
    public static long calDistributorPrice(long total) {
        return calRatePrice(total, 10);
    }

    /**
     * 这里只做计算，可能会出现大于支付金额的情况，需要在分账的时候做处理
     *
     * @param total
     * @param rate  -> 20% 传 20
     * @return
     */
    public static long calRatePrice(long total, long rate) {
        return (long) Math.ceil((double) (total * rate) / 100);
    }

    public static void main(String[] args) {

        System.out.println(calRatePrice(1000, 20));
    }
}
