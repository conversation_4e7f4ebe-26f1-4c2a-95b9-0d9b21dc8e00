package com.tencent.wxcloudrun.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.tencent.wxcloudrun.vo.UserSheetSubmitVo;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class SheetUtil {

    private static final String A = "A";
    private static final String B = "B";
    private static final String C = "C";
    private static final String D = "D";

    private static final List<String> AD = Lists.newArrayList(A, D);


    public static List<UserSheetSubmitVo.Sheet> calSheetScore(List<UserSheetSubmitVo.Sheet> sheetAnswers) {

        sheetAnswers.forEach(sheet -> {
            // 单选
            if (sheet.getType() == 1) {
                if (!StrUtil.isEmpty(sheet.getSingleChoose()) && sheet.getSingleChoose().equals(sheet.getCorrectAnswer())) {
                    sheet.setScore(sheet.getStandardScore());
                } else {
                    sheet.setSingleChoose("用户答案是：" + sheet.getSingleChoose() + ",答错了");
                }
            }
            if (sheet.getType() == 2) {
                List<String> multiCorrectAnswerList = JSONUtil.toList(JSONUtil.parseArray(sheet.getMultiCorrectAnswer()), String.class);
                if (check(sheet.getMultipleChoose(), multiCorrectAnswerList)) {
                    sheet.setScore(sheet.getStandardScore());
                }
            }
            if (sheet.getType() == 3) {
                if (!StrUtil.isEmpty(sheet.getJudgment()) && sheet.getJudgment().equals(sheet.getJudgmentAnswer())) {
                    sheet.setScore(sheet.getStandardScore());
                    sheet.setJudgment("true");
                } else {
                    sheet.setSingleChoose("用户答案是：" + sheet.getJudgment() + ",答错了");
                }
            }
        });
        return sheetAnswers;
    }

    public static boolean check(List<String> origin, List<String> answer) {
        if (Objects.isNull(origin) || Objects.isNull(answer)) {
            return false;
        }
        if (origin.size() != answer.size()) {
            return false;
        }
        return origin.stream().anyMatch(answer::contains);
    }
}
