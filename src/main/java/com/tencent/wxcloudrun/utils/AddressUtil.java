package com.tencent.wxcloudrun.utils;
import org.locationtech.spatial4j.context.SpatialContext;
import org.locationtech.spatial4j.distance.DistanceUtils;
import org.locationtech.spatial4j.shape.Point;
import java.math.BigDecimal;

public class AddressUtil {
    public static String hideBuildingSimple(String address) {
        if (address == null || address.isEmpty()) {
            return address;
        }

        // 计算截取位置（隐藏最后5个字符）
        int endIndex = address.length() - 5;
        if (endIndex > 0) {
            return address.substring(0, endIndex) + "***";
        } else {
            // 地址长度 <=5 时，直接返回 ​***
            return "***";
        }
    }

    public static long calDistance(BigDecimal latitude, BigDecimal longitude, BigDecimal dtoLatitude, BigDecimal dtoLongitude) {

        if (latitude != null && longitude != null && dtoLatitude != null && dtoLongitude != null) {
            double lat1 = latitude.doubleValue();
            double lon1 = longitude.doubleValue();
            double lat2 = dtoLatitude.doubleValue();
            double lon2 = dtoLongitude.doubleValue();

            SpatialContext ctx = SpatialContext.GEO;
            Point point1 = ctx.getShapeFactory().pointXY(lon1, lat1);
            Point point2 = ctx.getShapeFactory().pointXY(lon2, lat2);
            double dis = ctx.calcDistance(point1, point2) * DistanceUtils.DEG_TO_KM * 1000;
            return (long) dis;

        }
        return 100000000000L;
    }

    public static void main(String[] args) {
        BigDecimal latitude = new BigDecimal("30.57417400");
        BigDecimal longitude = new BigDecimal("103.92376500");
        BigDecimal dtoLatitude = new BigDecimal("30.57432300");
        BigDecimal dtoLongitude = new BigDecimal("103.92280300");
        long l = calDistance(latitude, longitude, dtoLatitude, dtoLongitude);
        System.out.println(l);

    }
}
