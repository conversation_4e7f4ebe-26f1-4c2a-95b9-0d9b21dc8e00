package com.tencent.wxcloudrun.utils;

public class ShortSnowflakeId {
    // 起始时间戳（2020-01-01）
    private static final long START_TIMESTAMP = 1577808000000L;

    // 每部分占用的位数
    private static final long SEQUENCE_BITS = 7;  // 序列号位数（每毫秒127个）
    private static final long MACHINE_BITS = 3;   // 机器标识位数（最多8台机器）

    // 最大值（计算用）
    private static final long MAX_SEQUENCE = ~(-1L << SEQUENCE_BITS);
    private static final long MAX_MACHINE_ID = ~(-1L << MACHINE_BITS);

    // 默认实例（机器ID=0）
    private static final ShortSnowflakeId DEFAULT_INSTANCE = new ShortSnowflakeId(0);

    private final long machineId;    // 机器ID
    private long lastTimestamp = -1L;
    private long sequence = 0L;

    // 构造函数
    public ShortSnowflakeId(long machineId) {
        if (machineId > MAX_MACHINE_ID || machineId < 0) {
            throw new IllegalArgumentException("机器ID范围: 0~" + MAX_MACHINE_ID);
        }
        this.machineId = machineId;
    }

    // 实例方法
    public synchronized long nextId() {
        long currentTimestamp = System.currentTimeMillis();

        if (currentTimestamp < lastTimestamp) {
            throw new RuntimeException("系统时间回退！");
        }

        if (currentTimestamp == lastTimestamp) {
            sequence = (sequence + 1) & MAX_SEQUENCE;
            if (sequence == 0) {
                currentTimestamp = waitNextMillis(lastTimestamp);
            }
        } else {
            sequence = 0L;
        }

        lastTimestamp = currentTimestamp;

        return ((currentTimestamp - START_TIMESTAMP) << (SEQUENCE_BITS + MACHINE_BITS))
                | (machineId << SEQUENCE_BITS)
                | sequence;
    }

    // 静态方法：直接使用默认实例生成ID
    public static long generate() {
        return DEFAULT_INSTANCE.nextId();
    }

    private long waitNextMillis(long lastTimestamp) {
        long current = System.currentTimeMillis();
        while (current <= lastTimestamp) {
            current = System.currentTimeMillis();
        }
        return current;
    }

    public static void main(String[] args) {
        // 静态方法直接调用
        System.out.println(ShortSnowflakeId.generate()); // 输出：123456789012345

        // 自定义机器ID的用法
        ShortSnowflakeId customGenerator = new ShortSnowflakeId(2);
        System.out.println(customGenerator.nextId());
    }
}