package com.tencent.wxcloudrun.utils;

import com.tencent.wxcloudrun.config.ExcelColumn;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

public class ExcelExportUtils {

    private static final int DEFAULT_BUFFER_SIZE = 500;
    private static final int DEFAULT_COLUMN_WIDTH = 20;

    public static <T> void export(String fileName, List<T> dataList,
                                  HttpServletResponse response, int defaultWidth) throws IOException {
        // 参数校验
        if (dataList == null || dataList.isEmpty()) {
            throw new IllegalArgumentException("导出数据不能为空");
        }

        try (SXSSFWorkbook workbook = new SXSSFWorkbook(DEFAULT_BUFFER_SIZE)) {
            Sheet sheet = workbook.createSheet("服务记录");

            // 解析字段配置
            Class<?> clazz = dataList.get(0).getClass();
            List<FieldConfig> configs = parseFieldConfigs(clazz);

            // 创建表头
            createHeader(sheet, configs, workbook);

            // 填充数据
            fillData(sheet, dataList, configs, workbook);

            // 设置列宽
            setColumnWidth(sheet, configs, defaultWidth);

            // 设置响应
            setResponse(response, fileName);

            // 写入流
            workbook.write(response.getOutputStream());
            workbook.dispose();
        }
    }

    private static class FieldConfig {
        Field field;
        String name;
        int order;
        String dateFormat;
        boolean isTimestamp;
        int width = -1;
    }

    private static List<FieldConfig> parseFieldConfigs(Class<?> clazz) {
        List<FieldConfig> configs = new ArrayList<>();
        for (Field field : clazz.getDeclaredFields()) {
            ExcelColumn annotation = field.getAnnotation(ExcelColumn.class);
            if (annotation == null || annotation.ignore()) continue;

            FieldConfig config = new FieldConfig();
            config.field = field;
            config.name = annotation.name().isEmpty() ? field.getName() : annotation.name();
            config.order = annotation.order();
            config.dateFormat = annotation.dateFormat();
            config.isTimestamp = annotation.isTimestamp();
            config.width = annotation.width();
            configs.add(config);
        }
        configs.sort(Comparator.comparingInt(c -> c.order));
        return configs;
    }

    private static void createHeader(Sheet sheet, List<FieldConfig> configs, Workbook workbook) {
        Row headerRow = sheet.createRow(0);
        CellStyle headerStyle = createHeaderStyle(workbook);

        for (int i = 0; i < configs.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(configs.get(i).name);
            cell.setCellStyle(headerStyle);
        }
    }

    private static CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short)12);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        return style;
    }

    private static void fillData(Sheet sheet, List<?> dataList,
                                 List<FieldConfig> configs, Workbook workbook) {
        AtomicInteger rowNum = new AtomicInteger(1);
        dataList.forEach(data -> {
            Row row = sheet.createRow(rowNum.getAndIncrement());
            for (int i = 0; i < configs.size(); i++) {
                FieldConfig config = configs.get(i);
                try {
                    config.field.setAccessible(true);
                    Object value = config.field.get(data);
                    createCell(row, i, value, config, workbook);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException("字段访问失败: " + config.field.getName(), e);
                }
            }
        });
    }

    private static void createCell(Row row, int cellIndex, Object value,
                                   FieldConfig config, Workbook workbook) {
        Cell cell = row.createCell(cellIndex);

        try {
            if (config.isTimestamp && value != null) {
                handleTimestamp(cell, value, config);
            } else if (value instanceof Number) {
                handleNumber(cell, value, config, workbook);
            } else {
                cell.setCellValue(value != null ? value.toString() : "");
            }
        } catch (Exception e) {
            cell.setCellValue("格式错误");
        }
    }

    private static void handleTimestamp(Cell cell, Object value, FieldConfig config) {
        long timestamp = ((Number) value).longValue();
        Date date = new Date(timestamp);

        SimpleDateFormat sdf = new SimpleDateFormat(config.dateFormat);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));

        cell.setCellValue(sdf.format(date));
    }

    private static void handleNumber(Cell cell, Object value,
                                     FieldConfig config, Workbook workbook) {
        if (value instanceof Double || value instanceof Float) {
            cell.setCellValue(((Number) value).doubleValue());
        } else {
            cell.setCellValue(((Number) value).longValue());
        }
    }

    private static void setColumnWidth(Sheet sheet, List<FieldConfig> configs, int defaultWidth) {
        for (int i = 0; i < configs.size(); i++) {
            int width = configs.get(i).width > 0 ?
                    configs.get(i).width * 256 : // 1字符 ≈ 256单位
                    defaultWidth * 256;
            sheet.setColumnWidth(i, width);
        }
    }

    private static void setResponse(HttpServletResponse response, String fileName)
            throws IOException {
        String encodedName = URLEncoder.encode(fileName, "UTF-8")
                .replaceAll("\\+", "%20");

        response.reset();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition",
                "attachment; filename*=UTF-8''" + encodedName + ".xlsx");
        response.setCharacterEncoding("UTF-8");
    }
}