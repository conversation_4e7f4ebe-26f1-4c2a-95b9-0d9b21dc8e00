package com.tencent.wxcloudrun.utils;

import com.google.gson.Gson;
import com.wechat.pay.java.core.notification.Notification;
import com.wechat.pay.java.core.util.GsonUtil;
import com.wechat.pay.java.service.payments.model.Transaction;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

public class UniqueGenerator {
    public static String generateUniqueNickname() {
        String uuid = generateUuid(); // 生成 UUID 并去掉横线
        String time_str = String.valueOf(new Date().getTime());
        return "用户_" + uuid.substring(0, 4) + getLastSixChars(time_str);
    }

    public static String getLastSixChars(String str) {
        if (str == null || str.isEmpty()) {
            return "";
        }
        int startIndex = Math.max(0, str.length() - 6);
        return str.substring(startIndex);
    }

    public static String generateUuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    public static String generateInviteCode(String userId) {
        String base64Code = Base64.getEncoder().encodeToString(userId.getBytes());
        System.out.println("Generated Code: " + base64Code);
        return base64Code.replace('+', 'A')
                .replace('/', 'B')
                .replace('=', 'C');
    }


    public static void main(String[] args) {
        Gson gson = GsonUtil.getGson();
        String requestBody =   "{\"mchid\":\"097b4502f5d80989c107f9f42cd6af75\",\"transaction_id\":\"wx169868002220032\",\"out_trade_no\":\"169868002220032\",\"payer\":{\"openid\":\"oUy9Z7IDzKJsyRNose37IUa3fLeI\"},\"amount\":{\"payer_total\":17600, \"total\":20000},\"trade_state\":\"SUCCESS\"}";
        Transaction response = gson.fromJson(requestBody, Transaction.class);
        System.out.println(response);
    }
}