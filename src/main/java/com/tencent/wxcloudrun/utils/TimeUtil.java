package com.tencent.wxcloudrun.utils;

import com.tencent.wxcloudrun.dto.UserOrdersDTO;
import org.joda.time.DateTime;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class TimeUtil {

    /**
     * 将日期字符串（格式：yyyy-MM-dd）转换为北京时间的0点时间戳
     *
     * @param dateStr 日期字符串，例如 "2022-01-01"
     * @return 对应的北京时间0点时间戳（毫秒级）
     * @throws DateTimeException 如果日期格式无效
     */
    public static long convertDateToBeijingTimestamp(String dateStr) {
        // 解析日期字符串
        LocalDate date = LocalDate.parse(dateStr);

        // 附加北京时区信息并转为时间戳
        return date.atStartOfDay(ZoneId.of("Asia/Shanghai"))
                .toInstant()
                .toEpochMilli();
    }

    public static String format(long time) {
        return new DateTime(time).toString("yyyy年MM月dd日");
    }

    public static void main(String[] args) {
        List<String> serviceTime = Arrays.asList("2025-06-19");
        Long l = serviceTime.stream().map(TimeUtil::convertDateToBeijingTimestamp)
                .min(Comparator.comparingLong(Long::longValue))
                .get();
        System.out.println(l);
        UserOrdersDTO.RatePay ratePay = calCompensationPrice(l);
        System.out.println(ratePay.getRatePayString());
    }

    private static UserOrdersDTO.RatePay calCompensationPrice(long minTime) {
        long loneTime = System.currentTimeMillis();
        long oneHoursMillis = TimeUnit.HOURS.toMillis(12);
        long twoHoursMillis = TimeUnit.HOURS.toMillis(24);
        long threeHoursMillis = TimeUnit.HOURS.toMillis(48);
        long fourHoursMillis = TimeUnit.HOURS.toMillis(72);
        long rate = 0L;
        long lowTime = 0L;
        if (minTime - loneTime < oneHoursMillis) {
            rate = 80;
            lowTime = 12;
        } else if (minTime - loneTime < twoHoursMillis) {
            rate = 50;
            lowTime = 24;
        } else if (minTime - loneTime < threeHoursMillis) {
            rate = 20;
            lowTime = 48;
        } else {
            rate = 0;
            lowTime = 72;
        }
        UserOrdersDTO.RatePay ratePay = new UserOrdersDTO.RatePay();
        ratePay.setRate(rate);
        ratePay.setLowTime(lowTime);
        return ratePay;
    }


}

