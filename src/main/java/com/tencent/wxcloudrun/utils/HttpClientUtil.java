package com.tencent.wxcloudrun.utils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public class HttpClientUtil {

    /**
     * 发送 GET 请求
     *
     * @param url    请求地址
     * @param params 请求参数（拼接在 URL 后面）
     * @return 响应内容
     */
    public static String get(String url, Map<String, String> params) throws Exception {
        // 拼接 URL 参数
        String fullUrl = buildUrlWithParams(url, params);
        HttpURLConnection connection = (HttpURLConnection) new URL(fullUrl).openConnection();
        connection.setRequestMethod("GET");
        connection.setRequestProperty("Accept", "application/json");
        // 获取响应
        return getResponse(connection);
    }

    /**
     * 发送 POST 请求
     *
     * @param url    请求地址
     * @param params 请求参数（拼接在 URL 后面）
     * @param body   请求体（JSON 或其他格式）
     * @return 响应内容
     */
    public static String post(String url, Map<String, String> params, String body) throws Exception {
        // 拼接 URL 参数
        String fullUrl = buildUrlWithParams(url, params);
        HttpURLConnection connection = (HttpURLConnection) new URL(fullUrl).openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("Accept", "application/json");
        connection.setDoOutput(true);
        // 写入请求体
        if (body == null) {
            body = "{}";
        }
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = body.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }

        // 获取响应
        return getResponse(connection);
    }

    /**
     * 构建带参数的 URL
     *
     * @param url    基础 URL
     * @param params 参数 Map
     * @return 带参数的完整 URL
     */
    private static String buildUrlWithParams(String url, Map<String, String> params) throws Exception {
        if (params == null || params.isEmpty()) {
            return url;
        }

        StringBuilder fullUrl = new StringBuilder(url);
        fullUrl.append("?");
        for (Map.Entry<String, String> entry : params.entrySet()) {
            fullUrl.append(URLEncoder.encode(entry.getKey(), "UTF-8"))
                    .append("=")
                    .append(URLEncoder.encode(entry.getValue(), "UTF-8"))
                    .append("&");
        }
        // 删除最后一个多余的 "&"
        fullUrl.deleteCharAt(fullUrl.length() - 1);
        return fullUrl.toString();
    }

    /**
     * 获取响应内容
     *
     * @param connection HttpURLConnection 对象
     * @return 响应内容
     */
    private static String getResponse(HttpURLConnection connection) throws Exception {
        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                StringBuilder response = new StringBuilder();
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
                return response.toString();
            }
        } else {
            throw new RuntimeException("HTTP 请求失败，响应码: " + responseCode);
        }
    }
}