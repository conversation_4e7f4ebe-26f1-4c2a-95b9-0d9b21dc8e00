package com.tencent.wxcloudrun.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;

public class MD5PasswordExample {

    /**
     * 使用MD5加密密码
     * @param password 原始密码
     * @return 加密后的十六进制字符串
     */
    public static String encryptMD5(String password) {
        try {
            // 创建MD5摘要器
            MessageDigest md = MessageDigest.getInstance("MD5");

            // 计算哈希值
            byte[] hashBytes = md.digest(password.getBytes());

            // 将字节数组转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5算法不可用", e);
        }
    }

    /**
     * 验证密码是否正确
     * @param inputPassword 用户输入的密码
     * @param storedPassword 存储的加密密码
     * @return 是否匹配
     */
    public static boolean verifyPassword(String inputPassword, String storedPassword) {
        // 对输入密码进行MD5加密
        String encryptedInput = encryptMD5(inputPassword);

        // 比较加密后的密码与存储的密码是否一致
        return encryptedInput.equals(storedPassword);
    }

    public static void main(String[] args) {
        // 示例用法
        String originalPassword = "123456";

        System.out.println("原始密码: " + originalPassword);

        // 加密密码
        String encryptedPassword = encryptMD5(originalPassword);
        System.out.println("加密后的密码: " + encryptedPassword);

        // 验证密码
        boolean isValid1 = verifyPassword("123456", encryptedPassword);
        System.out.println("验证正确密码: " + isValid1); // 应该返回true

        boolean isValid2 = verifyPassword("wrongPassword", encryptedPassword);
        System.out.println("验证错误密码: " + isValid2); // 应该返回false
    }
}