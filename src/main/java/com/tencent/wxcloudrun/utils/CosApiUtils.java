package com.tencent.wxcloudrun.utils;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.model.*;
import com.qcloud.cos.region.Region;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.*;
import java.net.URL;
import java.util.Date;
import java.util.UUID;

/**
 * 腾讯云COS工具类
 * 提供文件上传、下载、删除、生成URL等功能
 */
@Slf4j
@Component
public class CosApiUtils {

    @Value("${TENCENT.SECRET_ID}")
    private String secretId;

    @Value("${TENCENT.SECRET_KEY}")
    private String secretKey;

    @Value("${TENCENT.COS.REGION}")
    private String region;

    @Value("${TENCENT.COS.BUCKET_NAME}")
    private String bucketName;

    private COSClient cosClient;

    /**
     * 初始化COS客户端
     */
    @PostConstruct
    public void initCosClient() {
        try {
            // 1 初始化用户身份信息（secretId, secretKey）
            COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);

            // 2 设置 bucket 的地域
            Region regionObj = new Region(region);
            ClientConfig clientConfig = new ClientConfig(regionObj);

            // 3 生成 cos 客户端
            this.cosClient = new COSClient(cred, clientConfig);

            log.info("COS客户端初始化成功，region: {}, bucket: {}", region, bucketName);
        } catch (Exception e) {
            log.error("COS客户端初始化失败", e);
            throw new RuntimeException("COS客户端初始化失败", e);
        }
    }

    /**
     * 上传文件
     *
     * @param file     要上传的文件
     * @param fileName 文件名（可选，为空时自动生成）
     * @return 文件的访问URL
     */
    public String uploadFile(MultipartFile file, String fileName) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("上传文件不能为空");
        }

        try {
            // 生成文件名
            if (fileName == null || fileName.trim().isEmpty()) {
                fileName = generateFileName(file.getOriginalFilename());
            }

            // 创建上传请求
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentType(file.getContentType());

            PutObjectRequest putObjectRequest = new PutObjectRequest(
                    bucketName, fileName, file.getInputStream(), metadata);

            // 执行上传
            PutObjectResult result = cosClient.putObject(putObjectRequest);
            log.info("文件上传成功，fileName: {}, etag: {}", fileName, result.getETag());

            // 返回文件访问URL
            return getFileUrl(fileName);

        } catch (IOException e) {
            log.error("文件上传失败，fileName: {}", fileName, e);
            throw new RuntimeException("文件上传失败", e);
        } catch (CosServiceException e) {
            log.error("COS服务异常，fileName: {}, errorCode: {}, errorMessage: {}",
                    fileName, e.getErrorCode(), e.getErrorMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getErrorMessage(), e);
        } catch (CosClientException e) {
            log.error("COS客户端异常，fileName: {}", fileName, e);
            throw new RuntimeException("文件上传失败", e);
        }
    }

    /**
     * 上传字节数组
     *
     * @param bytes    字节数组
     * @param fileName 文件名
     * @return 文件的访问URL
     */
    public String uploadFile(byte[] bytes, String fileName) {
        if (bytes == null || bytes.length == 0) {
            throw new IllegalArgumentException("上传数据不能为空");
        }

        try {
            // 创建上传请求
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(bytes.length);

            ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                    bucketName, fileName, inputStream, metadata);

            // 执行上传
            PutObjectResult result = cosClient.putObject(putObjectRequest);
            log.info("字节数组上传成功，fileName: {}, etag: {}", fileName, result.getETag());

            return getFileUrl(fileName);

        } catch (CosServiceException e) {
            log.error("COS服务异常，fileName: {}, errorCode: {}, errorMessage: {}",
                    fileName, e.getErrorCode(), e.getErrorMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getErrorMessage(), e);
        } catch (CosClientException e) {
            log.error("COS客户端异常，fileName: {}", fileName, e);
            throw new RuntimeException("文件上传失败", e);
        }
    }

    /**
     * 下载文件到本地
     *
     * @param fileName   COS中的文件名
     * @param localPath  本地保存路径
     * @return 是否下载成功
     */
    public boolean downloadFile(String fileName, String localPath) {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        try {
            GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, fileName);
            ObjectMetadata objectMetadata = cosClient.getObject(getObjectRequest, new File(localPath));

            log.info("文件下载成功，fileName: {}, localPath: {}", fileName, localPath);
            return true;

        } catch (CosServiceException e) {
            log.error("COS服务异常，fileName: {}, errorCode: {}, errorMessage: {}",
                    fileName, e.getErrorCode(), e.getErrorMessage(), e);
            return false;
        } catch (CosClientException e) {
            log.error("COS客户端异常，fileName: {}", fileName, e);
            return false;
        }
    }

    /**
     * 下载文件为字节数组
     *
     * @param fileName COS中的文件名
     * @return 文件字节数组
     */
    public byte[] downloadFileAsBytes(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        try {
            GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, fileName);
            COSObject cosObject = cosClient.getObject(getObjectRequest);

            try (InputStream inputStream = cosObject.getObjectContent();
                 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

                byte[] buffer = new byte[1024];
                int length;
                while ((length = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, length);
                }

                log.info("文件下载成功，fileName: {}, size: {} bytes", fileName, outputStream.size());
                return outputStream.toByteArray();

            } finally {
                cosObject.close();
            }

        } catch (IOException e) {
            log.error("文件读取异常，fileName: {}", fileName, e);
            throw new RuntimeException("文件下载失败", e);
        } catch (CosServiceException e) {
            log.error("COS服务异常，fileName: {}, errorCode: {}, errorMessage: {}",
                    fileName, e.getErrorCode(), e.getErrorMessage(), e);
            throw new RuntimeException("文件下载失败: " + e.getErrorMessage(), e);
        } catch (CosClientException e) {
            log.error("COS客户端异常，fileName: {}", fileName, e);
            throw new RuntimeException("文件下载失败", e);
        }
    }

    /**
     * 删除文件
     *
     * @param fileName 要删除的文件名
     * @return 是否删除成功
     */
    public boolean deleteFile(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        try {
            cosClient.deleteObject(bucketName, fileName);
            log.info("文件删除成功，fileName: {}", fileName);
            return true;

        } catch (CosServiceException e) {
            log.error("COS服务异常，fileName: {}, errorCode: {}, errorMessage: {}",
                    fileName, e.getErrorCode(), e.getErrorMessage(), e);
            return false;
        } catch (CosClientException e) {
            log.error("COS客户端异常，fileName: {}", fileName, e);
            return false;
        }
    }

    /**
     * 生成预签名URL（用于临时访问）
     *
     * @param fileName       文件名
     * @param expirationTime 过期时间（毫秒）
     * @return 预签名URL
     */
    public String generatePresignedUrl(String fileName, long expirationTime) {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        try {
            Date expiration = new Date(System.currentTimeMillis() + expirationTime);
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(
                    bucketName, fileName, HttpMethodName.GET);
            request.setExpiration(expiration);

            URL url = cosClient.generatePresignedUrl(request);
            log.info("预签名URL生成成功，fileName: {}, expiration: {}", fileName, expiration);
            return url.toString();

        } catch (CosServiceException e) {
            log.error("COS服务异常，fileName: {}, errorCode: {}, errorMessage: {}",
                    fileName, e.getErrorCode(), e.getErrorMessage(), e);
            throw new RuntimeException("URL生成失败: " + e.getErrorMessage(), e);
        } catch (CosClientException e) {
            log.error("COS客户端异常，fileName: {}", fileName, e);
            throw new RuntimeException("URL生成失败", e);
        }
    }

    /**
     * 生成永久访问URL
     *
     * @param fileName 文件名
     * @return 文件访问URL
     */
    public String getFileUrl(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        // 构建文件访问URL
        return String.format("https://%s.cos.%s.myqcloud.com/%s", bucketName, region, fileName);
    }

    /**
     * 检查文件是否存在
     *
     * @param fileName 文件名
     * @return 文件是否存在
     */
    public boolean doesFileExist(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }

        try {
            return cosClient.doesObjectExist(bucketName, fileName);
        } catch (CosServiceException e) {
            log.error("COS服务异常，fileName: {}, errorCode: {}, errorMessage: {}",
                    fileName, e.getErrorCode(), e.getErrorMessage(), e);
            return false;
        } catch (CosClientException e) {
            log.error("COS客户端异常，fileName: {}", fileName, e);
            return false;
        }
    }

    /**
     * 获取文件信息
     *
     * @param fileName 文件名
     * @return 文件元数据信息
     */
    public ObjectMetadata getFileMetadata(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        try {
            ObjectMetadata metadata = cosClient.getObjectMetadata(bucketName, fileName);
            log.info("获取文件信息成功，fileName: {}, size: {}, lastModified: {}",
                    fileName, metadata.getContentLength(), metadata.getLastModified());
            return metadata;

        } catch (CosServiceException e) {
            log.error("COS服务异常，fileName: {}, errorCode: {}, errorMessage: {}",
                    fileName, e.getErrorCode(), e.getErrorMessage(), e);
            throw new RuntimeException("获取文件信息失败: " + e.getErrorMessage(), e);
        } catch (CosClientException e) {
            log.error("COS客户端异常，fileName: {}", fileName, e);
            throw new RuntimeException("获取文件信息失败", e);
        }
    }

    /**
     * 批量删除文件
     *
     * @param fileNames 要删除的文件名列表
     * @return 删除结果
     */
    public DeleteObjectsResult batchDeleteFiles(String[] fileNames) {
        if (fileNames == null || fileNames.length == 0) {
            throw new IllegalArgumentException("文件名列表不能为空");
        }

        try {
            DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(bucketName);
            for (String fileName : fileNames) {
                deleteObjectsRequest.addKey(fileName);
            }

            DeleteObjectsResult result = cosClient.deleteObjects(deleteObjectsRequest);
            log.info("批量删除文件成功，删除数量: {}", result.getDeletedObjects().size());
            return result;

        } catch (CosServiceException e) {
            log.error("COS服务异常，errorCode: {}, errorMessage: {}",
                    e.getErrorCode(), e.getErrorMessage(), e);
            throw new RuntimeException("批量删除文件失败: " + e.getErrorMessage(), e);
        } catch (CosClientException e) {
            log.error("COS客户端异常", e);
            throw new RuntimeException("批量删除文件失败", e);
        }
    }

    /**
     * 生成唯一文件名
     *
     * @param originalFileName 原始文件名
     * @return 生成的唯一文件名
     */
    private String generateFileName(String originalFileName) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String extension = "";

        if (originalFileName != null && originalFileName.contains(".")) {
            extension = originalFileName.substring(originalFileName.lastIndexOf("."));
        }

        return uuid + extension;
    }

    /**
     * 根据文件类型生成带路径的文件名
     *
     * @param originalFileName 原始文件名
     * @param folder          文件夹路径（可选）
     * @return 生成的文件名
     */
    public String generateFileNameWithPath(String originalFileName, String folder) {
        String fileName = generateFileName(originalFileName);

        if (folder != null && !folder.trim().isEmpty()) {
            // 确保文件夹路径以/结尾
            if (!folder.endsWith("/")) {
                folder += "/";
            }
            return folder + fileName;
        }

        return fileName;
    }

    /**
     * 关闭COS客户端
     */
    public void shutdown() {
        if (cosClient != null) {
            cosClient.shutdown();
            log.info("COS客户端已关闭");
        }
    }

    // Getter方法，供外部获取配置信息
    public String getBucketName() {
        return bucketName;
    }

    public String getRegion() {
        return region;
    }
}