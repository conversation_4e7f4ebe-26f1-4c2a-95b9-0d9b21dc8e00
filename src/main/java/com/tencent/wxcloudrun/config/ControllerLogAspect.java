package com.tencent.wxcloudrun.config;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.tencent.wxcloudrun.model.WxUsers;
import com.tencent.wxcloudrun.service.api.IWxUsersService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
@Aspect
@Slf4j
@Order(-98)
public class ControllerLogAspect {
    private static final Pattern NON_DIGIT_PATTERN = Pattern.compile("\\D+");

    @Pointcut("execution(public * com.tencent.wxcloudrun.controller..*.*(..))")
    public void pointcut() {
    }

    @Autowired
    private IWxUsersService wxUsersService;

    @Around("pointcut()")
    public Object handle(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            String requestUri = request.getRequestURI();
            Signature signature = joinPoint.getSignature();

            //方法路径
            String methodName = signature.getDeclaringType().getSimpleName() + "." + signature.getName();
            //请求入参
            // 请求入参
            List<Object> filteredParams = Arrays.stream(joinPoint.getArgs())
                    .filter(param -> !(param instanceof HttpServletRequest)
                            && !(param instanceof HttpServletResponse)
                            && !(param instanceof MultipartFile)
                            && !(param instanceof MultipartFile[])
                    ).collect(Collectors.toList());
            String requestParam = JSONUtil.toJsonStr(filteredParams);
            // 检查请求参数是否包含userId
            Object[] args = joinPoint.getArgs();
            Object[] modifiedArgs = replaceUserIdInArgs(args);
            long begin = System.currentTimeMillis();
            log.info("[web.request], requestURI: [{}], methodName: [{}], requestParam: [{}]",
                    requestUri, methodName, requestParam);

            // 使用修改后的参数继续执行
            Object result = joinPoint.proceed(modifiedArgs);
            String response = JSONUtil.toJsonStr(result);
            long rt = System.currentTimeMillis() - begin;
            log.info("[web.response], requestURI: [{}], methodName: [{}], 耗时: {}ms, response: [{}]",
                    requestUri, methodName, rt, response);
            return result;
        } catch (Exception e) {
            log.error("failed to process", e);
            return joinPoint.proceed();
        }
    }

    /**
     * 检查并替换参数中的 userId 为 111111
     */
    private Object[] replaceUserIdInArgs(Object[] args) {
        Object[] modifiedArgs = Arrays.copyOf(args, args.length);
        for (int i = 0; i < modifiedArgs.length; i++) {
            Object arg = modifiedArgs[i];
            if (arg == null || arg instanceof HttpServletRequest || arg instanceof HttpServletResponse
                    || arg instanceof MultipartFile || arg instanceof MultipartFile[]) {
                continue; // 跳过不需要处理的参数
            }
            try {
                // 如果参数是 Map 类型
                if (arg instanceof Map) {
                    Map<?, ?> map = (Map<?, ?>) arg;
                    if (map.containsKey("userId")) {
                        String userId = map.get("userId").toString();
                        userId = replaceUserId(userId);
                        ((Map<String, Object>) map).put("userId",userId);
                    }
                }
                else if (arg instanceof String) {
                    // 使用时
                    String strArg = (String) arg;
                    if (NON_DIGIT_PATTERN.matcher(strArg).find()) {
                        String userId = replaceUserId(strArg);
                        modifiedArgs[i] = userId;
                    }
                    log.warn("replaceUserIdInArgs String  userId in arg: {}", arg);
                }
                // 如果参数是普通 Java 对象
                else {
                    Class<?> clazz = arg.getClass();
                    // 检查是否有 userId 字段
                    try {
                        Field userIdField = clazz.getDeclaredField("userId");
                        userIdField.setAccessible(true);
                        Object currentUserId = userIdField.get(arg);
                        if (currentUserId != null) {
                            String userId = currentUserId.toString();
                            userId = replaceUserId(userId);
                            userIdField.set(arg, userId);
                        }
                    } catch (NoSuchFieldException e) {
                        // 如果没有 userId 字段，尝试调用 getUserId/setUserId 方法
                        try {
                            Method getUserIdMethod = clazz.getMethod("getUserId");
                            Object currentUserId = getUserIdMethod.invoke(arg);
                            if (currentUserId != null) {
                                String userId = currentUserId.toString();
                                userId = replaceUserId(userId);
                                Method setUserIdMethod = clazz.getMethod("setUserId", String.class);
                                setUserIdMethod.invoke(arg, userId);
                            }
                        } catch (NoSuchMethodException ex) {
                            // 既没有字段也没有 getter/setter 方法，跳过
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("Failed to replace userId in arg: {}", arg, e);
            }
        }
        return modifiedArgs;
    }

    private String replaceUserId(String openId) {
        if (StrUtil.isEmpty(openId)) {
            return openId;
        }
        WxUsers byOpenId = wxUsersService.getByOpenId(openId);
        if (byOpenId!=null) {
            UserContextHolder.UserLog userLog = new UserContextHolder.UserLog();
            userLog.setUserId(byOpenId.getOpenid());
            userLog.setNickName(byOpenId.getNickname());
            UserContextHolder.setCurrentUser(userLog);
            log.info("userLog = {}", JSONUtil.toJsonStr(userLog));
        }

        if(!ObjectUtil.isEmpty(byOpenId) && byOpenId.getMobile() != null && byOpenId.getMobile().equals("15811111111") ) {
            return "15811111111";
        }
       return openId;
    }
}
