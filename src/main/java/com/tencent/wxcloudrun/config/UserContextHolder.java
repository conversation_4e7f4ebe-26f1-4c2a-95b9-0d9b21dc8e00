package com.tencent.wxcloudrun.config;

import lombok.Data;

public class UserContextHolder {

    private static final ThreadLocal<UserLog> currentUser = new ThreadLocal<>();

    // 禁止实例化
    private UserContextHolder() {}

    public static void setCurrentUser(UserLog user) {
        currentUser.set(user);
    }

    public static String getCurrentUserLog() {
        UserLog userLog = currentUser.get();
        if (userLog == null) {
            return "用户不存在";
        }
        return "用户昵称:" + userLog.getNickName() + ", 用户id:" + userLog.getUserId();
    }

    public static UserLog getCurrentUser() {
        return currentUser.get();
    }

    public static void clear() {
        currentUser.remove();
    }

    @Data
    public static class UserLog {
        private String userId;
        private String nickName;
    }
}
