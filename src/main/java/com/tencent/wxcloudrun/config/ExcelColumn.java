package com.tencent.wxcloudrun.config;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ExcelColumn {
    /** 列标题（默认使用字段名） */
    String name() default "";

    /** 列顺序（从0开始） */
    int order() default 999;

    /** 日期格式（默认：yyyy-MM-dd HH:mm:ss） */
    String dateFormat() default "yyyy-MM-dd HH:mm:ss";

    /** 是否是时间戳（支持Long/Integer类型转换） */
    boolean isTimestamp() default false;

    /** 列宽（单位：字符数，默认自动调整） */
    int width() default -1;

    /** 是否忽略该字段 */
    boolean ignore() default false;

}