package com.tencent.wxcloudrun.config;

import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class GlobalExceptionHandler {

    // 处理业务异常
    @ExceptionHandler(BusinessException.class)
    public ApiResponse<String> handleBusinessException(BusinessException e) {
        return ApiResponse.error(e.getErrorCode(), e.getErrorMsg());
    }

    // 处理空指针异常
    @ExceptionHandler(NullPointerException.class)
    public ApiResponse<String> handleNullPointerException(NullPointerException e) {
        return ApiResponse.error(500, "空指针异常: " + e.getMessage());
    }

    // 处理其他异常
    @ExceptionHandler(Exception.class)
    public ApiResponse<String> handleException(Exception e) {
        return ApiResponse.error(500, "系统内部错误: " + e.getMessage());
    }
}