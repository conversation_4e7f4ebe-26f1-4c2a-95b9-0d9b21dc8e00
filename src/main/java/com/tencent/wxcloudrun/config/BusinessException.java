package com.tencent.wxcloudrun.config;

import com.tencent.wxcloudrun.enums.ResponseEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
@EqualsAndHashCode(callSuper = true)
@Data
public class BusinessException extends RuntimeException {
    private final int errorCode;
    private final String errorMsg;

    public BusinessException(int errorCode, String errorMsg) {
        super(errorMsg);
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }
    public BusinessException(ResponseEnum responseEnum) {
        super(responseEnum.getMessage());
        this.errorCode = responseEnum.getCode();
        this.errorMsg =  responseEnum.getMessage();

    }
}