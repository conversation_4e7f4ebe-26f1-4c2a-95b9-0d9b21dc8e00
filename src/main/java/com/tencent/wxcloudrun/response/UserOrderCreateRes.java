package com.tencent.wxcloudrun.response;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserOrderCreateRes {

    private String userOpenId;
    private String prepayId;
    private String orderNo;

    private String appId;
    private String timestamp;
    private String nonceStr;
    private String packageVal;
    private String signType;
    private String paySign;
}
