package com.tencent.wxcloudrun.response;

import com.tencent.wxcloudrun.dto.UserOrdersDTO;
import com.tencent.wxcloudrun.model.ServiceContent;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserOrderDetailRes {
    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("服务地址")
    private String serviceAddress;

    @ApiModelProperty("服务内容")
    private String serviceContent;

    @ApiModelProperty("服务报告")
    private List<ServiceContent> serviceReport;

    @ApiModelProperty("服务时间 格式 2022-01-01")
    private List<String> serviceTimes;

    @ApiModelProperty("宠物头像url")
    private String petUrl;

    @ApiModelProperty("宠物名称")
    private String petName;

    /**
     * 服务类型
     */
    @ApiModelProperty("服务类型")
    private Integer serviceType;

    @ApiModelProperty("交接方式")
    private UserOrdersDTO.Handover handover;

    @ApiModelProperty("订单备注")
    private String remarks;

    @ApiModelProperty("下单时间")
    private Long createTime;

    @ApiModelProperty("订单总价")
    private Long totalPrice;

    @ApiModelProperty("支付金额")
    private Long payPrice;

    @ApiModelProperty("优惠金额")
    private Long discountPrice;

    @ApiModelProperty("服务人员id")
    private String serviceUserId = "";

    @ApiModelProperty("服务人员名称")
    private String serviceUserName = "";

    @ApiModelProperty("服务人员头像")
    private String serviceCustomUrl = "";

    @ApiModelProperty("服务人员评分")
    private long serviceUserScore = 46;

    @ApiModelProperty("服务次数")
    private int serviceCount = 10;
    @ApiModelProperty("订单状态")
    private int status;

}
