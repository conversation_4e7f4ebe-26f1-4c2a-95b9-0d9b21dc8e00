package com.tencent.wxcloudrun.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.tencent.wxcloudrun.model.ServiceContent;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceOrderRes {

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("服务地址")
    private String serviceAddress;

    @ApiModelProperty("服务内容")
    private String serviceContent;

    @ApiModelProperty("服务类型")
    private int serviceType;

    @ApiModelProperty("服务时间 格式 2022-01-01")
    private List<String> serviceTimes;

    /**
     * 预估收入
     */
    @ApiModelProperty("收入")
    private long servicePrice;

    @ApiModelProperty("奖励收入")
    private long rewardPrice;

    @ApiModelProperty("宠物头像url")
    private String petUrl;

    @ApiModelProperty("订单状态")
    private int status;

    @ApiModelProperty("喂养员结算时间")
    private Long accountedTime;

    @ApiModelProperty("喂养员对用户的评价分数")
    private Integer userScore;

    @ApiModelProperty("用户对喂养员的评价分数")
    private Integer serviceUserScore;

    @ApiModelProperty("服务报告")
    private List<ServiceContent> serviceReport;
}
