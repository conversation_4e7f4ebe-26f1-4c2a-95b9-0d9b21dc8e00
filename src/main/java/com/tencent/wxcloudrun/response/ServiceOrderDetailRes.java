package com.tencent.wxcloudrun.response;


import com.tencent.wxcloudrun.dto.UserOrdersDTO;
import com.tencent.wxcloudrun.model.Pets;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceOrderDetailRes {

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("服务地址")
    private String serviceAddress;

    @ApiModelProperty("服务内容")
    private String serviceContent;

    @ApiModelProperty("服务时间 格式 2022-01-01")
    private List<String> serviceTimes;

    @ApiModelProperty("预估收入")
    private long servicePrice;

    @ApiModelProperty("奖励收入")
    private long rewardPrice;

    @ApiModelProperty("宠物头像url")
    private String petUrl;

    @ApiModelProperty("宠物名称")
    private String petName;
    @ApiModelProperty("宠物名称")
    private Pets pets;

    /**
     * 服务类型
     */
    @ApiModelProperty("服务类型")
    private Integer serviceType;

    @ApiModelProperty("交接方式")
    private UserOrdersDTO.Handover handover;

    @ApiModelProperty("订单备注")
    private String remarks;

    @ApiModelProperty("下单时间")
    private Long createTime;

}
