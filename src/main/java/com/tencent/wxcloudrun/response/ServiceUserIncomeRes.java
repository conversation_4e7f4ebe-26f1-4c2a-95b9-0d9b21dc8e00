package com.tencent.wxcloudrun.response;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceUserIncomeRes {


    private String userId;
    @ApiModelProperty("总收入")
    private Long totalAmount;

    @ApiModelProperty("已提现金额")
    private Long withdrawnAmount;

    @ApiModelProperty("可提现金额")
    private Long releaseAmount;

    @ApiModelProperty("冻结金额")
    private Long freezeAmount;

}
