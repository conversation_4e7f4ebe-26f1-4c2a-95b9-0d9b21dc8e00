package com.tencent.wxcloudrun.response;

import com.tencent.wxcloudrun.model.Pets;
import com.tencent.wxcloudrun.model.ServiceContent;
import com.tencent.wxcloudrun.model.WorkerCertification;
import com.tencent.wxcloudrun.model.WxUsers;
import com.tencent.wxcloudrun.service.impl.UserOrderServiceImpl;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdminServiceUserRes extends WxUsers {

    @ApiModelProperty("用户服务类型")
    private List<Integer> userServiceType;



}
