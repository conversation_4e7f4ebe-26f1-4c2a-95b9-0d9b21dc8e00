package com.tencent.wxcloudrun.response;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import java.io.IOException;
import java.util.List;

/**
 * 腾讯位置服务JSON解析器，负责解析API响应
 */

public class TencentLocationParser {
    private final ObjectMapper objectMapper = new ObjectMapper();

    public ReverseGeocodeResult parseReverseGeocodeResponse(String json) throws IOException {
        return objectMapper.readValue(json, ReverseGeocodeResult.class);
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ReverseGeocodeResult {
        private int status;
        private String message;
        private ResultData result;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ResultData {
        private String address;
        private Location location;

        @JsonProperty("formatted_addresses")
        private FormattedAddresses formattedAddresses;

        @JsonProperty("address_component")
        private AddressComponent addressComponent;

        @JsonProperty("ad_info")
        private AdInfo adInfo;

        @JsonProperty("address_reference")
        private AddressReference addressReference;

        @JsonProperty("poi_count")
        private Integer poiCount;
        @JsonProperty("pois")
        private List<Poi> pois;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FormattedAddresses {
        private String recommend;
        private String rough;

        @JsonProperty("standard_address")
        private String standardAddress;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AddressComponent {
        private String nation;
        private String province;
        private String city;
        private String district;
        private String street;

        @JsonProperty("street_number")
        private String streetNumber;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AdInfo {
        @JsonProperty("nation_code")
        private String nationCode;

        private String adcode;

        @JsonProperty("phone_area_code")
        private String phoneAreaCode;

        @JsonProperty("city_code")
        private String cityCode;

        private String name;
        private Location location;
        private String nation;
        private String province;
        private String city;
        private String district;

        @JsonProperty("_distance")
        private Integer distance;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Location {
        private double lat;
        private double lng;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AddressReference {
        @JsonProperty("famous_area")
        private ReferenceItem famousArea;

        @JsonProperty("business_area")
        private ReferenceItem businessArea;

        private ReferenceItem town;

        @JsonProperty("landmark_l1")
        private ReferenceItem landmarkL1;

        @JsonProperty("landmark_l2")
        private ReferenceItem landmarkL2;

        private ReferenceItem street;

        @JsonProperty("street_number")
        private ReferenceItem streetNumber;

        private ReferenceItem crossroad;
        private ReferenceItem water;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ReferenceItem {
        private String id;
        private String title;
        private Location location;

        @JsonProperty("_distance")
        private Integer distance;

        @JsonProperty("_dir_desc")
        private String dirDesc;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Poi {
        private String id;
        private String title;
        private String address;
        private String category;
        private Location location;
        private AdInfo adInfo;

        @JsonProperty("_distance")
        private Integer distance;

        @JsonProperty("_dir_desc")
        private String dirDesc;
    }
}