package com.tencent.wxcloudrun.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.tencent.wxcloudrun.model.Pets;
import com.tencent.wxcloudrun.model.ServiceContent;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserOrderRes {

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("服务地址")
    private String serviceAddress;

    @ApiModelProperty("服务类型")
    private int serviceType;

    @ApiModelProperty("服务报告")
    private List<ServiceContent> serviceReport;

    @ApiModelProperty("服务时间 格式 2022-01-01")
    private List<String> serviceTimes;

    @ApiModelProperty("实付款")
    private long payAmount;
    /**
     * 喂养员到手价格
     */
    @TableField("service_price")
    @ApiModelProperty("喂养员价格")
    private Long servicePrice;

    /**
     * 分销到手价格
     */
    @TableField("distributor_price")
    @ApiModelProperty("推广员价格")
    private Long distributorPrice;

    @ApiModelProperty("用户手机号")
    private String userMobile;

    @ApiModelProperty("服务人员手机号")
    private String serviceMobile;

    @ApiModelProperty("宠物对象")
    private Pets pets;

    @ApiModelProperty("宠物名称")
    private String petUrl;

    @ApiModelProperty("宠物名称")
    private String petName;

    @ApiModelProperty("服务人Id")
    private String serviceUserId;

    @ApiModelProperty("服务人名称")
    private String serviceUserName;
    @ApiModelProperty("下单用户昵称")
    private String userName;

    @ApiModelProperty("下单用户openId")
    private String userId;

    @ApiModelProperty("订单状态")
    private int status;

    @ApiModelProperty("喂养员对用户的评价")
    private Integer userScore;

    @ApiModelProperty("用户对喂养员的评价分数")
    private Integer serviceUserScore;

    @ApiModelProperty("提现状态 1 冻结状态， 2 提现完成")
    private Integer withdrawalStatus;
}
