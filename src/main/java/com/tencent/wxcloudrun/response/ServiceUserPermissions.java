package com.tencent.wxcloudrun.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceUserPermissions {

    private String serviceUserId;

    @ApiModelProperty("身份证是否认证")
    private boolean certified= false;

    @ApiModelProperty("是否考试 没有就是没考试， 有就是已考试 服务类型--主要是有没有考试")
    private List<Integer> serviceType = new ArrayList<>();

    private boolean sheetType = false;
}
