package com.tencent.wxcloudrun.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchServiceUser {

    @ApiModelProperty("喂养员id")
    private String serviceUserId;

    private String serviceUserName;

    @ApiModelProperty("喂养员头像")
    private String serviceUserUrl;

    @ApiModelProperty("喂养员评分")
    private Long serviceUserScore;

    @ApiModelProperty("喂养员服务次数")
    private long serviceUserCount;

    @ApiModelProperty("喂养员服务距离")
    private String serviceUserDistance;

    /**
     * 纬度（WGS84坐标系）
     */
    @ApiModelProperty(value = "纬度（WGS84坐标系）", example = "39.9042")
    private BigDecimal latitude;

    /**
     * 经度（WGS84坐标系）
     */
    @ApiModelProperty(value = "经度（WGS84坐标系）", example = "116.4074")
    private BigDecimal longitude;

}
