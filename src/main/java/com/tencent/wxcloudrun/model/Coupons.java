package com.tencent.wxcloudrun.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <p>
 * 优惠券表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("pet_technology_coupons")
@ApiModel(value = "Coupons对象", description = "优惠券表")
public class Coupons implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 优惠类型
     */
    @ApiModelProperty("优惠类型 1 满减 2 打折")
    @TableField("coupon_type")
    private int couponType;

    /**
     * 优惠券名称
     */
    @TableField("title")
    @ApiModelProperty("优惠券名称")
    private String title;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    @TableField("coupons_num")
    private Integer couponsNum;

    /**
     * 生效时间
     */
    @ApiModelProperty("生效时间")
    @TableField("valid_start")
    private Long validStart;

    /**
     * 过期时间
     */
    @TableField("valid_end")
    @ApiModelProperty("过期时间")
    private Long validEnd;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private Long updateTime;

    @ApiModelProperty("是否删除 0正常,1删除")
    @TableField("is_delete")
    private int isDelete;

    @ApiModelProperty("0 展示 1 不展示")
    @TableField("enable")
    private int enable;
    @ApiModelProperty("优惠券有效时间 默认30天")
    @TableField("effective_time")
    private int effectiveTime;

    @ApiModelProperty("优惠券有效时间 默认30天")
    @TableField("enable_time")
    private Long enableTime;
    @ApiModelProperty("1用户侧 2 喂养员侧")
    @TableField("user_type")
    private Long userType;

    private String ext;
}
