package com.tencent.wxcloudrun.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.tencent.wxcloudrun.config.ExcelColumn;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderServiceContent {

    @ExcelColumn(name = "服务报告ID", order = 1) // 不导出
    private Long id;

    @ExcelColumn(name = "日期", order = 2)
    private String date;

    @ExcelColumn(name = "喂养员ID", order = 3)
    private String workerId;

    @ExcelColumn(name = "订单编号", order = 1)
    private String orderNo;

    @ExcelColumn(name = "服务类型", order = 4)
    private Integer serviceType;

    @ExcelColumn(name = "服务状态", order = 5)
    private Integer serviceContentStatus = 1;

    @ExcelColumn(name = "服务状态说明", order = 6)
    private String serviceContentStatusValue;

    // 图片字段设置更宽的列宽格式
    @ExcelColumn(name = "宠物照片", order = 7, width = 40)
    private String petUrls;

    @ExcelColumn(name = "入门照片", order = 8, width = 40)
    private String doorUrls;

    @ExcelColumn(name = "房屋清洁照片", order = 9, width = 40)
    private String roomUrls;

    @ExcelColumn(name = "喂食照片", order = 10, width = 40)
    private String eatUrls;

    @ExcelColumn(name = "宠物精神情况", order = 11)
    private String petEmotionDesc;

    @ExcelColumn(name = "宠物健康状况", order = 12)
    private String petHealthDesc;

    @ExcelColumn(name = "宠物进食情况", order = 13)
    private String petEatDesc;

    @ExcelColumn(name = "备注", order = 14)
    private String remark;

    @ExcelColumn(name = "环境检查", order = 15)
    private String environment;

    @ExcelColumn(name = "安全检查", order = 16)
    private String security;

    @ExcelColumn(name = "陪玩情况", order = 17)
    private String partner;

    @ExcelColumn(
            name = "创建时间",
            order = 18,
            isTimestamp = true,
            dateFormat = "yyyy-MM-dd HH:mm:ss"
    )
    private long createTime;

    @ExcelColumn(
            name = "更新时间",
            order = 19,
            isTimestamp = true,
            dateFormat = "yyyy-MM-dd HH:mm:ss"
    )
    private long updateTime;

    @ExcelColumn(ignore = true) // 不导出
    private String ext;
}
