package com.tencent.wxcloudrun.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("pet_technology_service_type")
@ApiModel(value = "ServiceType对象", description = "")
public class ServiceType implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    /**
     * 服务名称
     */
    @ApiModelProperty("服务名称")
    @TableField("service_name")
    private String serviceName;

    /**
     * 排序
     */
    @TableField("order")
    @ApiModelProperty("排序")
    private Integer order;

    /**
     * 类型(猫 0, 狗 1)
     */
    @TableField("type")
    @ApiModelProperty("类型(猫 0, 狗 1)")
    private Integer type;
}
