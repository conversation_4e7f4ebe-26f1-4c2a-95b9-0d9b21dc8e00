package com.tencent.wxcloudrun.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
/**
 * <p>
 * 喂养员服务类型流程
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("pet_technology_caretakers_flow")
@ApiModel(value = "CaretakersFlow对象", description = "喂养员服务类型流程")
public class CaretakersFlow implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty("唯一标识")
    private String id;

    /**
     * 关联用户ID
     */
    @TableField("user_id")
    @ApiModelProperty("关联用户ID")
    private String userId;

    /**
     * 基础服务半径（单位：公里）
     */
    @TableField("service_radius")
    @ApiModelProperty("基础服务半径（单位：公里）")
    private Integer serviceRadius;

    /**
     * 基础服务价格
     */
    @TableField("base_price")
    @ApiModelProperty("基础服务价格")
    private String basePrice;

    /**
     * 增加价格，每一公里增加X元
     */
    @TableField("increase_price")
    @ApiModelProperty("增加价格，每一公里增加X元")
    private String increasePrice;

    /**
     * 服务类型
     */
    @TableField("type")
    @ApiModelProperty("服务类型")
    private String type;

    /**
     * 服务流程
     */
    @TableField("flow")
    @ApiModelProperty("服务流程")
    private String flow;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private String createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private String updateTime;

    /**
     * json格式数据
     */
    @TableField("ext")
    @ApiModelProperty("json格式数据")
    private String ext;
}
