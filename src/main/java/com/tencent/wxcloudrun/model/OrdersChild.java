package com.tencent.wxcloudrun.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
/**
 * <p>
 * 订单子单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("pet_technology_orders_child")
@ApiModel(value = "OrdersChild对象", description = "订单子单")
public class OrdersChild implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单唯一标识
     */
    @TableId(value = "id",type = IdType.AUTO)
    @ApiModelProperty("订单唯一标识")
    private String id;

    /**
     * 业务订单号（规则生成）
     */
    @TableField("order_no")
    @ApiModelProperty("业务订单号（规则生成）")
    private String orderNo;

    /**
     * 子订单号（规则生成）
     */
    @TableField("child_order_no")
    @ApiModelProperty("子订单号（规则生成）")
    private String childOrderNo;

    /**
     * 总微信支付单号
     */
    @ApiModelProperty("总微信支付单号")
    @TableField("wx_transaction_id")
    private String wxTransactionId;

    /**
     * 下单用户ID
     */
    @TableField("user_id")
    @ApiModelProperty("下单用户ID")
    private String userId;

    /**
     * 喂养员ID
     */
    @ApiModelProperty("喂养员ID")
    @TableField("caretakers_id")
    private String caretakersId;

    /**
     * 服务宠物ID,可能有多个
     */
    @TableField("pet_id")
    @ApiModelProperty("服务宠物ID,可能有多个")
    private String petId;

    /**
     * 服务地址ID
     */
    @TableField("address_id")
    @ApiModelProperty("服务地址ID")
    private String addressId;

    /**
     * 服务类型
     */
    @ApiModelProperty("服务类型")
    @TableField("service_type")
    private String serviceType;

    /**
     * 服务流程
     */
    @ApiModelProperty("服务流程")
    @TableField("service_flow")
    private String serviceFlow;

    /**
     * 子订单价格
     */
    @ApiModelProperty("子订单价格")
    @TableField("original_price")
    private String originalPrice;

    /**
     * 子订单支付价格
     */
    @TableField("final_price")
    @ApiModelProperty("子订单支付价格")
    private String finalPrice;

    /**
     *  待付款, 待接单, 待服务 ,进行中, 已完成 待评价 已取消， 申请退款，退款完成， 不够在加
     */
    @TableField("status")
    @ApiModelProperty(" 待付款, 待接单, 待服务 ,进行中, 已完成 待评价 已取消， 申请退款，退款完成， 不够在加")
    private Integer status;

    /**
     * 交接方式
     */
    @TableField("handover")
    @ApiModelProperty("交接方式")
    private String handover;

    /**
     * 订单备注
     */
    @TableField("remarks")
    @ApiModelProperty("订单备注")
    private String remarks;

    /**
     * 服务过程图片，多张图片，和文字描述
     */
    @TableField("caretakers_process")
    @ApiModelProperty("服务过程图片，多张图片，和文字描述")
    private String caretakersProcess;

    /**
     * 评价，用户和喂养员评价,文字描述
     */
    @TableField("evaluate_notes")
    @ApiModelProperty("评价，用户和喂养员评价,文字描述")
    private String evaluateNotes;

    /**
     * 支付完成时间
     */
    @TableField("pay_time")
    @ApiModelProperty("支付完成时间")
    private String payTime;

    /**
     * 退款时间
     */
    @ApiModelProperty("退款时间")
    @TableField("refund_time")
    private String refundTime;

    /**
     * 预约服务时间
     */
    @ApiModelProperty("预约服务时间")
    @TableField("service_time")
    private String serviceTime;

    /**
     * 服务实际开始时间
     */
    @ApiModelProperty("服务实际开始时间")
    @TableField("service_start_time")
    private String serviceStartTime;

    /**
     * 服务实际结束时间
     */
    @ApiModelProperty("服务实际结束时间")
    @TableField("service_end_time")
    private String serviceEndTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private String createTime;

    /**
     * json格式数据
     */
    @TableField("ext")
    @ApiModelProperty("json格式数据")
    private String ext;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private String updateTime;
}
