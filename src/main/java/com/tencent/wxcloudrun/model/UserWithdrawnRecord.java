package com.tencent.wxcloudrun.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Getter
@Setter
@ToString
@Accessors(chain = true)
@Builder
@TableName("pet_technology_user_withdrawn_record")
@ApiModel(value = "钱包提现记录表", description = "钱包提现记录表")
public class UserWithdrawnRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 宠物唯一标识
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty("钱包Id")
    private Long id;

    @TableField("user_id")
    @ApiModelProperty("用户openID")
    private String userId;


    @TableField("withdrawn_amount")
    @ApiModelProperty("提现金额")
    private long withdrawnAmount;

    @TableField("out_bill_no")
    @ApiModelProperty("提现订单编号")
    private String outBillNo;

    @TableField("transfer_bill_no")
    @ApiModelProperty("微信转账单号")
    private String transferBillNo;

    @TableField("create_time")
    @ApiModelProperty("创建时间")
    private long createTime;

    @TableField("update_time")
    @ApiModelProperty("更新时间")
    private long updateTime;

    @TableField("ext")
    private String ext;
}
