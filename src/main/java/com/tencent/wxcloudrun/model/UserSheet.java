package com.tencent.wxcloudrun.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 用户地理位置数据存储
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("pet_technology_user_sheet")
@ApiModel(value = "UserSheet对象", description = "用户考试试卷")
public class UserSheet implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 地址唯一标识
     */
    @TableId("id")
    @ApiModelProperty("地址唯一标识")
    private Long id;

    /**
     * 关联用户ID
     */
    @TableField("user_id")
    @ApiModelProperty("关联用户ID")
    private String userId;

    @TableField("score")
    @ApiModelProperty("得分")
    private int score;

    @TableField("status")
    @ApiModelProperty("状态 1-已提交 2-通过 3-不通过")
    private int status;

    @TableField("service_type")
    @ApiModelProperty("服务类型")
    private int serviceType;

    @TableField("sheet_id")
    @ApiModelProperty("试卷id")
    private long sheetId;

    @TableField("answer")
    @ApiModelProperty("答卷")
    private String answer;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private Long updateTime;

    @TableField("ext")
    private String ext;

}
