package com.tencent.wxcloudrun.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
/**
 * <p>
 * 用户优惠券管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-02
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("pet_technology_user_coupons")
@ApiModel(value = "UserCoupons对象", description = "用户优惠券管理表")
public class UserCoupons implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value = "id",type = IdType.AUTO)
    private String id;

    /**
     * 优惠券id
     */
    @TableField("coupons_id")
    @ApiModelProperty("优惠券id")
    private Long couponsId;

    /**
     * 所属用户ID
     */
    @TableField("user_id")
    @ApiModelProperty("所属用户ID")
    private String userId;

    /**
     * 生效时间
     */
    @ApiModelProperty("生效时间")
    @TableField("valid_start")
    private Long validStart;

    /**
     * 过期时间
     */
    @TableField("valid_end")
    @ApiModelProperty("过期时间")
    private Long validEnd;

    /**
     * 使用状态（0未使用 1已使用）
     */
    @TableField("is_used")
    @ApiModelProperty("使用状态（0未使用 1已使用）")
    private Integer isUsed;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private Long updateTime;
}
