package com.tencent.wxcloudrun.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("pet_technology_user_wallet")
@ApiModel(value = "钱包", description = "钱包")
public class UserWallet implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 宠物唯一标识
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty("钱包Id")
    private Long id;

    @TableField("user_id")
    @ApiModelProperty("用户openID")
    private String userId;

    @TableField("total_amount")
    @ApiModelProperty("总收入")
    private long totalAmount;

    @TableField("withdrawn_amount")
    @ApiModelProperty("已提现金额")
    private long withdrawnAmount;

    @TableField("release_amount")
    @ApiModelProperty("可提现金额")
    private long releaseAmount;

    @TableField("freeze_amount")
    @ApiModelProperty("冻结金额")
    private long freezeAmount;

    @TableField("create_time")
    @ApiModelProperty("创建时间")
    private long createTime;

    @TableField("update_time")
    @ApiModelProperty("更新时间")
    private long updateTime;

    @TableField("ext")
    private String ext;
}
