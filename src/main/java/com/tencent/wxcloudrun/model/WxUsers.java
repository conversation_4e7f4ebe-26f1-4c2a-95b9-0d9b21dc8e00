package com.tencent.wxcloudrun.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户基础信息与微信数据融合表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("pet_technology_wx_users")
@ApiModel(value = "WxUsers对象", description = "用户基础信息与微信数据融合表")
@Data
public class WxUsers implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * UUID主键（弱关联核心标识）
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty("UUID主键（弱关联核心标识）")
    private Long id;

    /**
     * 微信openid（加密存储）
     */
    @TableField("openid")
    @ApiModelProperty("微信openid（加密存储）")
    private String openid;
    @TableField("user_password")
    @ApiModelProperty("密码")
    private String userPassword;

    /**
     * 昵称
     */
    @ApiModelProperty("昵称")
    @TableField("nickname")
    private String nickname;

    /**
     * 真实姓名
     */
    @TableField("real_name")
    @ApiModelProperty("真实姓名")
    private String realName;

    /**
     * 身份证ID
     */
    @ApiModelProperty("身份证ID")
    @TableField("identity_id")
    private String identityId;

    /**
     * 身份证URL
     */
    @ApiModelProperty("身份证URL")
    @TableField("identity_url")
    private String identityUrl;

    /**
     * 性别
     */
    @TableField("gender")
    @ApiModelProperty("性别")
    private String gender;

    /**
     * 自定义头像URL
     */
    @TableField("custom_url")
    @ApiModelProperty("自定义头像URL")
    private String customUrl;

    /**
     * 绑定手机号
     */
    @TableField("mobile")
    @ApiModelProperty("绑定手机号")
    private String mobile;

    /**
     * 身份认证（0未认证 1已认证）
     */
    @TableField("is_certified")
    @ApiModelProperty("身份认证（0未认证 1已认证）")
    private Integer isCertified;

    /**
     * 喂养员资格
     */
    @TableField("service_type")
    @ApiModelProperty("List<Integer>")
    private int serviceType;

    /**
     * 个人介绍
     */
    @ApiModelProperty("个人介绍")
    @TableField("introduction")
    private String introduction;

    /**
     * 认证时间
     */
    @TableField("auth_time")
    @ApiModelProperty("认证时间")
    private String authTime;

    /**
     * 综合评价分（0.00-5.00）在完成订单后进行更新
     */
    @TableField("rating")
    @ApiModelProperty("综合评价分（0.00-5.00）在完成订单后进行更新")
    private Long rating = 46L;

    /**
     * 服务次数
     */
    @TableField("service_count")
    @ApiModelProperty("服务次数")
    private Long serviceCount;

    /**
     * 用户专属邀请码
     */
    @TableField("invite_code")
    @ApiModelProperty("用户专属邀请码")
    private String inviteCode;

    /**
     * 被邀请码
     */
    @ApiModelProperty("被邀请码")
    @TableField("was_invite_code")
    private String wasInviteCode;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private Long updateTime;

    /**
     * json格式数据
     */
    @TableField("ext")
    @ApiModelProperty("json格式数据")
    private String ext;

    /**
     * 备注
     */
    @TableField("remarks")
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 纬度（WGS84坐标系）
     */
    @TableField("latitude")
    @ApiModelProperty("纬度（WGS84坐标系）")
    private BigDecimal latitude;

    /**
     * 经度（WGS84坐标系）
     */
    @TableField("longitude")
    @ApiModelProperty("经度（WGS84坐标系）")
    private BigDecimal longitude;
}
