package com.tencent.wxcloudrun.model;

import cn.hutool.json.JSONUtil;

import java.util.List;

public class ImageItem {
    private String url;

    // 必须有无参构造器
    public ImageItem() {}

    // Getter & Setter
    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    // 完整反序列化方法
    public static List<ImageItem> parseList(String json) {
        return JSONUtil.parseArray(json).toList(ImageItem.class);
    }
}
