package com.tencent.wxcloudrun.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 试卷表实体类
 * 对应数据库表 pet_technology_exam_question
 */
@Data
@TableName("pet_technology_exam_question")
@ApiModel(value = "ExamQuestion对象", description = "试卷表")
public class ExamQuestion {

    /**
     * 主键ID，自增长
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID，自增长", example = "1")
    private Long id;

    /**
     * 题号
     */
    @TableField("number")
    @ApiModelProperty(value = "题号", example = "1")
    private Integer number;

    /**
     * 试卷类型，1单选 2多选 3判断 4主观题
     */
    @TableField("type")
    @ApiModelProperty(value = "试卷类型，1单选 2多选 3判断 4主观题", example = "1")
    private Integer type;

    /**
     * 试卷类型 1，猫 2 狗
     */
    @TableField("service_type")
    @ApiModelProperty(value = "试卷类型 1，猫 2 狗", example = "1")
    private Integer serviceType;

    /**
     * 题干
     */
    @TableField("content")
    @ApiModelProperty(value = "题干", example = "这是题目内容")
    private String content;

    /**
     * 选项(JSON格式: {"A":"选项内容",...})
     */
    @TableField("options_type")
    @ApiModelProperty(value = "选项(JSON格式: {\"A\":\"选项内容\",...})", example = "{\"A\":\"选项A\",\"B\":\"选项B\"}")
    private String optionsType;

    /**
     * 单选正确答案
     */
    @TableField("correct_answer")
    @ApiModelProperty(value = "单选正确答案", example = "A")
    private String correctAnswer;

    /**
     * 多选答案
     */
    @TableField("multi_correct_answer")
    @ApiModelProperty(value = "多选答案", example = "[\"A\",\"B\"]")
    private String multiCorrectAnswer;

    /**
     * 判断答案
     */
    @TableField("judgment_answer")
    @ApiModelProperty(value = "判断答案", example = "true")
    private String judgmentAnswer;

    /**
     * 主观题答案
     */
    @TableField("subjective_answer")
    @ApiModelProperty(value = "主观题答案", example = "这是主观题答案")
    private String subjectiveAnswer;

    /**
     * 本题分值
     */
    @TableField("standard_score")
    @ApiModelProperty(value = "本题分值", example = "10")
    private Integer standardScore;

    /**
     * 答案解析
     */
    @TableField("analysis")
    @ApiModelProperty(value = "答案解析", example = "这是答案解析")
    private String analysis;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @ApiModelProperty(value = "创建时间", example = "1672531200000")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @ApiModelProperty(value = "更新时间", example = "1672531200000")
    private Long updateTime;

    /**
     * 扩展字段，JSON格式数据
     */
    @TableField("ext")
    @ApiModelProperty(value = "扩展字段，JSON格式数据", example = "{\"key\":\"value\"}")
    private JsonNode ext;
}
