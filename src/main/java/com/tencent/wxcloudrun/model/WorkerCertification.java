package com.tencent.wxcloudrun.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data

@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("pet_technology_worker_certification")
@ApiModel(value = "喂养员认证类", description = "喂养员认证类")
public class WorkerCertification implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;
    @TableField("user_id")
    @ApiModelProperty("用户ID")
    private String userId;
    @ApiModelProperty("认证类型与试卷类型一致")
    @TableField("certification_type")
    private int certificationType;
    @ApiModelProperty("认证编号")
    @TableField("certification_number")
    private String certificationNumber;

    @TableField("create_time")
    private Long createTime;

    @TableField("update_time")
    private Long updateTime;
    @TableField("ext")
    private String ext;
}
