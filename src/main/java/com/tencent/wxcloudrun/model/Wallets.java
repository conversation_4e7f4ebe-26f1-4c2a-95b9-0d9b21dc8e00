package com.tencent.wxcloudrun.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
/**
 * <p>
 * 用户资金账户管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("pet_technology_wallets")
@ApiModel(value = "Wallets对象", description = "用户资金账户管理表")
public class Wallets implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @TableId(value = "id",type = IdType.AUTO)
    @ApiModelProperty("唯一标识")
    private String id;

    /**
     * 关联用户ID
     */
    @TableField("user_id")
    @ApiModelProperty("关联用户ID")
    private String userId;

    /**
     * 总订单单号
     */
    @TableField("order_no")
    @ApiModelProperty("总订单单号")
    private String orderNo;

    /**
     * 订单完成时间
     */
    @TableField("order_time")
    @ApiModelProperty("订单完成时间")
    private String orderTime;

    /**
     * 当前金额（分）
     */
    @ApiModelProperty("当前金额（分）")
    @TableField("current_amount")
    private String currentAmount;

    /**
     * 增加金额（分）
     */
    @TableField("add_amount")
    @ApiModelProperty("增加金额（分）")
    private String addAmount;

    /**
     * 减少金额（分）
     */
    @ApiModelProperty("减少金额（分）")
    @TableField("reduce_amount")
    private String reduceAmount;

    /**
     * 金额来源， 1订单2.打赏。3提现， 4退款
     */
    @TableField("amount_source")
    @ApiModelProperty("金额来源， 1订单2.打赏。3提现， 4退款")
    private String amountSource;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private String createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private String updateTime;

    /**
     * json格式数据
     */
    @TableField("ext")
    @ApiModelProperty("json格式数据")
    private String ext;
}
