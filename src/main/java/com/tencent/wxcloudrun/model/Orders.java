package com.tencent.wxcloudrun.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
/**
 * <p>
 * 订单全生命周期管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-08
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("pet_technology_orders")
@ApiModel(value = "Orders对象", description = "订单全生命周期管理表")
public class Orders implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长
     */
    @ApiModelProperty("自增长")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 子单业务号
     * 子单业务号为空表示母单
     * 子单业务号为1_1 1_2 表示是子单的第几个订单
     */
    @TableField("order_no")
    @ApiModelProperty("业务订单号（规则生成）")
    private String orderNo;

    /**
     * 订单状态
     */
    @TableField("orders_status")
    @ApiModelProperty("订单状态 ")
    private int ordersStatus;


    /**
     * 微信支付单号
     */
    @ApiModelProperty("微信支付单号")
    @TableField("wx_transaction_id")
    private String wxTransactionId;

    /**
     * 微信退款单号
     */
    @ApiModelProperty("微信退款单号")
    @TableField("wx_transaction_refund_id")
    private String wxTransactionRefundId;

    /**
     * 商户退款单号
     */
    @ApiModelProperty("商户退款单号")
    @TableField("merchant_transaction_refund_id")
    private String merchantTransactionRefundId;

    /**
     * 分账单号
     */
    @ApiModelProperty("分账单号")
    @TableField("out_order_no")
    private String outOrderNo;

    /**
     * 下单用户ID
     */
    @TableField("user_id")
    @ApiModelProperty("下单用户ID")
    private String userId;

    /**
     * 接单用户ID
     */
    @TableField("service_user_id")
    @ApiModelProperty("接单用户ID")
    private String serviceUserId;

    /**
     * 分销用户ID
     */
    @TableField("distribute_user_id")
    @ApiModelProperty("分销用户ID")
    private String distributeUserId;

    /**
     * 服务宠物ID,可能有多个
     */
    @TableField("pet_id")
    @ApiModelProperty("服务宠物ID,可能有多个")
    private long petId;

    /**
     * 服务地址ID
     */
    @TableField("address_id")
    @ApiModelProperty("服务地址ID")
    private long addressId;

    /**
     * 服务类型
     */
    @ApiModelProperty("服务类型")
    @TableField("service_type")
    private Integer serviceType;

    /**
     * 服务流程
     */
    @ApiModelProperty("服务流程")
    @TableField("service_flow")
    private String serviceFlow;

    /**
     * 订单原始价格
     */
    @ApiModelProperty("订单原始价格")
    @TableField("original_price")
    private Long originalPrice;

    /**
     * 实际支付价格
     */
    @TableField("final_price")
    @ApiModelProperty("实际支付价格")
    private Long finalPrice;

    /**
     * 喂养员到手价格
     */
    @TableField("service_price")
    @ApiModelProperty("喂养员价格")
    private Long servicePrice;

    /**
     * 分销到手价格
     */
    @TableField("distributor_price")
    @ApiModelProperty("推广员价格")
    private Long distributorPrice;

    /**
     * 服务时间
     */
    @ApiModelProperty("服务时间")
    @TableField("service_time")
    private String serviceTime;

    /**
     * 使用的优惠券ID
     */
    @TableField("coupon_ids")
    @ApiModelProperty("使用的优惠券ID")
    private String couponIds;

    @TableField("service_coupon_ids")
    @ApiModelProperty("接单员使用的优惠券ID")
    private String serviceCouponIds;

    /**
     * 交接方式 (0 钥匙, 1密码)
     */
    @TableField("handover_type")
    @ApiModelProperty("交接方式 (0 钥匙, 1密码)")
    private Integer handoverType;

    @TableField("handover")
    private String handover;

    /**
     * 订单备注
     */
    @TableField("remarks")
    @ApiModelProperty("订单备注")
    private String remarks;

    /**
     * 接单时间
     */
    @TableField("receive_time")
    @ApiModelProperty("接单时间")
    private Long receiveTime;

    /**
     * 接单时间
     */
    @TableField("begin_service_time")
    @ApiModelProperty("开始服务时间")
    private Long beginServiceTime;

    /**
     * 完单时间
     */
    @TableField("finish_time")
    @ApiModelProperty("完单时间")
    private Long finishTime;

    /**
     * 支付完成时间
     */
    @TableField("pay_time")
    @ApiModelProperty("支付完成时间")
    private Long payTime;

    /**
     * 退款时间
     */
    @ApiModelProperty("退款时间")
    @TableField("refund_time")
    private Long refundTime;


    /**
     * 饲养员对用户评价分
     */
    @ApiModelProperty("用户评价分")
    @TableField("user_score")
    private Integer userScore;

    /**
     * 用户对饲养员评价分
     */
    @ApiModelProperty("饲养员评价分")
    @TableField("service_user_score")
    private Integer serviceUserScore;

    /**
     * 用户对饲养员评价内容
     */
    @ApiModelProperty("饲养员评价内容")
    @TableField("service_user_review")
    private String serviceUserReview;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private Long updateTime;

    /**
     * 是否删除 0正常, 1删除
     */
    @TableField("is_delete")
    @ApiModelProperty("是否删除 0正常, 1删除")
    private Integer isDelete;
    /**
     * 提现状态 1 冻结状态， 2 提现完成
     */
    @TableField("withdrawal_status")
    @ApiModelProperty("提现状态")
    private Integer withdrawalStatus ;

    /**
     * 提现状态 1 冻结状态， 2 提现完成
     */
    @TableField("accounted_time")
    @ApiModelProperty("入账时间")
    private Long accountedTime ;
}
