package com.tencent.wxcloudrun.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("pet_technology_service_content")
@ApiModel(value = "ServiceContent对象", description = "")
public class ServiceContent implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    @TableField("date")
    private String date;

    /**
     * 喂养员id
     */
    @TableField("worker_id")
    @ApiModelProperty("喂养员id")
    private String workerId;

    /**
     * 业务订单号（规则生成）
     */
    @TableField("order_no")
    @ApiModelProperty("业务订单号（规则生成）")
    private String orderNo;

    @ApiModelProperty("服务类型")
    @TableField("service_type")
    private Integer serviceType;

    @TableField("service_content_status")
    @ApiModelProperty("服务状态(已上传 1, 审核通过 2，审核不通过 3)")
    private Integer serviceContentStatus = 1;

    @TableField(exist = false)
    @ApiModelProperty("服务状态(已上传 1, 审核通过 2，审核不通过 3)")
    private String serviceContentStatusValue;

    @ApiModelProperty("宠物照片")
    @TableField("pet_urls")
    private String petUrls;

    @TableField("door_urls")
    @ApiModelProperty("入门照片")
    private String doorUrls;

    @TableField("room_urls")
    @ApiModelProperty("房屋清洁照片")
    private String roomUrls;

    @TableField("eat_urls")
    @ApiModelProperty("喂食照片")
    private String eatUrls;

    @TableField("pet_emotion_desc")
    @ApiModelProperty("宠物精神情况")
    private String petEmotionDesc;

    @TableField("pet_health_desc")
    @ApiModelProperty("宠物健康情况")
    private String petHealthDesc;

    @TableField("pet_eat_desc")
    @ApiModelProperty("宠物进食情况")
    private String petEatDesc;

    @TableField("remark")
    @ApiModelProperty("备注")
    private String remark;

    @TableField("environment")
    @ApiModelProperty("环境检查")
    private String environment;

    @TableField("security")
    @ApiModelProperty("安全检查")
    private String security;

    @TableField("partner")
    @ApiModelProperty("陪玩")
    private String partner;

    @TableField("create_time")
    @ApiModelProperty("创建时间")
    private long createTime;

    @TableField("update_time")
    @ApiModelProperty("更新时间")
    private long updateTime;

    @TableField("ext")
    private String ext;
}
