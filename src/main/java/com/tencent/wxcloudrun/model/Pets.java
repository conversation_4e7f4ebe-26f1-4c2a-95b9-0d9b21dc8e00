package com.tencent.wxcloudrun.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
/**
 * <p>
 * 宠物详细信息档案
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("pet_technology_pets")
@ApiModel(value = "Pets对象", description = "宠物详细信息档案")
public class Pets implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 宠物唯一标识
     */
    @TableId(value = "id",type = IdType.AUTO)
    @ApiModelProperty("宠物唯一标识")
    private Long id;

    /**
     * 所属用户ID
     */
    @TableField("user_id")
    @ApiModelProperty("所属用户ID")
    private String userId;

    /**
     * 宠物名称
     */
    @TableField("name")
    @ApiModelProperty("宠物名称")
    private String name;

    /**
     * 宠物类型
     */
    @TableField("pet_type")
    @ApiModelProperty("宠物类型")
    private String petType;

    /**
     * 年龄（单位：岁）
     */
    @TableField("age")
    @ApiModelProperty("年龄（单位：岁）")
    private String age;

    @TableField("pets_datetime")
    @ApiModelProperty("生日")
    private String petsDatetime;

    /**
     * 性格
     */
    @ApiModelProperty("性格")
    @TableField("personality")
    private String personality;

    /**
     * 性别
     */
    @TableField("gender")
    @ApiModelProperty("性别")
    private String gender;

    /**
     * 是否绝育（0否 1是）
     */
    @TableField("is_neutered")
    @ApiModelProperty("是否绝育（0否 1是）")
    private Integer isNeutered;

    /**
     * 是否疫苗（0否 1是）
     */
    @TableField("is_vaccine")
    @ApiModelProperty("是否疫苗（0否 1是）")
    private Integer isVaccine;

    /**
     * 是否健康（0否 1是）
     */
    @TableField("is_healthy")
    @ApiModelProperty("是否健康（0否 1是）")
    private Integer isHealthy;

    /**
     * 健康状态备注
     */
    @ApiModelProperty("健康状态备注")
    @TableField("healthy_remarks")
    private String healthyRemarks;

    /**
     * 宠物照片 可以是多个根据逗号分割
     */
    @TableField("pets_url")
    @ApiModelProperty("宠物照片 可以是多个根据逗号分割")
    private String petsUrl;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private Long updateTime;

    /**
     * 备注
     */
    @TableField("remarks")
    @ApiModelProperty("备注")
    private String remarks;
}
