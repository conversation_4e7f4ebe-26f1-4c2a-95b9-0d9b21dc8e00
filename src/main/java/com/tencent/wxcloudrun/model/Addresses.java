package com.tencent.wxcloudrun.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
/**
 * <p>
 * 用户地理位置数据存储
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("pet_technology_addresses")
@ApiModel(value = "Addresses对象", description = "用户地理位置数据存储")
public class Addresses implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 地址唯一标识
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty("地址唯一标识")
    private Long id;

    /**
     * 关联用户ID
     */
    @TableField("user_id")
    @ApiModelProperty("关联用户ID")
    private String userId;

    /**
     * 地址别名（如：公司/家）
     */
    @TableField("alias")
    @ApiModelProperty("地址别名（如：公司/家）")
    private String alias;

    /**
     * 地址类型,1 用户地址2，喂养员地址
     */
    @TableField("addresses_type")
    @ApiModelProperty("地址类型,1 用户地址2，喂养员地址")
    private String addressesType;

    /**
     * 联系人姓名
     */
    @ApiModelProperty("联系人姓名")
    @TableField("contact_name")
    private String contactName;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    @TableField("contact_phone")
    private String contactPhone;

    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    @TableField("full_address")
    private String fullAddress;

    /**
     * 省
     */
    @ApiModelProperty("省")
    @TableField("province")
    private String province;

    /**
     * 市
     */
    @TableField("city")
    @ApiModelProperty("市")
    private String city;

    /**
     * 区
     */
    @ApiModelProperty("区")
    @TableField("district")
    private String district;

    /**
     * 纬度（WGS84坐标系）
     */
    @TableField("latitude")
    @ApiModelProperty("纬度（WGS84坐标系）")
    private BigDecimal latitude;

    /**
     * 经度（WGS84坐标系）
     */
    @TableField("longitude")
    @ApiModelProperty("经度（WGS84坐标系）")
    private BigDecimal longitude;

    /**
     * 是否默认地址（0否 1是）
     */
    @TableField("is_default")
    @ApiModelProperty("是否默认地址（0否 1是）")
    private Boolean isDefault;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private Long updateTime;

}
