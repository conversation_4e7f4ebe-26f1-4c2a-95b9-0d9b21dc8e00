package com.tencent.wxcloudrun.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 微信会话码表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("pet_wx_session_code")
@ApiModel(value = "WxSessionCode对象", description = "微信会话码表")
@Builder
public class WxSessionCode implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * UUID主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty("UUID主键")
    private Long id;

    /**
     * 微信openid
     */
    @TableField("openid")
    @ApiModelProperty("微信openid")
    private String openid;

    /**
     * 微信unionid
     */
    @TableField("unionid")
    @ApiModelProperty("微信unionid")
    private String unionid;

    /**
     * 会话密钥
     */
    @TableField("session_key")
    @ApiModelProperty("会话密钥")
    private String sessionKey;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private Long updateTime;

    /**
     * 安装时间
     */
    @ApiModelProperty("安装时间")
    @TableField("install_time")
    private Long installTime;
}
