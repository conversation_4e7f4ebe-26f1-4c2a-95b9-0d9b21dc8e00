package com.tencent.wxcloudrun.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.wxcloudrun.model.UserWallet;
import com.tencent.wxcloudrun.model.WxUsers;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 钱包
 * </p>
 */
@Mapper
public interface UserWalletMapper extends BaseMapper<UserWallet> {


    @Select("SELECT * FROM pet_technology_user_wallet WHERE user_id = #{userId} FOR UPDATE")
    UserWallet selectForUpdate(@Param("userId") String userId);

}

