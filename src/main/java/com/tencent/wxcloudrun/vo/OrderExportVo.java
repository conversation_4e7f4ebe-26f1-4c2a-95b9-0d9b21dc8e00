package com.tencent.wxcloudrun.vo;

import com.tencent.wxcloudrun.config.ExcelColumn;
import com.tencent.wxcloudrun.enums.OrderStatusEnum;
import com.tencent.wxcloudrun.model.Orders;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrderExportVo {

    // 基础信息
    @ExcelColumn(name = "订单号", order = 0, width = 25)
    private String orderNo;

    @ExcelColumn(name = "主订单号", order = 1, width = 25)
    private String parentOrderNo;

    @ExcelColumn(name = "订单状态", order = 2)
    private String orderStatus;

    // 金额信息（单位：元）
    @ExcelColumn(name = "订单金额（元）", order = 3)
    private Long originalPrice;

    @ExcelColumn(name = "实付金额（元）", order = 4)
    private Long finalPrice;

    @ExcelColumn(name = "服务费（元）", order = 5)
    private Long servicePrice;

    @ExcelColumn(name = "分销佣金（元）", order = 6)
    private Long distributorPrice;

    // 用户信息
    @ExcelColumn(name = "用户ID", order = 7, width = 20)
    private String userId;

    @ExcelColumn(name = "服务人员ID", order = 8, width = 20)
    private String serviceUserId;

    @ExcelColumn(name = "分销员ID", order = 9, width = 20)
    private String distributeUserId;

    // 时间信息
    @ExcelColumn(name = "创建时间", isTimestamp = true, order = 10, width = 20, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Long createTime;

    @ExcelColumn(name = "支付时间", isTimestamp = true, order = 11, width = 20, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Long payTime;

    @ExcelColumn(name = "完成时间", isTimestamp = true, order = 12, width = 20, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Long finishTime;

    // 业务信息
    @ExcelColumn(name = "服务类型", order = 13)
    private String serviceType;

    @ExcelColumn(name = "服务宠物", order = 14)
    private String petInfo;

    @ExcelColumn(name = "服务地址", order = 15, width = 30)
    private String addressInfo;

    @ExcelColumn(name = "提现状态", order = 16)
    private String withdrawalStatus;

    // 转换方法
    public static OrderExportVo fromOrder(Orders order) {
        OrderExportVo vo = new OrderExportVo();
        // 基础信息
        vo.setOrderNo(order.getOrderNo());
        vo.setParentOrderNo(order.getOrderNo());
        vo.setOrderStatus(OrderStatusEnum.find(order.getOrdersStatus()));

        // 金额转换（分转元）
        vo.setOriginalPrice(order.getOriginalPrice());
        vo.setFinalPrice(order.getFinalPrice());
        vo.setServicePrice(order.getServicePrice());
        vo.setDistributorPrice(order.getDistributorPrice());

        // 用户信息
        vo.setUserId(order.getUserId());
        vo.setServiceUserId(order.getServiceUserId());
        vo.setDistributeUserId(order.getDistributeUserId());

        // 时间信息
        vo.setCreateTime(order.getCreateTime());
        vo.setPayTime(order.getPayTime());
        vo.setFinishTime(order.getFinishTime());

        return vo;
    }
}