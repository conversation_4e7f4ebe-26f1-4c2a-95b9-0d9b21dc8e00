package com.tencent.wxcloudrun.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @ClassName CouponsVO
 * @Description
 * <AUTHOR>
 * @Version V1.0
 **/
@Getter
@Setter
@ToString
@Accessors(chain = true)
@ApiModel(value = "更新用户试卷")
public class UserSheetUpdateVO {

   @ApiModelProperty("喂养员id")
   private String serviceUserId;

   @ApiModelProperty("试卷类型")
   private int serviceType;

   @ApiModelProperty("状态 1-已提交 2-通过 3-不通过")
   private int status;

   @ApiModelProperty("打分")
   private List<UserSheetSubmitVo.Sheet> sheets;
}
