package com.tencent.wxcloudrun.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "用户订单对象")
public class WorkerOrdersVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 业务订单号（规则生成）
     */
    @ApiModelProperty("业务订单号")
    private String orderNo;

    /**
     * 微信支付单号
     */
    @ApiModelProperty("微信支付单号")
    private String wxTransactionId;

    /**
     * 下单用户ID
     */
    @ApiModelProperty("下单用户ID")
    private String userId;

    /**
     * 服务宠物ID,可能有多个
     */
    @ApiModelProperty("服务宠物ID,可能有多个")
    private String petId;

    /**
     * 服务地址ID
     */
    @ApiModelProperty("服务地址ID")
    private String addressId;

    /**
     * 服务类型
     */
    @ApiModelProperty("服务类型")
    private String serviceType;

    /**
     * 服务流程
     */
    @ApiModelProperty("服务流程")
    private String serviceFlow;

    /**
     * 订单原始价格
     */
    @ApiModelProperty("订单原始价格")
    private Long originalPrice;

    /**
     * 实际支付价格
     */
    @ApiModelProperty("实际支付价格")
    private Long finalPrice;

    /**
     * 服务时间
     */
    @ApiModelProperty("服务时间")
    private String serviceTime;

    /**
     * 使用的优惠券ID
     */
    @ApiModelProperty("使用的优惠券ID")
    private String couponId;

    /**
     * 交接方式 (0 钥匙, 1密码)
     */
    @ApiModelProperty("交接方式 (0 钥匙, 1密码)")
    private Integer handoverType;

    /**
     * 交接方式详细
     */
    @ApiModelProperty("交接方式详细")
    private String handover;

    /**
     * 订单备注
     */
    @ApiModelProperty("订单备注")
    private String remarks;

    /**
     * 支付完成时间
     */
    @ApiModelProperty("支付完成时间")
    private Long payTime;

    /**
     * 退款时间
     */
    @ApiModelProperty("退款时间")
    private Long refundTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Long createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Long updateTime;

    /**
     * 是否删除 0正常, 1删除
     */
    @ApiModelProperty("是否删除 0正常, 1删除")
    private Integer isDelete;

    @ApiModelProperty("订单状态 ")
    private String ordersStatus;
}
