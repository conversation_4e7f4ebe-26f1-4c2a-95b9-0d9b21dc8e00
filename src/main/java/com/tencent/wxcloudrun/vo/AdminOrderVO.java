package com.tencent.wxcloudrun.vo;

import com.tencent.wxcloudrun.dto.CouponsDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AdminOrderVO {

    @Data
    @ApiModel(value = "后台订单查询")
    public static class OrderPage extends PageSize {
        // 喂养员ID
        @ApiModelProperty("喂养员ID")
        private String serviceUserId;
        // 用户ID
        @ApiModelProperty("用户ID")
        private String userId;
        @ApiModelProperty("用户ID")
        private List<String> userIdList;
        // 手机号
        @ApiModelProperty("手机号")
        private String mobile;
        // 订单状态
        @ApiModelProperty("订单状态")
        private List<Integer> orderStatus;
        // 订单类型
        @ApiModelProperty("订单类型")
        private List<Integer> serviceTypes;
        // 提现状态 1 冻结 2 结算
        @ApiModelProperty("提现状态 1 冻结 2 结算")
        private Integer withdrawalStatus;
        @ApiModelProperty("分销用户ID")
        private String distributeUserId;

        @ApiModelProperty("用户昵称")
        private String nickName;
        @ApiModelProperty("订单号")
        private String orderNo;

        @ApiModelProperty("未接单订单")
        private boolean orderFlag = false;

    }


    @Data
    @ApiModel(value = "后台订单退款")
    public static class OrderCancel{

        @ApiModelProperty("订单号")
        private String orderNo;
        @ApiModelProperty("true 全款退款")
        private boolean cancelFlag =false;

    }

    @Data
    @ApiModel(value = "后台订单更换喂养员")
    public static class OrderServiceUser{

        @ApiModelProperty("订单号")
        private String orderNo;

        @ApiModelProperty("喂养员ID")
        private String serviceUserId;
    }
}
