package com.tencent.wxcloudrun.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @ClassName CouponsVO
 * @Description
 * <AUTHOR>
 * @Version V1.0
 **/
@Getter
@Setter
@ToString
@Accessors(chain = true)
@ApiModel(value = "优惠券VO")
public class CouponsVO {
   /**
    * 自增长
    */
   private Long id;
   
   /**
    * 优惠券编号
    */
   @ApiModelProperty("优惠券编号")
   private String number;
   
   /**
    * 名称
    */
   @ApiModelProperty("名称")
   private String name;
   /**
    * 优惠券类型(0 金额券, 1 折扣券)
    */
   @ApiModelProperty("优惠类型  1 打折2 满减")
   private Integer couponType;
   
   /**
    * 优惠金额/折扣率
    */
   @ApiModelProperty("优惠金额/折扣率")
   private int discount;

   /**
    * 生效时间
    */
   @ApiModelProperty("生效时间")
   private String validStart;
   
   /**
    * 过期时间
    */
   @ApiModelProperty("过期时间")
   private String validEnd;
   
   /**
    * 创建时间
    */
   @ApiModelProperty("创建时间")
   private String createTime;
   
   /**
    * 更新时间
    */
   @ApiModelProperty("更新时间")
   private String updateTime;
   
   @ApiModelProperty("所属用户ID")
   private String userId;
   
   @ApiModelProperty("使用状态（0未使用 1已使用）")
   private Integer isUsed;
   
}
