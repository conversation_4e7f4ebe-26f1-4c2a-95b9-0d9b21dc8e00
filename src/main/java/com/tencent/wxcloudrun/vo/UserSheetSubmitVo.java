package com.tencent.wxcloudrun.vo;

import com.tencent.wxcloudrun.dto.ExamQuestionDTO;
import com.tencent.wxcloudrun.model.ExamQuestion;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class UserSheetSubmitVo {

    @ApiModelProperty("喂养员id")
    private String serviceUserId;

    @ApiModelProperty("试卷类型")
    private int serviceType;

    @ApiModelProperty("试卷id")
    private long sheetId;

    @ApiModelProperty("提交时间")
    private long createTime ;

    @ApiModelProperty("试卷")
    private List<Sheet> sheetAnswers;

    @Data
    public static class Sheet extends ExamQuestionDTO.Base {
        @ApiModelProperty("题号")
        private Integer number;
        @ApiModelProperty("类型 1单选 2多选 3判断 4主观题")
        private Integer type;
        @ApiModelProperty("单选答案")
        private String singleChoose;
        @ApiModelProperty("多选答案")
        private List<String> multipleChoose;
        @ApiModelProperty("判断题答案")
        private String judgment;
        @ApiModelProperty("主观题答案")
        private String subjective;
        @ApiModelProperty("小分")
        private int score;
    }
}
