package com.tencent.wxcloudrun.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @ClassName CouponsVO
 * @Description
 * <AUTHOR>
 * @Version V1.0
 **/
@Getter
@Setter
@ToString
@Accessors(chain = true)
@ApiModel(value = "服务报告")
public class ServiceContentUpdateVO {

   /**
    * 业务订单号（规则生成）
    */
   @ApiModelProperty("业务订单号（规则生成）")
   private String orderNo;

   @ApiModelProperty("服务状态(2 审核通过 ，3 审核不通过 )")
   private Integer serviceContentStatus = 1;
}
