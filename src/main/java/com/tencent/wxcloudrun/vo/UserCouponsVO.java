package com.tencent.wxcloudrun.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.google.gson.annotations.SerializedName;
import com.tencent.wxcloudrun.model.Coupons;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 优惠券表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@ApiModel(value = "Coupons对象", description = "优惠券表")
public class UserCouponsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 优惠券id
     */
    @TableField("coupons_id")
    @ApiModelProperty("优惠券id")
    private Long couponsId;


    @ApiModelProperty("user_id")
    private String openId;

    /**
     * 优惠类型
     */
    @ApiModelProperty("优惠类型")
    @TableField("coupon_type")
    private int couponType;

    /**
     * 优惠券名称
     */
    @TableField("title")
    @ApiModelProperty("优惠券名称")
    private String title;

    /**
     * 生效时间
     */
    @ApiModelProperty("生效时间")
    @TableField("valid_start")
    private Long validStart;

    /**
     * 过期时间
     */
    @TableField("valid_end")
    @ApiModelProperty("过期时间")
    private Long validEnd;




    private String ext;



}
