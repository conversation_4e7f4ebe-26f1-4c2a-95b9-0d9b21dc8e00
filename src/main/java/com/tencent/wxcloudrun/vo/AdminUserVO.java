package com.tencent.wxcloudrun.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AdminUserVO {

    @Data
    @ApiModel(value = "后台用户查询")
    public static class UserPage extends PageSize {
        // 用户ID
        @ApiModelProperty("用户ID")
        private String userId;
        // 手机号
        @ApiModelProperty("手机号")
        private String mobile;

        @ApiModelProperty("用户名称")
        private String nickName;

        @ApiModelProperty("认证类型")
        private Integer serviceType;

        @ApiModelProperty("用户ID列表")
        private List<String> userIdList;

    }


}
