package com.tencent.wxcloudrun.vo;

import com.tencent.wxcloudrun.model.Addresses;
import com.tencent.wxcloudrun.model.Orders;
import com.tencent.wxcloudrun.model.Pets;
import com.tencent.wxcloudrun.model.ServiceContent;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Builder
@Data
public class OrderWarp {
    private Orders order;
    private Pets pets;
    private Addresses addresses;
    private List<ServiceContent> serviceReport;


}
