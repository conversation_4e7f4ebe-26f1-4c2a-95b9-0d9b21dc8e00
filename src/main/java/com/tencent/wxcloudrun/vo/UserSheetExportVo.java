package com.tencent.wxcloudrun.vo;

import com.tencent.wxcloudrun.config.ExcelColumn;
import lombok.Data;

@Data
public class UserSheetExportVo {

    // 喂养员信息
    @ExcelColumn(name = "喂养员ID", order = 0)
    private String serviceUserId;

    @ExcelColumn(name = "试卷ID", order = 1)
    private Long sheetId;

    @ExcelColumn(name = "试卷类型", order = 2)
    private Integer serviceType;

    // 题目信息
    @ExcelColumn(name = "题号", order = 3)
    private Integer number;

    @ExcelColumn(name = "题型", order = 4)
    private String typeStr; // 转换后的类型描述

    @ExcelColumn(name = "题目内容", order = 5, width = 40)
    private String content;

    @ExcelColumn(name = "单选答案", order = 6)
    private String singleChoose;

    @ExcelColumn(name = "多选答案", order = 7)
    private String multipleChoose;

    @ExcelColumn(name = "判断答案", order = 8)
    private String judgment;

    @ExcelColumn(name = "主观答案", order = 9, width = 50)
    private String subjective;

    @ExcelColumn(name = "该题分数", order = 10)
    private Integer score;

    @ExcelColumn(name = "用户分数", order = 11)
    private Integer userScore;

    // 省略getter/setter
}