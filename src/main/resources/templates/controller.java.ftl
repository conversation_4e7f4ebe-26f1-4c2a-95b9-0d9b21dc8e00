package com.tencent.wxcloudrun.controller;

import com.tencent.wxcloudrun.config.ApiResponse;
import com.tencent.wxcloudrun.model.${entity};
import com.tencent.wxcloudrun.service.api.I${entity}Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/${entity}")
@Api(tags = "${entity}管理接口", description = "提供${entity}相关的增删改查服务")
public class ${entity}Controller {
springboot-wxcloudrun-1.0.jar
    @Autowired
    private I${entity}Service service;

    @GetMapping("/id/{id}")
    @ApiOperation(value = "获取${entity}", notes = "获取${entity}")
    public ApiResponse getObjectById(@PathVariable Long id) {
        return ApiResponse.ok(service.getById(id));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获取${entity}列表", notes = "分页查询所有${entity}信息")
    public ApiResponse list() {
        return ApiResponse.ok(service.list());
    }

    @PostMapping("/save")
    @ApiOperation(value = "新增${entity}", notes = "保存新的${entity}记录")
    public ApiResponse save(@RequestBody @ApiParam(value = "${entity}对象", required = true) ${entity} entity) {
        return ApiResponse.ok(service.save(entity));
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除${entity}", notes = "根据ID删除指定${entity}记录")
    public ApiResponse delete(@PathVariable Long id) {
        return ApiResponse.ok(service.removeById(id));
}

    @PutMapping("/update")
    @ApiOperation(value = "更新${entity}", notes = "根据ID修改${entity}记录")
    public ApiResponse update(@RequestBody @ApiParam(value = "${entity}对象", required = true) ${entity} entity) {
        return ApiResponse.ok(service.updateById(entity));
    }
}