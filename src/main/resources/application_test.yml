server:
  port: 8080

wx:
  appid: wxc89654a50513fe85 #微信小程序的appid
  secret: 097b4502f5d80989c107f9f42cd6af75 #微信小程序的Secret
  env: prod-7g0sy825f9a0d50e
  subMchId: 097b4502f5d80989c107f9f42cd6af75
  pay_ip: *************
  merchantId: 190000
  privateKeyPath: /Users/<USER>/your/path/apiclient_key.pem
  merchantSerialNumber: 5157F09EFDC096DE15EBE81A47057A72
  apiV3key: 1232321321
  prod_env: test
#spring:
#  datasource:
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: jdbc:mysql://${MYSQL_ADDRESS}/${MYSQL_DATABASE:springboot_demo}
#    username: ${MYSQL_USERNAME}
#    password: ${MySQL_PASSWORD}
TENCENT:
  SECRET_ID: AKIDfUKzRghV8mdQ2UjneJky0rVxRmtqHbnm
  SECRET_KEY: sc9XjiV8Zq9HJ9C7qYBfw0qUu76va2GG
  REGION: faceid.tencentcloudapi.com
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************
    username: root
    password: <EMAIL>
  mvc:
    view:
      suffix: .html
      prefix: /

mybatis:
  mapper-locations: classpath*:mapper/*Mapper.xml

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

logging:
  level:
    com.course.server.mapper: trace