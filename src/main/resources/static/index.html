<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <link rel="shortcut icon"
    href="https://static-index-4gtuqm3bfa95c963-1304825656.tcloudbaseapp.com/official-website/favicon.svg"
    mce_href="https://static-index-4gtuqm3bfa95c963-1304825656.tcloudbaseapp.com/official-website/favicon.svg"
    type="image/x-icon" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"
    integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous" />
  <title>欢迎使用微信云托管</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Droid Sans", "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", "Droid Sans Fallback", "Microsoft YaHei", sans-serif;
    }

    .title-logo {
      width: 80px;
      height: 80px;
    }

    .container {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    @media screen and (min-width:1200px) {
      body .container {
        margin-top: 160px;
        margin-bottom: 272px;
      }
    }

    @media screen and (max-width:1919px) {
      body .container {
        margin-top: 76px;
        margin-bottom: 128px;
      }
    }

    .container {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .count-button {
      width: 320px;
      height: 40px;
      box-sizing: border-box;
      margin: 16px 8px;
      font-weight: 500;
      font-size: 17px;
      color: #FFFFFF;
      letter-spacing: 1px;
      text-align: center;
      border-radius: 4px;
    }

    .count-number {
      align-self: flex-start;
      opacity: 0.9;
      font-weight: 500;
      font-size: 14px;
      color: #000000;
    }

    .count-reset {
      color: #576b95;
      cursor: pointer;
      margin-left: auto;
    }

    .count-text {
      width: 320px;
      height: 40px;
      padding: 0 12px;
      margin: 0 auto;
      line-height: 40px;
      text-align: left;
      display: flex;
      border: 1px solid rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      opacity: 0.9;
      box-sizing: border-box;
      font-weight: 400;
      font-size: 14px;
      color: #000000;
    }

    .quote {
      font-size: 12px;
    }

    .qrcode {
      height: 144px;
      width: 144px;
      display: block;
      margin: 0 auto;
    }

    .title {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      width: 320px;
    }

    .title-text {
      width: 320px;
      height: 48px;
      text-align: center;
      margin-top: 16px;
      font-size: 32px;
      opacity: 0.9;
      font-weight: 500;
      font-size: 32px;
      color: #000000;
      letter-spacing: 0;
      line-height: 48px;
    }

    .counter-container {
      margin-top: 48px;
    }

    .hr {
      text-align: center;
      position: relative;
      z-index: 2;
      height: 20px;
      line-height: 20px;
    }

    .hr::after {
      position: absolute;
      content: '';
      top: 10px;
      left: 60px;
      width: 200px;
      border-bottom: 1px solid rgba(0, 0, 0, .1);
      z-index: -1;
    }

    .hr small {
      display: inline-block;
      background-color: white;
    }

    .hr-text {
      display: inline-block;
      height: 20px;
      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.55);
      letter-spacing: 0;
      text-align: center;
      z-index: 2;
      background-color: white;
      padding: 0 8px;
      line-height: 20px;
    }

    .link-button {
      width: 154px;
      height: 48px;
      box-sizing: border-box;
      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.90);
      letter-spacing: 0;
      text-align: center;
      background: rgba(0, 0, 0, 0.03);
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .card {
      margin-top: 16px;
      width: 320px;
      height: 200px;
      box-sizing: border-box;
      border: 1px solid rgba(0, 0, 0, 0.10);
      border-radius: 5.71px;
      position: relative;
    }

    .card-text {
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.55);
      letter-spacing: 0;
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="title">
      <img class="title-logo"
        src="https://static-index-4gtuqm3bfa95c963-1304825656.tcloudbaseapp.com/official-website/favicon.svg" />
      <div style="display: inline; margin-bottom: 48px" class="title-text">欢迎使用微信云托管</div>
    </div>
    <div style="text-align: center; display: flex; flex-direction: column; align-items: center; margin: 0">
      <span class="count-text">
        <span style="margin-right: 16px;">当前计数:</span>
        <span class="count-number"></span>
        <span class="count-reset" onclick="set('clear')">清零</span>
      </span>
      <a class="btn btn-success btn-lg count-button" style="background: #07c160; border: 0"
        onclick="set('inc')">计数+1</a>
      <div class="card" style="width: 320px; margin-bottom: 48px;">
        <div class="card-body">
          <img class="qrcode middle"
            src="https://qcloudimg.tencent-cloud.cn/raw/89b46988d3cd73d8a56e76a1b82bb377.png" />
          <small class="card-text" style="display: flex; justify-content: center; margin-top: 8px">扫码加入微信云托管用户群</small>
        </div>
      </div>
      <div>
        <div class="hr">
          <span class="hr-text">快速入门</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-top: 16px;">
          <a class="btn btn-light btn-lg link-button" style="border: 0; box-shadow: none; margin-right: 12px;"
            href="https://developers.weixin.qq.com/miniprogram/dev/wxcloudrun/src/basic/intro.html" target="_blank"
            rel="noopener noreferrer">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px"
              height="24px" viewBox="0 0 24 24">
              <title>icons_outline_warrant copy</title>
              <g id="icons_outline_warrant-copy" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <g id="0.&#x56FE;&#x6807;/&#x7EBF;&#x578B;/icons_outlined_copy">
                  <rect id="Rectangle" x="0" y="0" width="20" height="20"></rect>
                  <path
                    d="M13.9979043,2 L20,8 L20,21.0013542 C20,21.5542301 19.5541613,22 19.0041915,22 L5.99580851,22 C5.44892021,22 5,21.552891 5,21.0013542 L5,2.99864581 C5,2.44576991 5.44583866,2 5.99580851,2 L13.9979043,2 Z M12.399,3.2 L6.2,3.2 L6.2,20.8 L18.8,20.8 L18.8,9.6 L14,9.6 C13.1163444,9.6 12.4,8.8836556 12.4,8 L12.399,3.2 Z M14.2,14.4 L14.2,15.6 L7.8,15.6 L7.8,14.4 L14.2,14.4 Z M17.2,12 L17.2,13.2 L7.8,13.2 L7.8,12 L17.2,12 Z M13.599,3.299 L13.6,8 C13.6,8.2209139 13.7790861,8.4 14,8.4 L18.703,8.4 L13.599,3.299 Z"
                    id="Combined-Shape" fill-opacity="0.9" fill="#000000"></path>
                </g>
              </g>
            </svg>
            <span style="margin-left: 4px">开发者文档</span></a>
          <a class="btn btn-light btn-lg link-button" style="border: 0; box-shadow: none;"
            href="https://developers.weixin.qq.com/community/business/course/00068c2c0106c0667f5b01d015b80d"
            target="_blank" rel="noopener noreferrer">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px"
              height="24px" viewBox="0 0 24 24">
              <title>icons_outlined_play</title>
              <g id="&#x573A;&#x666F;&#x5316;&#x90E8;&#x7F72;" stroke="none" stroke-width="1" fill="none"
                fill-rule="evenodd">
                <g id="icons_outlined_play">
                  <rect id="Rectangle-664" x="0" y="0" width="20" height="20"></rect>
                  <path
                    d="M12,20.8 C16.8601058,20.8 20.8,16.8601058 20.8,12 C20.8,7.1398942 16.8601058,3.2 12,3.2 C7.1398942,3.2 3.2,7.1398942 3.2,12 C3.2,16.8601058 7.1398942,20.8 12,20.8 Z M12,22 C6.4771525,22 2,17.5228475 2,12 C2,6.4771525 6.4771525,2 12,2 C17.5228475,2 22,6.4771525 22,12 C22,17.5228475 17.5228475,22 12,22 Z M10.7,14.8349028 L15.2358445,12 L10.7,9.16509717 L10.7,14.8349028 Z M10.2649995,7.47812467 L16.8216014,11.5760008 C17.0557696,11.722356 17.1269562,12.0308312 16.980601,12.2649995 C16.9403607,12.329384 16.8859859,12.3837588 16.8216014,12.4239992 L10.2649995,16.5218753 C10.0308312,16.6682305 9.72235601,16.5970439 9.57600085,16.3628756 C9.52633472,16.2834098 9.5,16.191586 9.5,16.0978762 L9.5,7.90212382 C9.5,7.62598145 9.72385763,7.40212382 10,7.40212382 C10.0937099,7.40212382 10.1855337,7.42845854 10.2649995,7.47812467 Z"
                    id="Oval-78" fill-opacity="0.9" fill="#000000"></path>
                </g>
              </g>
            </svg>
            <span style="margin-left: 4px">视频教程</span></a>
        </div>
      </div>
    </div>
  </div>
</body>
<script src="https://code.jquery.com/jquery-1.12.4.min.js" integrity="sha256-ZosEbRLbNQzLpnKIkEdrPv7lOy9C27hHQ+Xp8a4MxAQ=" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"
  integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p" crossorigin="anonymous"></script>
<script>
  init();
  function init() {
    $.ajax("/api/count", {
      method: "get",
    }).done(function (res) {
      if (res && res.data !== undefined) {
        $(".count-number").html(res.data);
      }
    });
  }
  function set(action) {
    $.ajax("/api/count", {
      method: "POST",
      contentType: "application/json; charset=utf-8",
      dataType: "json",
      data: JSON.stringify({
        action: action,
      }),
    }).done(function (res) {
      if (res && res.data !== undefined) {
        $(".count-number").html(res.data);
      }
    });
  }
</script>

</html>
