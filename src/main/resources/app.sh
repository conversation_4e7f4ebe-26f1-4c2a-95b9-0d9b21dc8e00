#!/bin/bash

# Spring Boot应用管理脚本
# 使用方法: ./app.sh [start|stop|restart|status|logs]

APP_NAME="springboot-wxcloudrun-1.0"
APP_JAR="/app/${APP_NAME}.jar"
APP_LOG_DIR="/app/logs/${APP_NAME}"
PID_FILE="/app/logs/${APP_NAME}.pid"
JVM_OPTS="-Xms768m -Xmx1536m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/${APP_NAME}/heapdump.hprof"
JAVA_OPTS="${JVM_OPTS} -Dlogging.file.name=${APP_LOG_DIR}/app.log -Dspring.profiles.active=prod"

# 创建日志目录
mkdir -p "${APP_LOG_DIR}"
chmod 755 "${APP_LOG_DIR}"

# 检查应用状态
check_status() {
    if [ -f "${PID_FILE}" ]; then
        PID=$(cat "${PID_FILE}")
        if ps -p "${PID}" > /dev/null; then
            return 0
        else
            rm -f "${PID_FILE}"
            return 1
        fi
    else
        return 1
    fi
}

# 启动应用
start() {
    check_status
    if [ $? -eq 0 ]; then
        echo "应用已在运行中，PID: $(cat ${PID_FILE})"
        return 1
    fi

    echo "正在启动应用..."
    nohup java ${JAVA_OPTS} -jar "${APP_JAR}" > /dev/null 2>&1 &
    echo $! > "${PID_FILE}"

    sleep 3
    check_status
    if [ $? -eq 0 ]; then
        echo "应用启动成功，PID: $(cat ${PID_FILE})"
        echo "日志路径: ${APP_LOG_DIR}/app.log"
        return 0
    else
        echo "应用启动失败"
        return 1
    fi
}

# 停止应用
stop() {
    check_status
    if [ $? -ne 0 ]; then
        echo "应用未运行"
        return 0
    fi

    echo "正在停止应用..."
    PID=$(cat "${PID_FILE}")
    kill "${PID}"

    sleep 3
    if ps -p "${PID}" > /dev/null; then
        echo "应用未停止，正在强制终止..."
        kill -9 "${PID}"
        sleep 2
    fi

    rm -f "${PID_FILE}"
    echo "应用已停止"
    return 0
}

# 重启应用
restart() {
    stop
    start
}

# 查看应用状态
status() {
    check_status
    if [ $? -eq 0 ]; then
        echo "应用正在运行中，PID: $(cat ${PID_FILE})"
    else
        echo "应用未运行"
    fi
}

# 查看日志
logs() {
    if [ ! -f "${APP_LOG_DIR}/app.log" ]; then
        echo "日志文件不存在"
        return 1
    fi

    tail -f "${APP_LOG_DIR}/app.log"
}

# 主函数
case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs
        ;;
    *)
        echo "用法: $0 [start|stop|restart|status|logs]"
        exit 1
        ;;
esac

exit 0