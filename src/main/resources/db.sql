-- 用户核心表（整合微信数据）
CREATE TABLE pet_technology_wx_users
(
    id              BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增长',
    openid          VARCHAR(255) UNIQUE NOT NULL COMMENT '微信openid（加密存储）',
    nickname        VARCHAR(255) COMMENT '昵称',
    real_name       VARCHAR(255) COMMENT '真实姓名',
    identity_id     VARCHAR(255) COMMENT '身份证ID',
    identity_url    JSON COMMENT '身份证URL',
    gender          VARCHAR(255) COMMENT '性别',
    custom_url      VARCHAR(512) COMMENT '自定义头像URL',
    mobile          VARCHAR(20) COMMENT '绑定手机号',
    is_certified    INT DEFAULT 0 COMMENT '喂养员认证（0未认证 1已认证）',
    introduction    VARCHAR(512) COMMENT '个人介绍',
    auth_time       LONG COMMENT '认证时间',
    rating          LONG COMMENT '综合评价分（0.00-5.00）在完成订单后进行更新',
    invite_code     VARCHAR(255) COMMENT '用户专属邀请码',
    was_invite_code VARCHAR(255) COMMENT '被邀请码',
    create_time     LONG COMMENT '创建时间',
    update_time     LONG COMMENT '更新时间',
    ext             JSON COMMENT 'json格式数据',
    remarks         VARCHAR(512) COMMENT '备注'
) COMMENT '用户基础信息与微信数据融合表';
CREATE INDEX idx_invite_code ON pet_technology_wx_users (invite_code);
CREATE INDEX idx_was_invite_code ON pet_technology_wx_users (was_invite_code);
-- 宠物档案表（弱关联用户）
CREATE TABLE pet_technology_pets
(
    id              BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增长',
    user_id         VARCHAR(255) NOT NULL COMMENT '所属用户ID',
    name            VARCHAR(255) NOT NULL COMMENT '宠物名称',
    pet_type        VARCHAR(255) NOT NULL COMMENT '宠物类型',
    age             VARCHAR(255) COMMENT '年龄（单位：岁）',
    personality     VARCHAR(255) COMMENT '性格',
    gender          VARCHAR(255) COMMENT '性别',
    is_neutered     INT DEFAULT 0 COMMENT '是否绝育（0否 1是）',
    is_vaccine      INT DEFAULT 0 COMMENT '是否疫苗（0否 1是）',
    is_healthy      INT DEFAULT 0 COMMENT '是否健康（0否 1是）',
    healthy_remarks VARCHAR(512) COMMENT '健康状态备注',
    pets_url        JSON COMMENT '宠物照片 可以是多个根据逗号分割',
    create_time     LONG COMMENT '创建时间',
    update_time     LONG COMMENT '更新时间',
    ext             JSON COMMENT 'json格式数据',
    remarks         VARCHAR(512) COMMENT '备注'
) COMMENT '宠物详细信息档案';
-- 基础用户查询索引
CREATE INDEX idx_pet_tech_user ON pet_technology_pets (user_id);
-- 类型筛选索引
CREATE INDEX idx_pet_tech_type ON pet_technology_pets (pet_type);


-- 地理围栏表（含坐标）
CREATE TABLE pet_technology_addresses
(
    id             BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增长',
    user_id        VARCHAR(255)   NOT NULL COMMENT '关联用户ID',
    alias          VARCHAR(255) COMMENT '地址别名（如：公司/家）',
    addresses_type VARCHAR(255)   NOT NULL COMMENT '地址类型,1 用户地址2，喂养员地址',
    contact_name   VARCHAR(255)   NOT NULL COMMENT '联系人姓名',
    contact_phone  VARCHAR(255)   NOT NULL COMMENT '联系电话',
    full_address   VARCHAR(255)   NOT NULL COMMENT '详细地址',
    province       VARCHAR(255)   NOT NULL COMMENT '省',
    city           VARCHAR(255)   NOT NULL COMMENT '市',
    district       VARCHAR(255)   NOT NULL COMMENT '区',
    latitude       DECIMAL(10, 8) NOT NULL COMMENT '纬度（WGS84坐标系）',
    longitude      DECIMAL(11, 8) NOT NULL COMMENT '经度（WGS84坐标系）',
    is_default     TINYINT(1) DEFAULT 0 COMMENT '是否默认地址（0否 1是）',
    create_time    LONG COMMENT '创建时间',
    update_time    LONG COMMENT '更新时间',
    ext            JSON COMMENT 'json格式数据'
) COMMENT '用户地理位置数据存储';
-- 用户维度查询
CREATE INDEX idx_addr_user ON pet_technology_addresses (user_id);
-- 用户+类型复合索引
CREATE INDEX idx_addr_user_type ON pet_technology_addresses (user_id, addresses_type);


-- 优惠策略表
CREATE TABLE pet_technology_coupons
(
    id          BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增长',
    coupon_type VARCHAR(255) NOT NULL COMMENT '优惠类型',
    title       VARCHAR(255) NOT NULL COMMENT '优惠券名称',
    discount    INT          NOT NULL COMMENT '折扣率折扣几几折',
    coupons_num INT          NOT NULL COMMENT '数量',
    valid_start LONG         NOT NULL COMMENT '生效时间',
    valid_end   LONG         NOT NULL COMMENT '过期时间',
    create_time LONG COMMENT '创建时间',
    update_time LONG COMMENT '更新时间',
    ext         JSON COMMENT 'json格式数据'
) COMMENT '优惠券表';

-- 标题搜索索引
CREATE INDEX idx_coupon_title ON pet_technology_coupons (title);
-- 折扣率筛选索引
CREATE INDEX idx_coupon_discount ON pet_technology_coupons (discount);

-- 领取优惠券
CREATE TABLE pet_technology_receive_coupons
(
    id          BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增长',
    user_id     VARCHAR(255) NOT NULL COMMENT '所属用户ID',
    discount    LONG         NOT NULL COMMENT '优惠金额/折扣率',
    valid_start LONG         NOT NULL COMMENT '生效时间',
    valid_end   LONG         NOT NULL COMMENT '过期时间',
    is_used     INT DEFAULT 0 COMMENT '使用状态（0未使用 1已使用）',
    create_time LONG COMMENT '创建时间',
    update_time LONG COMMENT '更新时间',
    ext         JSON COMMENT 'json格式数据'
) COMMENT '用户优惠券管理表';
-- 核心用户查询索引
CREATE INDEX idx_receive_user ON pet_technology_receive_coupons (user_id);
-- 用户优惠券状态管理
CREATE INDEX idx_receive_user_status ON pet_technology_receive_coupons (user_id, is_used);


-- 订单核心表（含支付数据）
CREATE TABLE pet_technology_orders
(
    id                BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增长',
    order_no          VARCHAR(255) NOT NULL COMMENT '业务订单号（规则生成）',
    wx_transaction_id VARCHAR(255) COMMENT '微信支付单号',
    user_id           VARCHAR(255) NOT NULL COMMENT '下单用户ID',
    pet_id            JSON         NOT NULL COMMENT '服务宠物ID,可能有多个',
    address_id        VARCHAR(255) NOT NULL COMMENT '服务地址ID',
    service_type      VARCHAR(255) NOT NULL COMMENT '服务类型',
    service_flow      JSON         NOT NULL COMMENT '服务流程',
    original_price    LONG         NOT NULL COMMENT '订单原始价格',
    final_price       LONG         NOT NULL COMMENT '实际支付价格',
    coupon_id         VARCHAR(255) COMMENT '使用的优惠券ID',
    handover          VARCHAR(512) COMMENT '交接方式',
    remarks           VARCHAR(512) COMMENT '订单备注',
    pay_time          LONG COMMENT '支付完成时间',
    refund_time       LONG COMMENT '退款时间',
    create_time       LONG COMMENT '创建时间',
    ext               JSON COMMENT 'json格式数据',
    update_time       LONG COMMENT '更新时间'
) COMMENT '订单全生命周期管理表';
-- 用户维度索引
ALTER TABLE pet_technology_orders
    ADD INDEX idx_order_user (user_id);
-- 订单号唯一索引（业务主键）
ALTER TABLE pet_technology_orders
    ADD UNIQUE INDEX idx_order_no (order_no);
-- 微信支付单号索引
ALTER TABLE pet_technology_orders
    ADD INDEX idx_wx_payment (wx_transaction_id);


-- 订单子表
CREATE TABLE pet_technology_orders_child
(
    id                 BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增长',
    order_no           VARCHAR(255) NOT NULL COMMENT '业务订单号（规则生成）',
    child_order_no     VARCHAR(255) NOT NULL COMMENT '子订单号（规则生成）',
    wx_transaction_id  VARCHAR(255) COMMENT '总微信支付单号',
    user_id            VARCHAR(255) NOT NULL COMMENT '下单用户ID',
    caretakers_id      VARCHAR(255) NOT NULL COMMENT '喂养员ID',
    pet_id             JSON         NOT NULL COMMENT '服务宠物ID,可能有多个',
    address_id         VARCHAR(255) NOT NULL COMMENT '服务地址ID',
    service_type       VARCHAR(255) NOT NULL COMMENT '服务类型',
    service_flow       JSON         NOT NULL COMMENT '服务流程',
    original_price     LONG         NOT NULL COMMENT '子订单价格',
    final_price        LONG         NOT NULL COMMENT '子订单支付价格',
    status             INT COMMENT ' 待付款, 待接单, 待服务 ,进行中, 已完成 待评价 已取消， 申请退款，退款完成， 不够在加',
    handover           VARCHAR(512) COMMENT '交接方式',
    remarks            VARCHAR(512) COMMENT '订单备注',
    caretakers_process JSON COMMENT '服务过程图片，多张图片，和文字描述',
    evaluate_notes     JSON COMMENT '评价，用户和喂养员评价,文字描述',
    pay_time           LONG COMMENT '支付完成时间',
    refund_time        LONG COMMENT '退款时间',
    service_time       LONG COMMENT '预约服务时间',
    service_start_time LONG COMMENT '服务实际开始时间',
    service_end_time   LONG COMMENT '服务实际结束时间',
    create_time        LONG COMMENT '创建时间',
    ext                JSON COMMENT 'json格式数据',
    update_time        LONG COMMENT '更新时间'
) COMMENT '订单子单';

ALTER TABLE pet_technology_orders_child
    ADD INDEX idx_order_no (order_no);
ALTER TABLE pet_technology_orders_child
    ADD INDEX idx_wx_transaction_id (wx_transaction_id);
ALTER TABLE pet_technology_orders_child
    ADD INDEX idx_user_id (user_id);
ALTER TABLE pet_technology_orders_child
    ADD INDEX idx_caretakers_id (caretakers_id);
ALTER TABLE pet_technology_orders_child
    ADD INDEX idx_status (status);


-- 喂养员服务类型流程
CREATE TABLE pet_technology_caretakers_flow
(
    id             BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增长',
    user_id        VARCHAR(255) UNIQUE NOT NULL COMMENT '关联用户ID',
    service_radius INT COMMENT '基础服务半径（单位：公里）',
    base_price     LONG                NOT NULL COMMENT '基础服务价格',
    increase_price LONG                NOT NULL COMMENT '增加价格，每一公里增加X元',
    type           VARCHAR(255) COMMENT '服务类型',
    flow           JSON COMMENT '服务流程',
    create_time    LONG COMMENT '创建时间',
    update_time    LONG COMMENT '更新时间',
    ext            JSON COMMENT 'json格式数据'
) COMMENT '喂养员服务类型流程';
ALTER TABLE pet_technology_caretakers_flow
    ADD INDEX idx_service_radius (service_radius);

-- 钱包明细表
CREATE TABLE pet_technology_wallets
(
    id             BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增长',
    user_id        VARCHAR(255) UNIQUE NOT NULL COMMENT '关联用户ID',
    order_no       VARCHAR(255) COMMENT '总订单单号',
    order_time     Long COMMENT '订单完成时间',
    current_amount LONG COMMENT '当前金额（分）',
    add_amount     LONG COMMENT '增加金额（分）',
    reduce_amount  LONG COMMENT '减少金额（分）',
    amount_source  VARCHAR(255) COMMENT '金额来源， 1订单2.打赏。3提现， 4退款',
    create_time    LONG COMMENT '创建时间',
    update_time    LONG COMMENT '更新时间',
    ext            JSON COMMENT 'json格式数据'
) COMMENT '用户资金账户管理表';


ALTER TABLE pet_technology_wx_users ADD COLUMN service_type int COMMENT '服务类型';
ALTER TABLE pet_technology_wx_users
    ADD COLUMN service_count BIGINT DEFAULT 0 COMMENT '服务次数';
ALTER TABLE pet_technology_wx_users
    ADD COLUMN latitude DECIMAL(10, 8) DEFAULT NULL COMMENT '纬度（WGS84坐标系）';

ALTER TABLE pet_technology_wx_users
    ADD COLUMN longitude DECIMAL(11, 8) DEFAULT NULL COMMENT '经度（WGS84坐标系）';

ALTER TABLE pet_technology_orders
    ADD COLUMN `wx_transaction_refund_id` varchar(255) DEFAULT NULL COMMENT '微信退款单号',
    ADD COLUMN `out_order_no` varchar(255) DEFAULT NULL COMMENT '分帐单号',
    ADD COLUMN `merchant_transaction_refund_id` varchar(255) DEFAULT NULL COMMENT '商户退款单号',
    ADD COLUMN `service_user_id` varchar(255) DEFAULT NULL COMMENT '接单用户ID',
    ADD COLUMN `distribute_user_id` varchar(255) DEFAULT NULL COMMENT '分销用户ID',
    ADD COLUMN `service_price` bigint DEFAULT NULL COMMENT '喂养员价格',
    ADD COLUMN `distributor_price` bigint DEFAULT NULL COMMENT '推广员价格',
    ADD COLUMN `receive_time` bigint DEFAULT NULL COMMENT '接单时间',
    ADD COLUMN `begin_service_time` bigint DEFAULT NULL COMMENT '开始服务时间',
    ADD COLUMN `finish_time` bigint DEFAULT NULL COMMENT '完单时间',
    ADD COLUMN `user_score` int DEFAULT NULL COMMENT '用户评价分',
    ADD COLUMN `service_user_score` int DEFAULT NULL COMMENT '饲养员评价分',
    ADD COLUMN `service_user_review` text COMMENT '饲养员评价内容';

CREATE TABLE pet_technology_worker_certification (
              id BIGINT AUTO_INCREMENT PRIMARY KEY,
              user_id  VARCHAR(255) NOT NULL COMMENT '关联用户ID',
              certification_type int NOT NULL COMMENT '认证类型',
              certification_number VARCHAR(255) NOT NULL COMMENT '认证编号',
              create_time    LONG COMMENT '创建时间',
              update_time    LONG COMMENT '更新时间',
              ext            JSON COMMENT 'json格式数据'
) COMMENT '喂养员认证类型表';


CREATE TABLE pet_technology_service_content (
                                                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID，自增',
                                                date VARCHAR(255) COMMENT '日期',
                                                worker_id VARCHAR(255) COMMENT '喂养员ID',
                                                order_no VARCHAR(255) COMMENT '业务订单号（规则生成）',
                                                service_type INT COMMENT '服务类型',
                                                service_content_status INT DEFAULT 1 COMMENT '服务状态(已上传 1, 审核通过 2，审核不通过 3)',
                                                pet_urls VARCHAR(255) COMMENT '宠物照片',
                                                door_urls VARCHAR(255) COMMENT '入门照片',
                                                room_urls VARCHAR(255) COMMENT '房屋清洁照片',
                                                eat_urls VARCHAR(255) COMMENT '喂食照片',
                                                pet_emotion_desc VARCHAR(255) COMMENT '宠物精神情况',
                                                pet_health_desc VARCHAR(255) COMMENT '宠物健康情况',
                                                pet_eat_desc VARCHAR(255) COMMENT '宠物进食情况',
                                                remark VARCHAR(255) COMMENT '备注',
                                                environment VARCHAR(255) COMMENT '环境检查',
                                                security VARCHAR(255) COMMENT '安全检查',
                                                partner VARCHAR(255) COMMENT '陪玩',
                                                create_time BIGINT COMMENT '创建时间',
                                                update_time BIGINT COMMENT '更新时间',
                                                ext            JSON COMMENT 'json格式数据'
) COMMENT='服务内容表';

-- 添加 worker_id 索引
CREATE INDEX idx_worker_id ON pet_technology_service_content (worker_id);

-- 添加 order_no 索引
CREATE INDEX idx_order_no ON pet_technology_service_content (order_no);

CREATE TABLE pet_technology_user_sheet (
                                           id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '地址唯一标识',
                                           user_id VARCHAR(255) COMMENT '关联用户ID',
                                           score INT COMMENT '得分',
                                           status INT COMMENT '状态 1-已提交 2-通过 3-不通过',
                                           service_type INT COMMENT '服务类型',
                                           sheetId BIGINT COMMENT '试卷id',
                                           answer TEXT COMMENT '答卷',
                                           create_time BIGINT COMMENT '创建时间',
                                           update_time BIGINT COMMENT '更新时间',
                                           ext TEXT COMMENT '扩展字段'
) COMMENT='用户考试试卷';

-- 索引
CREATE INDEX idx_user_id ON pet_technology_user_sheet (user_id);

ALTER TABLE pet_technology_orders
    ADD COLUMN `out_order_no` varchar(255) DEFAULT NULL COMMENT '分帐单号';

-- 试卷表
CREATE TABLE pet_technology_exam_question
(
    id             BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增长',
    number  INT  NOT NULL COMMENT '题号',
    type  INT  NOT NULL COMMENT '试卷类型，1单选 2多选 3判断 4主观题',
    service_type  INT  NOT NULL COMMENT '试卷类型  1， 猫 2 狗',
    content        TEXT NOT NULL COMMENT '题干',
    options_type        varchar(512) COMMENT '选项(JSON格式: {"A":"选项内容",...})',
    correct_answer varchar(255) NOT NULL COMMENT '单选正确答案',
    multi_correct_answer varchar(255) NOT NULL COMMENT '多选答案',
    judgment_answer varchar(255) NOT NULL COMMENT '判断答案',
    subjective_answer varchar(255) NOT NULL COMMENT '主观题答案',
    standard_score          INT  NOT NULL COMMENT '本题分值',
    analysis       TEXT COMMENT '答案解析',
    create_time    LONG COMMENT '创建时间',
    update_time    LONG COMMENT '更新时间',
    ext            JSON COMMENT 'json格式数据'
) COMMENT '试卷库';

-- 索引
CREATE INDEX idx_service_type ON pet_technology_exam_question (service_type);


ALTER TABLE pet_technology_orders
    ADD COLUMN `withdrawal_status` INT DEFAULT 0 COMMENT '提现状态，默认0 未提现，1已提现';




-- 钱包
CREATE TABLE pet_technology_user_wallet
(
    id             BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增长',
    user_id  varchar(255)  NOT NULL COMMENT '用户openid',
    total_amount  LONG  NOT NULL COMMENT '总金额',
    withdrawn_amount  LONG  NOT NULL COMMENT '已提现金额',
    release_amount  LONG  NOT NULL COMMENT '可提现金额',
    freeze_amount  LONG  NOT NULL COMMENT '冻结金额',
    create_time    LONG COMMENT '创建时间',
    update_time    LONG COMMENT '更新时间',
    ext            JSON COMMENT 'json格式数据'
) COMMENT '钱包';



-- 钱包提现记录
CREATE TABLE pet_technology_user_withdrawn_record
(
    id             BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增长',
    user_id  varchar(255)  NOT NULL COMMENT '用户openid',
    out_bill_no  varchar(255)   NOT NULL COMMENT '提现订单编号',
    transfer_bill_no  varchar(255)   NOT NULL COMMENT '微信转账单号',
    withdrawn_amount  INT  NOT NULL COMMENT '提现金额',
    create_time    LONG COMMENT '创建时间',
    update_time    LONG COMMENT '更新时间',
    ext            JSON COMMENT 'json格式数据'
) COMMENT '钱包';


ALTER TABLE pet_technology_orders
    ADD COLUMN `accounted_time` LONG COMMENT '到账时间';



ALTER TABLE pet_technology_coupons
    ADD COLUMN `enable` INT default 0 COMMENT '展示状态 0 展示 1 不展示';


ALTER TABLE pet_technology_coupons
    ADD COLUMN `effective_time` INT default 30 COMMENT '有效时间（天）';
ALTER TABLE pet_technology_coupons
    ADD COLUMN `enable_time` LONG COMMENT '展示时间';

ALTER TABLE pet_technology_coupons
    ADD COLUMN `user_type` INT default 1 COMMENT '1用户侧  2 喂养员侧';

ALTER TABLE pet_technology_orders
    ADD COLUMN `service_coupon_ids` varchar(255)   COMMENT '喂养员优惠券';
ALTER TABLE pet_technology_orders
    ADD COLUMN `coupon_ids` varchar(255)   COMMENT '用户优惠券列表';