# searchServiceUsers 排序优化说明

## 问题描述

原来的 `searchServiceUsers` 方法实现有一个逻辑问题：
1. 先使用 MyBatis-Plus 进行分页查询
2. 然后对查询结果进行排序

这种方式会导致排序不准确，因为只对当前页的数据进行排序，而不是对全部数据排序后再分页。

## 解决方案

重新实现了 `searchServiceUsers` 方法，采用 **先排序，再分页** 的正确逻辑：

### 修改后的流程

1. **获取认证用户列表**
   ```java
   List<WorkerCertification> usersWorker = workerCertificationService.workerServiceType(dto.getServiceType());
   ```

2. **获取所有符合条件的用户（不分页）**
   ```java
   List<WxUsers> allUsers = usersService.lambdaQuery()
           .in(WxUsers::getOpenid, userIdList)
           .list(); // 注意：这里使用 list() 而不是 page()
   ```

3. **先进行排序**
   ```java
   List<WxUsers> sortedUsers;
   if (dto.isOrderByDistance()) {
       // 按距离升序排序
       sortedUsers = allUsers.stream().sorted(Comparator.comparingLong(value -> 
           AddressUtil.calDistance(value.getLatitude(), value.getLongitude(), dto.getLatitude(), dto.getLongitude())
       )).collect(Collectors.toList());
   } else {
       // 按评分倒序排序
       sortedUsers = allUsers.stream().sorted(Comparator.comparingLong(WxUsers::getRating).reversed()).collect(Collectors.toList());
   }
   ```

4. **手动分页**
   ```java
   long current = dto.getCursorId();
   long size = dto.getPageSize();
   long total = sortedUsers.size();
   
   int start = (int) ((current - 1) * size);
   int end = (int) Math.min(start + size, total);
   
   List<WxUsers> pagedUsers = sortedUsers.subList(start, end);
   ```

5. **转换为返回对象并构建分页结果**

## 关键改进

### 1. 排序准确性
- **修改前**：只对当前页数据排序，结果不准确
- **修改后**：对全部数据排序后再分页，结果准确

### 2. 分页逻辑
- **修改前**：使用 MyBatis-Plus 的 `page()` 方法
- **修改后**：先查询全部数据，然后手动分页

### 3. 性能考虑
- **优点**：排序结果准确，符合业务需求
- **注意**：对于大数据量，需要考虑性能优化

## 使用示例

### 按距离排序
```json
{
  "serviceType": 1,
  "latitude": 39.9042,
  "longitude": 116.4074,
  "orderByDistance": true,
  "pageSize": 10,
  "cursorId": 1
}
```

### 按评分排序
```json
{
  "serviceType": 1,
  "latitude": 39.9042,
  "longitude": 116.4074,
  "orderByDistance": false,
  "pageSize": 10,
  "cursorId": 1
}
```

## 响应示例

```json
{
  "code": 0,
  "errorMsg": "",
  "data": {
    "records": [
      [
        {
          "serviceUserName": "张三",
          "serviceUserId": "user123",
          "serviceUserUrl": "avatar.jpg",
          "serviceUserScore": 95,
          "serviceUserDistance": "1200",
          "serviceUserCount": 50
        },
        // ... 更多用户，按距离或评分正确排序
      ]
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

## 性能优化建议

### 1. 数据量控制
- 如果认证用户数量很大，可以考虑在数据库层面进行初步过滤
- 可以添加地理位置索引来优化距离计算

### 2. 缓存优化
- 可以考虑缓存认证用户列表，减少重复查询
- 对于评分排序，可以考虑缓存排序结果

### 3. 分页优化
- 对于距离排序，可以考虑使用地理位置数据库功能
- 可以实现更智能的分页策略

## 注意事项

1. **数据一致性**：确保排序逻辑与业务需求一致
2. **性能监控**：监控大数据量情况下的性能表现
3. **错误处理**：处理地理位置计算可能出现的异常
4. **参数验证**：确保经纬度参数的有效性

## 总结

这次修改解决了排序不准确的问题，确保了：
- 按距离排序时，返回的是全局最近的用户
- 按评分排序时，返回的是全局评分最高的用户
- 分页结果的准确性和一致性

虽然在性能上可能有一定影响（需要查询全部数据），但保证了业务逻辑的正确性。在实际使用中，可以根据数据量和性能要求进行进一步优化。
