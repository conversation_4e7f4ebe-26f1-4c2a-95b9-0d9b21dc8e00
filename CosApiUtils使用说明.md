# CosApiUtils 腾讯云COS工具类使用说明

## 概述

CosApiUtils 是基于腾讯云COS SDK封装的工具类，提供了文件上传、下载、删除、生成URL等常用功能。

## 配置

### 1. 依赖配置

项目已包含腾讯云COS依赖：
```xml
<dependency>
    <groupId>com.qcloud</groupId>
    <artifactId>cos_api</artifactId>
    <version>5.6.227</version>
</dependency>
```

### 2. 配置文件

在 `application.yml` 中配置：
```yaml
TENCENT:
  SECRET_ID: your_secret_id
  SECRET_KEY: your_secret_key
  COS:
    REGION: ap-beijing          # COS地域
    BUCKET_NAME: your-bucket    # 存储桶名称
```

## 功能说明

### 1. 文件上传

#### 1.1 上传MultipartFile
```java
@Autowired
private CosApiUtils cosApiUtils;

// 上传文件，自动生成文件名
String fileUrl = cosApiUtils.uploadFile(multipartFile, null);

// 上传文件，指定文件名
String fileUrl = cosApiUtils.uploadFile(multipartFile, "custom-name.jpg");

// 上传到指定文件夹
String fileName = cosApiUtils.generateFileNameWithPath("image.jpg", "images");
String fileUrl = cosApiUtils.uploadFile(multipartFile, fileName);
```

#### 1.2 上传字节数组
```java
byte[] fileBytes = getFileBytes();
String fileUrl = cosApiUtils.uploadFile(fileBytes, "file.pdf");
```

### 2. 文件下载

#### 2.1 下载到本地文件
```java
boolean success = cosApiUtils.downloadFile("remote-file.jpg", "/local/path/file.jpg");
```

#### 2.2 下载为字节数组
```java
byte[] fileBytes = cosApiUtils.downloadFileAsBytes("remote-file.jpg");
```

### 3. 文件删除

#### 3.1 删除单个文件
```java
boolean success = cosApiUtils.deleteFile("file-to-delete.jpg");
```

#### 3.2 批量删除文件
```java
String[] fileNames = {"file1.jpg", "file2.pdf", "file3.txt"};
DeleteObjectsResult result = cosApiUtils.batchDeleteFiles(fileNames);
```

### 4. URL生成

#### 4.1 生成永久访问URL
```java
String publicUrl = cosApiUtils.getFileUrl("file.jpg");
// 返回: https://bucket-name.cos.region.myqcloud.com/file.jpg
```

#### 4.2 生成预签名URL（临时访问）
```java
// 生成1小时有效的预签名URL
long oneHour = 60 * 60 * 1000;
String presignedUrl = cosApiUtils.generatePresignedUrl("private-file.jpg", oneHour);
```

### 5. 文件信息

#### 5.1 检查文件是否存在
```java
boolean exists = cosApiUtils.doesFileExist("file.jpg");
```

#### 5.2 获取文件元数据
```java
ObjectMetadata metadata = cosApiUtils.getFileMetadata("file.jpg");
long fileSize = metadata.getContentLength();
Date lastModified = metadata.getLastModified();
String contentType = metadata.getContentType();
```

## 使用示例

### 完整的文件上传示例

```java
@RestController
@RequestMapping("/api/file")
public class FileController {

    @Autowired
    private CosApiUtils cosApiUtils;

    @PostMapping("/upload")
    public ApiResponse<String> uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return ApiResponse.error("文件不能为空");
            }

            // 生成带路径的文件名
            String fileName = cosApiUtils.generateFileNameWithPath(
                file.getOriginalFilename(), "uploads");

            // 上传文件
            String fileUrl = cosApiUtils.uploadFile(file, fileName);

            return ApiResponse.ok(fileUrl);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return ApiResponse.error("文件上传失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/delete")
    public ApiResponse<Boolean> deleteFile(@RequestParam("fileName") String fileName) {
        try {
            boolean success = cosApiUtils.deleteFile(fileName);
            return ApiResponse.ok(success);
        } catch (Exception e) {
            log.error("文件删除失败", e);
            return ApiResponse.error("文件删除失败: " + e.getMessage());
        }
    }

    @GetMapping("/url")
    public ApiResponse<String> getFileUrl(@RequestParam("fileName") String fileName) {
        try {
            // 检查文件是否存在
            if (!cosApiUtils.doesFileExist(fileName)) {
                return ApiResponse.error("文件不存在");
            }

            String fileUrl = cosApiUtils.getFileUrl(fileName);
            return ApiResponse.ok(fileUrl);
        } catch (Exception e) {
            log.error("获取文件URL失败", e);
            return ApiResponse.error("获取文件URL失败: " + e.getMessage());
        }
    }
}
```

## 注意事项

1. **配置安全性**: 请妥善保管SECRET_ID和SECRET_KEY，不要提交到代码仓库
2. **文件命名**: 建议使用工具类提供的文件名生成方法，避免文件名冲突
3. **异常处理**: 所有方法都会抛出运行时异常，请做好异常处理
4. **资源管理**: 工具类会自动管理COS客户端的生命周期
5. **权限配置**: 确保COS存储桶的访问权限配置正确

## 常见问题

### Q: 如何配置不同环境的存储桶？
A: 在不同的配置文件中设置不同的BUCKET_NAME值：
- application-dev.yml (开发环境)
- application-prod.yml (生产环境)

### Q: 如何实现文件的分类存储？
A: 使用generateFileNameWithPath方法，按类型创建不同的文件夹：
```java
String imageFileName = cosApiUtils.generateFileNameWithPath("photo.jpg", "images");
String documentFileName = cosApiUtils.generateFileNameWithPath("doc.pdf", "documents");
```

### Q: 如何处理大文件上传？
A: 对于大文件，建议：
1. 在前端实现分片上传
2. 使用COS的分片上传API
3. 设置合适的超时时间

### Q: 如何实现文件的访问控制？
A: 可以通过以下方式：
1. 设置存储桶为私有，使用预签名URL
2. 在应用层实现权限验证
3. 使用COS的访问策略功能
